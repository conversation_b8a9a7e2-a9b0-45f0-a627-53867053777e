<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$step = $_GET['step'] ?? 1;
$message = '';

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($_POST['action']) {
        case 'step1':
            $site_url = trim($_POST['site_url']);
            if (!empty($site_url)) {
                header("Location: postback_wizard.php?step=2&site_url=" . urlencode($site_url));
                exit;
            } else {
                $message = '<div class="alert alert-error">❌ يرجى إدخال رابط الموقع!</div>';
            }
            break;
            
        case 'step2':
            $site_url = $_POST['site_url'];
            $postback_type = $_POST['postback_type'];
            $password = $_POST['password'] ?? '';
            header("Location: postback_wizard.php?step=3&site_url=" . urlencode($site_url) . "&type=" . urlencode($postback_type) . "&password=" . urlencode($password));
            exit;
            break;
    }
}

// الحصول على المعاملات من URL
$site_url = $_GET['site_url'] ?? '';
$postback_type = $_GET['type'] ?? '';
$password = $_GET['password'] ?? '';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معالج إعداد Postback - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .wizard-header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .wizard-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .progress-bar {
            display: flex;
            justify-content: center;
            margin: 1rem 0;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .step-circle.active {
            background: #667eea;
            color: white;
        }
        
        .step-circle.completed {
            background: #28a745;
            color: white;
        }
        
        .step-line {
            width: 50px;
            height: 2px;
            background: #ddd;
        }
        
        .step-line.completed {
            background: #28a745;
        }
        
        .wizard-content {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .step-title {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .radio-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .radio-option {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .radio-option:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }
        
        .radio-option input[type="radio"] {
            margin-left: 0.5rem;
        }
        
        .radio-option.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 1rem 0;
            word-break: break-all;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .checklist {
            list-style: none;
            padding: 0;
        }
        
        .checklist li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .checklist li:before {
            content: "✅ ";
            margin-left: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .progress-bar {
                flex-direction: column;
                align-items: center;
            }
            
            .progress-step {
                margin: 0.5rem 0;
            }
            
            .step-line {
                width: 2px;
                height: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="wizard-header">
            <h1>🧙‍♂️ معالج إعداد Postback لـ CPALead</h1>
            <p>إعداد سهل وسريع خطوة بخطوة</p>
            
            <!-- شريط التقدم -->
            <div class="progress-bar">
                <div class="progress-step">
                    <div class="step-circle <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                    <span>معلومات الموقع</span>
                </div>
                <div class="step-line <?php echo $step > 1 ? 'completed' : ''; ?>"></div>
                <div class="progress-step">
                    <div class="step-circle <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                    <span>نوع Postback</span>
                </div>
                <div class="step-line <?php echo $step > 2 ? 'completed' : ''; ?>"></div>
                <div class="progress-step">
                    <div class="step-circle <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
                    <span>النتائج</span>
                </div>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="wizard-content">
            <?php if ($step == 1): ?>
                <!-- الخطوة 1: معلومات الموقع -->
                <h3 class="step-title">الخطوة 1: معلومات الموقع</h3>
                
                <form method="POST">
                    <input type="hidden" name="action" value="step1">
                    
                    <div class="form-group">
                        <label for="site_url">🌐 رابط موقعك:</label>
                        <input type="url" id="site_url" name="site_url" 
                               placeholder="https://yoursite.com" 
                               value="<?php echo htmlspecialchars($site_url); ?>" required>
                    </div>
                    
                    <div class="info-box">
                        <h4>📋 نصائح مهمة:</h4>
                        <ul>
                            <li>تأكد من أن الرابط صحيح ويعمل</li>
                            <li>لا تضع "/" في نهاية الرابط</li>
                            <li>استخدم https إذا كان متوفراً</li>
                        </ul>
                    </div>
                    
                    <button type="submit" class="btn">التالي ➡️</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- الخطوة 2: نوع Postback -->
                <h3 class="step-title">الخطوة 2: اختيار نوع Postback</h3>
                
                <form method="POST">
                    <input type="hidden" name="action" value="step2">
                    <input type="hidden" name="site_url" value="<?php echo htmlspecialchars($site_url); ?>">
                    
                    <div class="radio-group">
                        <label class="radio-option">
                            <input type="radio" name="postback_type" value="basic" checked>
                            <div>
                                <h4>🎯 Postback أساسي (موصى به للمبتدئين)</h4>
                                <p>يحتوي على المعاملات الأساسية: subid, ip_address, payout</p>
                                <small>سهل الإعداد وكافي لمعظم الاستخدامات</small>
                            </div>
                        </label>
                        
                        <label class="radio-option">
                            <input type="radio" name="postback_type" value="advanced">
                            <div>
                                <h4>🚀 Postback متقدم (جميع المعاملات)</h4>
                                <p>يحتوي على جميع معاملات CPALead المتاحة</p>
                                <small>للمستخدمين المتقدمين الذين يريدون تتبع مفصل</small>
                            </div>
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">🔒 كلمة مرور Postback (اختياري):</label>
                        <input type="text" id="password" name="password" 
                               placeholder="كلمة مرور للأمان الإضافي">
                    </div>
                    
                    <div class="info-box">
                        <h4>💡 نصيحة:</h4>
                        <p>ابدأ بـ Postback الأساسي، يمكنك ترقيته لاحقاً إذا احتجت معاملات إضافية.</p>
                    </div>
                    
                    <button type="button" onclick="history.back()" class="btn btn-secondary">⬅️ السابق</button>
                    <button type="submit" class="btn">إنشاء Postback 🚀</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- الخطوة 3: النتائج -->
                <h3 class="step-title">الخطوة 3: روابط Postback الجاهزة</h3>
                
                <?php
                // إنشاء روابط Postback
                $base_url = rtrim($site_url, '/') . '/postback_cpalead.php';
                
                if ($postback_type === 'basic') {
                    $postback_url = $base_url . '?subid={subid}&ip_address={ip_address}&payout={payout}';
                } else {
                    $postback_url = $base_url . '?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&idfa={idfa}&gaid={gaid}&payout={payout}&ip_address={ip_address}&gateway_id={gateway_id}&lead_id={lead_id}&country_iso={country_iso}&virtual_currency={virtual_currency}';
                }
                
                if (!empty($password)) {
                    $postback_url .= '&password={password}';
                }
                
                $tracking_url = rtrim($site_url, '/') . '/go.php?id={OFFER_ID}&subid={subid}';
                $test_url = rtrim($site_url, '/') . '/postback_cpalead.php?subid=123&ip_address=*************&payout=2.50';
                ?>
                
                <div class="alert alert-success">
                    ✅ تم إنشاء روابط Postback بنجاح!
                </div>
                
                <!-- رابط Postback -->
                <div class="info-box">
                    <h4>🔗 رابط Postback لـ CPALead:</h4>
                    <div class="code-box" id="postback-url"><?php echo htmlspecialchars($postback_url); ?></div>
                    <button class="copy-btn" onclick="copyToClipboard('postback-url')">📋 نسخ الرابط</button>
                </div>
                
                <!-- رابط التتبع -->
                <div class="info-box">
                    <h4>🎯 رابط التتبع (مثال):</h4>
                    <div class="code-box" id="tracking-url"><?php echo htmlspecialchars($tracking_url); ?></div>
                    <button class="copy-btn" onclick="copyToClipboard('tracking-url')">📋 نسخ الرابط</button>
                    <p><small>⚠️ استبدل {OFFER_ID} بمعرف العرض الفعلي</small></p>
                </div>
                
                <!-- رابط الاختبار -->
                <div class="info-box">
                    <h4>🧪 رابط الاختبار:</h4>
                    <div class="code-box" id="test-url"><?php echo htmlspecialchars($test_url); ?></div>
                    <button class="copy-btn" onclick="copyToClipboard('test-url')">📋 نسخ الرابط</button>
                    <a href="<?php echo htmlspecialchars($test_url); ?>" target="_blank" class="btn btn-success" style="margin-right: 0.5rem;">🧪 اختبار الآن</a>
                </div>
                
                <!-- خطوات التنفيذ -->
                <div class="info-box">
                    <h4>📋 خطوات التنفيذ في CPALead:</h4>
                    <ol class="checklist">
                        <li>اذهب إلى لوحة تحكم CPALead</li>
                        <li>انتقل إلى "Postback" → "Configuration"</li>
                        <li>فعّل Postback</li>
                        <li>ألصق رابط Postback أعلاه</li>
                        <li>احفظ الإعدادات</li>
                        <li>اختبر Postback باستخدام رابط الاختبار</li>
                    </ol>
                </div>
                
                <!-- أدوات إضافية -->
                <div class="info-box">
                    <h4>🛠️ أدوات مفيدة:</h4>
                    <a href="postback_debug.php" class="btn">🔍 تشخيص Postback</a>
                    <a href="test_postback.php" class="btn">🧪 اختبار متقدم</a>
                    <a href="conversions.php" class="btn">📊 مراجعة التحويلات</a>
                </div>
                
                <button type="button" onclick="location.href='postback_wizard.php'" class="btn btn-secondary">🔄 إعداد جديد</button>
                
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // تفعيل اختيار الراديو
        document.querySelectorAll('.radio-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.radio-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
            });
        });
        
        // نسخ النص
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ الرابط بنجاح! 📋');
            }, function(err) {
                console.error('فشل في نسخ النص: ', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الرابط بنجاح! 📋');
            });
        }
    </script>
</body>
</html>
