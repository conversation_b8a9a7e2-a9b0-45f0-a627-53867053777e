<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$tests = [];
$all_passed = true;

// اختبار 1: الاتصال بقاعدة البيانات
try {
    $conn = getDBConnection();
    $conn->close();
    $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'success', 'message' => 'تم الاتصال بنجاح'];
} catch (Exception $e) {
    $tests[] = ['name' => 'الاتصال بقاعدة البيانات', 'status' => 'error', 'message' => $e->getMessage()];
    $all_passed = false;
}

// اختبار 2: وجود جدول العروض
try {
    $conn = getDBConnection();
    $result = $conn->query("SHOW TABLES LIKE 'offers'");
    if ($result->num_rows > 0) {
        $tests[] = ['name' => 'جدول العروض', 'status' => 'success', 'message' => 'الجدول موجود'];
    } else {
        $tests[] = ['name' => 'جدول العروض', 'status' => 'error', 'message' => 'الجدول غير موجود'];
        $all_passed = false;
    }
    $conn->close();
} catch (Exception $e) {
    $tests[] = ['name' => 'جدول العروض', 'status' => 'error', 'message' => $e->getMessage()];
    $all_passed = false;
}

// اختبار 3: حقل الوصف
$has_description = hasDescriptionField();
if ($has_description) {
    $tests[] = ['name' => 'حقل الوصف', 'status' => 'success', 'message' => 'الحقل موجود ويعمل'];
} else {
    $tests[] = ['name' => 'حقل الوصف', 'status' => 'warning', 'message' => 'الحقل غير موجود - يمكن إضافته'];
}

// اختبار 4: الدوال الآمنة
try {
    $offers = safeGetOffers("WHERE is_active = 1 LIMIT 1");
    $tests[] = ['name' => 'الدوال الآمنة', 'status' => 'success', 'message' => 'تعمل بشكل صحيح'];
} catch (Exception $e) {
    $tests[] = ['name' => 'الدوال الآمنة', 'status' => 'error', 'message' => $e->getMessage()];
    $all_passed = false;
}

// اختبار 5: عدد العروض
try {
    $conn = getDBConnection();
    $count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
    $tests[] = ['name' => 'عدد العروض', 'status' => 'info', 'message' => "يوجد {$count} عرض في قاعدة البيانات"];
    $conn->close();
} catch (Exception $e) {
    $tests[] = ['name' => 'عدد العروض', 'status' => 'error', 'message' => $e->getMessage()];
}

// اختبار 6: نظام الإشعارات
try {
    if (function_exists('getUnreadNotificationsCount')) {
        $unread = getUnreadNotificationsCount();
        $tests[] = ['name' => 'نظام الإشعارات', 'status' => 'success', 'message' => "يعمل بشكل صحيح - {$unread} إشعار غير مقروء"];
    } else {
        $tests[] = ['name' => 'نظام الإشعارات', 'status' => 'warning', 'message' => 'الدالة غير متاحة'];
    }
} catch (Exception $e) {
    $tests[] = ['name' => 'نظام الإشعارات', 'status' => 'error', 'message' => $e->getMessage()];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .overall-status {
            text-align: center;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            font-size: 1.2rem;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #28a745;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #dc3545;
        }
        
        .test-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 5px solid #667eea;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .test-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .test-item.info {
            border-left-color: #17a2b8;
            background: #d1ecf1;
        }
        
        .test-name {
            font-weight: bold;
            color: #333;
        }
        
        .test-message {
            color: #666;
            font-size: 0.9rem;
        }
        
        .test-status {
            font-size: 1.5rem;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار النظام</h1>
        
        <!-- الحالة العامة -->
        <div class="overall-status <?php echo $all_passed ? 'status-success' : 'status-error'; ?>">
            <?php if ($all_passed): ?>
                ✅ جميع الاختبارات نجحت - النظام يعمل بشكل صحيح!
            <?php else: ?>
                ❌ بعض الاختبارات فشلت - يحتاج النظام إلى إصلاح
            <?php endif; ?>
        </div>
        
        <!-- نتائج الاختبارات -->
        <?php foreach ($tests as $test): ?>
            <div class="test-item <?php echo $test['status']; ?>">
                <div>
                    <div class="test-name"><?php echo htmlspecialchars($test['name']); ?></div>
                    <div class="test-message"><?php echo htmlspecialchars($test['message']); ?></div>
                </div>
                <div class="test-status">
                    <?php
                    switch ($test['status']) {
                        case 'success':
                            echo '✅';
                            break;
                        case 'error':
                            echo '❌';
                            break;
                        case 'warning':
                            echo '⚠️';
                            break;
                        case 'info':
                            echo 'ℹ️';
                            break;
                        default:
                            echo '❓';
                    }
                    ?>
                </div>
            </div>
        <?php endforeach; ?>
        
        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <?php if (!$has_description): ?>
                <a href="fix_description_field.php" class="btn btn-danger">
                    🔧 إصلاح حقل الوصف
                </a>
            <?php endif; ?>
            
            <?php if (!$all_passed): ?>
                <a href="database_diagnostic.php" class="btn btn-danger">
                    🔍 تشخيص مفصل
                </a>
            <?php endif; ?>
            
            <a href="add_test_offers.php" class="btn btn-success">
                🧪 إضافة عروض تجريبية
            </a>
            
            <a href="index.php" class="btn">
                🏠 الصفحة الرئيسية
            </a>
            
            <a href="api_manager.php" class="btn">
                🔧 إدارة API
            </a>
        </div>
    </div>
</body>
</html>
