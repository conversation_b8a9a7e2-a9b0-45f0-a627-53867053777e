<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$fixes = [];
$errors = [];

// إصلاح 1: التحقق من ملف معرفات API
if (!file_exists('api_ids.txt')) {
    // إنشاء ملف معرفات API مع معرف تجريبي
    $sample_content = "// أضف معرفات API الخاصة بك هنا\n// مثال: 12345\n";
    if (file_put_contents('api_ids.txt', $sample_content)) {
        $fixes[] = "✅ تم إنشاء ملف معرفات API";
    } else {
        $errors[] = "❌ فشل في إنشاء ملف معرفات API";
    }
} else {
    $fixes[] = "✅ ملف معرفات API موجود";
}

// إصلاح 2: التحقق من صلاحيات الكتابة
$logFiles = ['auto_sync.log', 'sync_results.log', 'cpalead_conversions.log'];
foreach ($logFiles as $logFile) {
    if (!file_exists($logFile)) {
        if (file_put_contents($logFile, "")) {
            $fixes[] = "✅ تم إنشاء ملف السجل: $logFile";
        } else {
            $errors[] = "❌ فشل في إنشاء ملف السجل: $logFile";
        }
    } else {
        if (is_writable($logFile)) {
            $fixes[] = "✅ ملف السجل قابل للكتابة: $logFile";
        } else {
            $errors[] = "❌ ملف السجل غير قابل للكتابة: $logFile";
        }
    }
}

// إصلاح 3: التحقق من إعدادات PHP
$required_extensions = ['curl', 'json', 'mysqli'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        $fixes[] = "✅ إضافة PHP متاحة: $ext";
    } else {
        $errors[] = "❌ إضافة PHP مفقودة: $ext";
    }
}

// إصلاح 4: اختبار الاتصال بـ CPALead
try {
    $test_url = "https://cpalead.com/api/offers?id=test";
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $test_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // تتبع إعادة التوجيه
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // حد أقصى 5 إعادة توجيه
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPALead-Sync/1.0)');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        $errors[] = "❌ خطأ في الاتصال بـ CPALead: $error";
    } elseif ($httpCode == 200 || $httpCode == 400 || $httpCode == 401) {
        // 200 = نجح، 400 = معرف خاطئ (متوقع)، 401 = غير مصرح (متوقع)
        $fixes[] = "✅ الاتصال بـ CPALead يعمل (HTTP: $httpCode)";
    } elseif ($httpCode == 301 || $httpCode == 302) {
        $fixes[] = "⚠️ إعادة توجيه من CPALead (HTTP: $httpCode) - الاتصال يعمل";
    } else {
        $errors[] = "❌ رمز HTTP غير متوقع من CPALead: $httpCode";
    }
} catch (Exception $e) {
    $errors[] = "❌ خطأ في اختبار CPALead: " . $e->getMessage();
}

// إصلاح 5: التحقق من قاعدة البيانات
try {
    $conn = getDBConnection();
    
    // التحقق من جدول العروض
    $result = $conn->query("SHOW TABLES LIKE 'offers'");
    if ($result->num_rows > 0) {
        $fixes[] = "✅ جدول العروض موجود";
        
        // التحقق من الحقول المطلوبة
        $columns = $conn->query("SHOW COLUMNS FROM offers");
        $existing_columns = [];
        while ($col = $columns->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }
        
        $required_columns = ['id', 'title', 'offer_url', 'is_active'];
        foreach ($required_columns as $col) {
            if (in_array($col, $existing_columns)) {
                $fixes[] = "✅ حقل موجود: $col";
            } else {
                $errors[] = "❌ حقل مفقود: $col";
            }
        }
        
        // التحقق من حقل الوصف
        if (hasDescriptionField()) {
            $fixes[] = "✅ حقل الوصف موجود";
        } else {
            $errors[] = "⚠️ حقل الوصف مفقود - سيتم إضافته تلقائياً";
        }
        
    } else {
        $errors[] = "❌ جدول العروض غير موجود";
    }
    
    $conn->close();
} catch (Exception $e) {
    $errors[] = "❌ خطأ في قاعدة البيانات: " . $e->getMessage();
}

$success = empty($errors);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل المزامنة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 800px;
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .status-message {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .fixes-list, .errors-list {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .fixes-list li, .errors-list li {
            background: #f8f9fa;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .errors-list li {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 8px;
            margin-top: 2rem;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .instructions ol {
            margin-right: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $success ? '✅' : '⚠️'; ?>
            </div>
            <h1>🔧 إصلاح مشاكل المزامنة</h1>
        </div>
        
        <div class="status-message <?php echo $success ? 'status-success' : 'status-error'; ?>">
            <?php if ($success): ?>
                🎉 جميع فحوصات المزامنة نجحت! النظام جاهز للمزامنة.
            <?php else: ?>
                ⚠️ تم العثور على بعض المشاكل التي تحتاج إلى إصلاح.
            <?php endif; ?>
        </div>
        
        <?php if (!empty($fixes)): ?>
        <div>
            <h3 style="color: #28a745; margin-bottom: 1rem;">✅ الفحوصات الناجحة:</h3>
            <ul class="fixes-list">
                <?php foreach ($fixes as $fix): ?>
                    <li><?php echo htmlspecialchars($fix); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div>
            <h3 style="color: #dc3545; margin-bottom: 1rem;">❌ المشاكل المكتشفة:</h3>
            <ul class="errors-list">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <?php if ($success): ?>
                <a href="run_sync.php" class="btn btn-success">
                    🚀 تشغيل المزامنة الآن
                </a>
            <?php else: ?>
                <a href="fix_all_issues.php" class="btn btn-danger">
                    🛠️ إصلاح جميع المشاكل
                </a>
            <?php endif; ?>
            
            <a href="sync_manager.php" class="btn">
                🔧 إدارة المزامنة
            </a>
            
            <a href="api_manager.php" class="btn">
                📡 إدارة API
            </a>
        </div>
        
        <?php if (!$success): ?>
        <div class="instructions">
            <h3>📋 خطوات الإصلاح:</h3>
            <ol>
                <li><strong>إضافة معرفات API:</strong> اذهب إلى إدارة المزامنة وأضف معرفات CPALead الخاصة بك</li>
                <li><strong>إصلاح قاعدة البيانات:</strong> استخدم أداة "إصلاح جميع المشاكل"</li>
                <li><strong>التحقق من الصلاحيات:</strong> تأكد من أن المجلد قابل للكتابة</li>
                <li><strong>اختبار المزامنة:</strong> جرب تشغيل المزامنة بعد الإصلاح</li>
            </ol>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
