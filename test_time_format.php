<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تنسيق الوقت - نظام 12 ساعة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .time-test {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }
        
        .time-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .time-value {
            font-size: 1.2rem;
            color: #667eea;
            font-family: monospace;
        }
        
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .info {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .live-time {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 اختبار تنسيق الوقت - نظام 12 ساعة</h1>
        
        <div class="live-time" id="live-time">
            جاري تحميل الوقت المباشر...
        </div>
        
        <div class="time-test success">
            <div class="time-label">✅ الوقت الحالي بنظام 12 ساعة (دالة getCurrentCairoTime):</div>
            <div class="time-value"><?php echo getCurrentCairoTime(); ?></div>
        </div>
        
        <div class="time-test info">
            <div class="time-label">🕐 تنسيق الوقت فقط (دالة formatTime12Hour):</div>
            <div class="time-value"><?php echo formatTime12Hour(date('Y-m-d H:i:s')); ?></div>
        </div>
        
        <div class="time-test">
            <div class="time-label">📅 تنسيق الوقت النسبي (دالة formatCairoTime):</div>
            <div class="time-value"><?php echo formatCairoTime(date('Y-m-d H:i:s')); ?></div>
        </div>
        
        <div class="time-test">
            <div class="time-label">⏰ مقارنة مع النظام القديم (24 ساعة):</div>
            <div class="time-value">
                <strong>نظام 24 ساعة:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
                <strong>نظام 12 ساعة:</strong> <?php echo getCurrentCairoTime(); ?>
            </div>
        </div>
        
        <div class="time-test success">
            <div class="time-label">🎯 مثال على إشعار بالوقت الجديد:</div>
            <div class="time-value">
                🎯 العرض: #11 👤 المستخدم: Spartan 🌐 IP: ************* 🕐 <?php echo getCurrentCairoTime(); ?>
            </div>
        </div>
        
        <div class="time-test info">
            <div class="time-label">📊 أمثلة على أوقات مختلفة:</div>
            <div class="time-value">
                <?php
                // أمثلة على أوقات مختلفة
                $times = [
                    '2025-07-13 09:30:15',
                    '2025-07-13 13:45:30',
                    '2025-07-13 18:20:45',
                    '2025-07-13 23:59:59'
                ];
                
                foreach ($times as $time) {
                    $cairo_time = new DateTime($time);
                    $cairo_time->setTimezone(new DateTimeZone('Africa/Cairo'));
                    echo "• " . $cairo_time->format('Y-m-d g:i:s A') . "<br>";
                }
                ?>
            </div>
        </div>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="notifications_panel.php">🔔 لوحة الإشعارات</a>
            <a href="conversions.php">📊 التحويلات</a>
            <a href="saved_ips.php">💾 IPs المحفوظة</a>
        </div>
    </div>
    
    <script>
        // تحديث الوقت المباشر كل ثانية
        function updateLiveTime() {
            const now = new Date();
            const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
            
            const year = cairoTime.getFullYear();
            const month = String(cairoTime.getMonth() + 1).padStart(2, '0');
            const day = String(cairoTime.getDate()).padStart(2, '0');
            
            let hours = cairoTime.getHours();
            const minutes = String(cairoTime.getMinutes()).padStart(2, '0');
            const seconds = String(cairoTime.getSeconds()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            
            hours = hours % 12;
            hours = hours ? hours : 12;
            
            const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${ampm}`;
            document.getElementById('live-time').innerHTML = '🕐 الوقت المباشر: ' + formatted;
        }
        
        // تحديث فوري عند تحميل الصفحة
        updateLiveTime();
        
        // تحديث كل ثانية
        setInterval(updateLiveTime, 1000);
    </script>
</body>
</html>
