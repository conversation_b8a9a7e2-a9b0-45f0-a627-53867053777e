<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';

// إنشاء إشعار تجريبي
if (isset($_POST['create_test_notification'])) {
    $type = $_POST['notification_type'];
    
    switch ($type) {
        case 'click':
            $result = notifyOfferClick(11, 'عرض تجريبي - تطبيق موبايل', 'Spartan', '*************');
            break;
        case 'conversion':
            $result = notifyConversion(123, 11, 'عرض تجريبي - تطبيق موبايل', 'Spartan', '*************', 2.50, 'cpalead');
            break;
        case 'ip_save':
            $result = notifyIPSave('Spartan', '*************');
            break;
        case 'new_offer':
            $result = notifyNewOffer(11, 'عرض تجريبي - تطبيق موبايل', 'manual');
            break;
    }
    
    if ($result) {
        $message = '<div class="alert alert-success">✅ تم إنشاء إشعار تجريبي بنظام 12 ساعة بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في إنشاء الإشعار!</div>';
    }
}

// الحصول على آخر 5 إشعارات
$recent_notifications = getNotifications(5);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات - نظام 12 ساعة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .notifications-preview {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .notifications-header {
            background: #667eea;
            color: white;
            padding: 1rem;
            font-weight: bold;
        }
        
        .notification-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .notification-icon {
            font-size: 1.2rem;
            margin-left: 0.5rem;
        }
        
        .notification-title {
            font-weight: bold;
            color: #333;
        }
        
        .notification-time {
            font-size: 0.9rem;
            color: #666;
        }
        
        .notification-message {
            color: #555;
            margin-bottom: 0.5rem;
        }
        
        .notification-data {
            background: #e9ecef;
            padding: 0.5rem;
            border-radius: 3px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .current-time {
            text-align: center;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: bold;
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 اختبار الإشعارات - نظام 12 ساعة</h1>
        
        <div class="current-time" id="current-time">
            🕐 الوقت الحالي: <?php echo getCurrentCairoTime(); ?>
        </div>
        
        <?php echo $message; ?>
        
        <!-- نموذج إنشاء إشعار تجريبي -->
        <div class="test-form">
            <h3>🧪 إنشاء إشعار تجريبي</h3>
            <form method="POST">
                <div class="form-group">
                    <label for="notification_type">نوع الإشعار:</label>
                    <select id="notification_type" name="notification_type" required>
                        <option value="">اختر نوع الإشعار</option>
                        <option value="click">🎯 ضغطة على عرض</option>
                        <option value="conversion">💰 تحويل جديد</option>
                        <option value="ip_save">💾 حفظ IP</option>
                        <option value="new_offer">🆕 عرض جديد</option>
                    </select>
                </div>
                <button type="submit" name="create_test_notification" class="btn">
                    ✨ إنشاء إشعار تجريبي
                </button>
            </form>
        </div>
        
        <!-- معاينة الإشعارات الأخيرة -->
        <div class="notifications-preview">
            <div class="notifications-header">
                📋 آخر 5 إشعارات (بنظام 12 ساعة)
            </div>
            
            <?php if (empty($recent_notifications)): ?>
                <div class="notification-item" style="text-align: center; color: #666;">
                    لا توجد إشعارات حالياً
                </div>
            <?php else: ?>
                <?php foreach ($recent_notifications as $notification): ?>
                    <div class="notification-item">
                        <div class="notification-header">
                            <div style="display: flex; align-items: center;">
                                <span class="notification-icon"><?php echo getNotificationIcon($notification['type']); ?></span>
                                <span class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></span>
                            </div>
                            <span class="notification-time"><?php echo formatCairoTime($notification['created_at']); ?></span>
                        </div>
                        
                        <div class="notification-message">
                            <?php echo htmlspecialchars($notification['message']); ?>
                        </div>
                        
                        <?php if ($notification['data']): ?>
                            <div class="notification-data">
                                <?php
                                $data = $notification['data'];
                                if (isset($data['offer_id'])) echo "🎯 العرض: #" . $data['offer_id'] . " ";
                                if (isset($data['username'])) echo "👤 المستخدم: " . htmlspecialchars($data['username']) . " ";
                                if (isset($data['ip_address'])) echo "🌐 IP: " . htmlspecialchars($data['ip_address']) . " ";
                                if (isset($data['payout']) && $data['payout'] > 0) echo "💰 العمولة: $" . number_format($data['payout'], 2) . " ";
                                if (isset($data['cairo_time'])) echo "🕐 " . $data['cairo_time'];
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="notifications_panel.php">🔔 لوحة الإشعارات الكاملة</a>
            <a href="test_time_format.php">🕐 اختبار تنسيق الوقت</a>
            <a href="conversions.php">📊 التحويلات</a>
        </div>
    </div>
    
    <script>
        // تحديث الوقت المباشر كل ثانية
        function updateCurrentTime() {
            const now = new Date();
            const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
            
            const year = cairoTime.getFullYear();
            const month = String(cairoTime.getMonth() + 1).padStart(2, '0');
            const day = String(cairoTime.getDate()).padStart(2, '0');
            
            let hours = cairoTime.getHours();
            const minutes = String(cairoTime.getMinutes()).padStart(2, '0');
            const seconds = String(cairoTime.getSeconds()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            
            hours = hours % 12;
            hours = hours ? hours : 12;
            
            const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${ampm}`;
            document.getElementById('current-time').innerHTML = '🕐 الوقت الحالي: ' + formatted;
        }
        
        // تحديث فوري عند تحميل الصفحة
        updateCurrentTime();
        
        // تحديث كل ثانية
        setInterval(updateCurrentTime, 1000);
    </script>
</body>
</html>
