<?php
require_once 'config.php';

/**
 * نظام الإشعارات
 * إرسال إشعارات عند تنفيذ العروض والتحويلات
 */

// إنشاء جدول الإشعارات إذا لم يكن موجود
function createNotificationsTable() {
    $conn = getDBConnection();
    
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL COLLATE utf8mb4_unicode_ci,
        title VARCHAR(255) NOT NULL COLLATE utf8mb4_unicode_ci,
        message TEXT NOT NULL COLLATE utf8mb4_unicode_ci,
        data JSON,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_type (type),
        INDEX idx_read (is_read),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->query($sql);
    $conn->close();
}

// إضافة إشعار جديد
function addNotification($type, $title, $message, $data = null) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("
        INSERT INTO notifications (type, title, message, data) 
        VALUES (?, ?, ?, ?)
    ");
    
    $data_json = $data ? json_encode($data, JSON_UNESCAPED_UNICODE) : null;
    $stmt->bind_param("ssss", $type, $title, $message, $data_json);
    
    $result = $stmt->execute();
    $stmt->close();
    $conn->close();
    
    return $result;
}

// إشعار عند الضغط على عرض
function notifyOfferClick($offer_id, $offer_title, $username, $ip_address) {
    $cairo_time = getCurrentCairoTime();

    $title = "🎯 ضغطة جديدة على عرض";
    $message = "المستخدم {$username} ضغط على العرض: {$offer_title}";

    $data = [
        'offer_id' => $offer_id,
        'offer_title' => $offer_title,
        'username' => $username,
        'ip_address' => $ip_address,
        'cairo_time' => $cairo_time,
        'action' => 'click'
    ];

    return addNotification('offer_click', $title, $message, $data);
}

// إشعار عند التحويل
function notifyConversion($conversion_id, $offer_id, $offer_title, $username, $ip_address, $payout = 0, $source = 'direct') {
    $cairo_time = getCurrentCairoTime();

    $title = "💰 تحويل جديد!";
    $payout_text = $payout > 0 ? " بقيمة $" . number_format($payout, 2) : "";
    $message = "تحويل ناجح للمستخدم {$username} على العرض: {$offer_title}{$payout_text}";

    $data = [
        'conversion_id' => $conversion_id,
        'offer_id' => $offer_id,
        'offer_title' => $offer_title,
        'username' => $username,
        'ip_address' => $ip_address,
        'payout' => $payout,
        'source' => $source,
        'cairo_time' => $cairo_time,
        'action' => 'conversion'
    ];

    return addNotification('conversion', $title, $message, $data);
}

// إشعار عند حفظ IP
function notifyIPSave($username, $ip_address) {
    $cairo_time = getCurrentCairoTime();

    $title = "💾 حفظ IP جديد";
    $message = "تم حفظ IP جديد للمستخدم: {$username}";

    $data = [
        'username' => $username,
        'ip_address' => $ip_address,
        'cairo_time' => $cairo_time,
        'action' => 'ip_save'
    ];

    return addNotification('ip_save', $title, $message, $data);
}

// إشعار عند إضافة عرض جديد
function notifyNewOffer($offer_id, $offer_title, $source = 'manual') {
    $cairo_time = getCurrentCairoTime();

    $title = "🆕 عرض جديد";
    $message = "تم إضافة عرض جديد: {$offer_title}";

    $data = [
        'offer_id' => $offer_id,
        'offer_title' => $offer_title,
        'source' => $source,
        'cairo_time' => $cairo_time,
        'action' => 'new_offer'
    ];

    return addNotification('new_offer', $title, $message, $data);
}

// الحصول على الإشعارات
function getNotifications($limit = 20, $unread_only = false) {
    $conn = getDBConnection();
    
    $where_clause = $unread_only ? "WHERE is_read = 0" : "";
    
    $stmt = $conn->prepare("
        SELECT * FROM notifications 
        {$where_clause}
        ORDER BY created_at DESC 
        LIMIT ?
    ");
    
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $row['data'] = $row['data'] ? json_decode($row['data'], true) : null;
        $notifications[] = $row;
    }
    
    $stmt->close();
    $conn->close();
    
    return $notifications;
}

// عدد الإشعارات غير المقروءة
function getUnreadNotificationsCount() {
    $conn = getDBConnection();
    
    $result = $conn->query("SELECT COUNT(*) as count FROM notifications WHERE is_read = 0");
    $count = $result->fetch_assoc()['count'];
    
    $conn->close();
    return $count;
}

// تعليم الإشعار كمقروء
function markNotificationAsRead($notification_id) {
    $conn = getDBConnection();
    
    $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE id = ?");
    $stmt->bind_param("i", $notification_id);
    $result = $stmt->execute();
    
    $stmt->close();
    $conn->close();
    
    return $result;
}

// تعليم جميع الإشعارات كمقروءة
function markAllNotificationsAsRead() {
    $conn = getDBConnection();
    
    $result = $conn->query("UPDATE notifications SET is_read = 1 WHERE is_read = 0");
    $conn->close();
    
    return $result;
}

// حذف إشعار محدد
function deleteNotification($notification_id) {
    $conn = getDBConnection();

    $stmt = $conn->prepare("DELETE FROM notifications WHERE id = ?");
    $stmt->bind_param("i", $notification_id);
    $result = $stmt->execute();

    $stmt->close();
    $conn->close();

    return $result;
}

// حذف إشعارات متعددة
function deleteMultipleNotifications($notification_ids) {
    if (empty($notification_ids)) return false;

    $conn = getDBConnection();

    $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
    $stmt = $conn->prepare("DELETE FROM notifications WHERE id IN ($placeholders)");
    $stmt->bind_param(str_repeat('i', count($notification_ids)), ...$notification_ids);
    $result = $stmt->execute();

    $affected_rows = $stmt->affected_rows;
    $stmt->close();
    $conn->close();

    return $affected_rows;
}

// حذف جميع الإشعارات
function deleteAllNotifications() {
    $conn = getDBConnection();

    $result = $conn->query("DELETE FROM notifications");
    $affected_rows = $conn->affected_rows;
    $conn->close();

    return $affected_rows;
}

// حذف الإشعارات القديمة (أكثر من 30 يوم)
function cleanOldNotifications() {
    $conn = getDBConnection();

    $result = $conn->query("
        DELETE FROM notifications
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");

    $affected_rows = $conn->affected_rows;
    $conn->close();

    return $affected_rows;
}

// إحصائيات الإشعارات
function getNotificationsStats() {
    $conn = getDBConnection();
    
    $stats = $conn->query("
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread,
            COUNT(CASE WHEN type = 'conversion' THEN 1 END) as conversions,
            COUNT(CASE WHEN type = 'offer_click' THEN 1 END) as clicks,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today
        FROM notifications
    ")->fetch_assoc();
    
    $conn->close();
    return $stats;
}

// تهيئة جدول الإشعارات
createNotificationsTable();

// دالة لتنسيق الوقت بالتوقيت المصري (نظام 12 ساعة)
function formatCairoTime($timestamp) {
    $cairo_time = new DateTime($timestamp);
    $cairo_time->setTimezone(new DateTimeZone('Africa/Cairo'));

    $now = new DateTime('now', new DateTimeZone('Africa/Cairo'));

    // حساب الفرق بالثواني للحصول على دقة أفضل
    $diff_seconds = $now->getTimestamp() - $cairo_time->getTimestamp();

    // إذا كان الوقت في المستقبل، اعرض الوقت الفعلي
    if ($diff_seconds < 0) {
        return $cairo_time->format('Y-m-d g:i A');
    }

    // أقل من دقيقة
    if ($diff_seconds < 60) {
        return "الآن";
    }

    // أقل من ساعة
    if ($diff_seconds < 3600) {
        $minutes = floor($diff_seconds / 60);
        return "منذ " . $minutes . " دقيقة" . ($minutes > 1 ? "" : "");
    }

    // أقل من يوم
    if ($diff_seconds < 86400) {
        $hours = floor($diff_seconds / 3600);
        return "منذ " . $hours . " ساعة" . ($hours > 1 ? "" : "");
    }

    // أقل من أسبوع
    if ($diff_seconds < 604800) {
        $days = floor($diff_seconds / 86400);
        if ($days == 1) {
            return "أمس في " . $cairo_time->format('g:i A');
        } else {
            return "منذ " . $days . " يوم" . ($days > 1 ? "" : "");
        }
    }

    // أكثر من أسبوع - اعرض التاريخ الكامل
    return $cairo_time->format('Y-m-d g:i A');
}

// دالة للحصول على الوقت الحالي بنظام 12 ساعة
function getCurrentCairoTime() {
    $cairo_time = new DateTime('now', new DateTimeZone('Africa/Cairo'));
    return $cairo_time->format('Y-m-d g:i:s A');
}

// دالة للحصول على الوقت الحالي بنظام 12 ساعة للإشعارات
function getCurrentCairoTimeForNotifications() {
    $cairo_time = new DateTime('now', new DateTimeZone('Africa/Cairo'));
    return $cairo_time->format('Y-m-d g:i:s A');
}

// دالة لتنسيق الوقت بنظام 12 ساعة فقط
function formatTime12Hour($timestamp) {
    $cairo_time = new DateTime($timestamp);
    $cairo_time->setTimezone(new DateTimeZone('Africa/Cairo'));
    return $cairo_time->format('g:i A');
}

// دالة للحصول على أيقونة الإشعار
function getNotificationIcon($type) {
    switch ($type) {
        case 'conversion':
            return '💰';
        case 'offer_click':
            return '🎯';
        case 'ip_save':
            return '💾';
        case 'new_offer':
            return '🆕';
        default:
            return '📢';
    }
}

// دالة للحصول على لون الإشعار
function getNotificationColor($type) {
    switch ($type) {
        case 'conversion':
            return '#28a745'; // أخضر
        case 'offer_click':
            return '#007bff'; // أزرق
        case 'ip_save':
            return '#6f42c1'; // بنفسجي
        case 'new_offer':
            return '#fd7e14'; // برتقالي
        default:
            return '#6c757d'; // رمادي
    }
}
?>
