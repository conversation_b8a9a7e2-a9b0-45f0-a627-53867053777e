<?php
// ملف الاستيراد التلقائي - يمكن تشغيله عبر Cron Job
require_once 'config.php';

// إعدادات الاستيراد التلقائي
$API_IDS = [
    '1941213', // يمكن إضافة معرفات API متعددة
    // 'your_api_id_2',
    // 'your_api_id_3',
];

$LOG_FILE = 'auto_import.log';

// دالة كتابة السجل
function writeLog($message) {
    global $LOG_FILE;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    file_put_contents($LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// دالة استيراد العروض من API
function importOffersFromAPI($apiId) {
    $conn = getDBConnection();
    $imported = 0;
    $errors = [];
    
    $apiUrl = "https://www.cpalead.com/api/offers?id=" . urlencode($apiId);
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (compatible; CPA-Site-AutoImport/1.0)',
            'method' => 'GET',
            'header' => [
                'Accept: application/json',
                'Content-Type: application/json'
            ]
        ]
    ]);
    
    try {
        $response = @file_get_contents($apiUrl, false, $context);
        
        if ($response !== false) {
            $data = json_decode($response, true);

            if ($data && isset($data['status']) && $data['status'] === 'success' && isset($data['offers'])) {
                $offers = $data['offers'];
                writeLog("تم العثور على " . count($offers) . " عرض من API: $apiId");

                foreach ($offers as $offer) {
                    if (isset($offer['title']) && (isset($offer['preview_link']) || isset($offer['link']))) {
                        $title = trim($offer['title']);
                        $preview_link = isset($offer['preview_link']) ? trim($offer['preview_link']) : '';
                        $tracking_link = isset($offer['link']) ? trim($offer['link']) : '';
                        $amount = isset($offer['amount']) ? $offer['amount'] : 0;
                        $device = isset($offer['device']) ? $offer['device'] : '';
                        $payout_type = isset($offer['payout_type']) ? $offer['payout_type'] : '';

                        // إنشاء عنوان محسن
                        $enhanced_title = $title;
                        if ($amount > 0) {
                            $enhanced_title .= " - $" . $amount . " " . $payout_type;
                        }
                        if ($device) {
                            $enhanced_title .= " (" . ucfirst($device) . ")";
                        }

                        // استخدام رابط التتبع إذا كان متوفراً، وإلا استخدم رابط المعاينة
                        $final_url = !empty($tracking_link) ? $tracking_link : $preview_link;

                        // استخراج صورة من creatives
                        $image_url = '';
                        if (isset($offer['creatives']['url'])) {
                            $image_url = trim($offer['creatives']['url']);
                        }

                        // استخدام صورة افتراضية إذا لم تكن متوفرة
                        if (empty($image_url)) {
                            $image_url = 'https://via.placeholder.com/300x200/4CAF50/white?text=' . urlencode($title);
                        }

                        // استخراج البلدان
                        $countries = '';
                        if (isset($offer['countries']) && is_array($offer['countries'])) {
                            $countries = implode(',', $offer['countries']);
                        }

                        // التحقق من عدم وجود العرض مسبقاً
                        $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ? OR offer_url = ?");
                        $checkStmt->bind_param("ss", $enhanced_title, $final_url);
                        $checkStmt->execute();
                        $existingOffer = $checkStmt->get_result();
                        $checkStmt->close();

                        if ($existingOffer->num_rows == 0) {
                            // استخراج الوصف من البيانات
                            $description = '';
                            if (isset($offer['description']) && !empty($offer['description'])) {
                                $description = strip_tags($offer['description']);
                                $description = substr($description, 0, 500); // تحديد طول الوصف
                            } elseif (isset($offer['preview_link']) && !empty($offer['preview_link'])) {
                                $description = "معاينة العرض متاحة - " . $offer['preview_link'];
                            } else {
                                $description = "عرض من CPALead - " . $enhanced_title;
                            }

                            // إضافة العرض الجديد مع المعلومات الإضافية والوصف
                            $insertStmt = $conn->prepare("INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                            $insertStmt->bind_param("ssssssds", $enhanced_title, $description, $image_url, $final_url, $countries, $device, $amount, $payout_type);

                            if ($insertStmt->execute()) {
                                $imported++;
                                writeLog("تم استيراد عرض جديد: $enhanced_title");
                            } else {
                                $errors[] = "فشل في إضافة العرض: $enhanced_title";
                            }
                            $insertStmt->close();
                        } else {
                            writeLog("العرض موجود مسبقاً: $enhanced_title");
                        }
                    } else {
                        $errors[] = "بيانات ناقصة في أحد العروض من API: $apiId";
                    }
                }
            } else {
                $errors[] = "تنسيق البيانات غير صحيح أو فشل API: $apiId";
                if (isset($data['status'])) {
                    writeLog("حالة API: " . $data['status']);
                }
            }
        } else {
            $errors[] = "فشل في الاتصال بـ API: $apiId";
        }
    } catch (Exception $e) {
        $errors[] = "خطأ في API $apiId: " . $e->getMessage();
    }
    
    $conn->close();
    return ['imported' => $imported, 'errors' => $errors];
}

// تشغيل الاستيراد التلقائي
writeLog("بدء عملية الاستيراد التلقائي");

$totalImported = 0;
$allErrors = [];

foreach ($API_IDS as $apiId) {
    writeLog("استيراد من API: $apiId");
    $result = importOffersFromAPI($apiId);
    
    $totalImported += $result['imported'];
    $allErrors = array_merge($allErrors, $result['errors']);
    
    writeLog("تم استيراد {$result['imported']} عرض من API: $apiId");
    
    if (!empty($result['errors'])) {
        foreach ($result['errors'] as $error) {
            writeLog("خطأ: $error");
        }
    }
    
    // انتظار ثانية واحدة بين الطلبات
    sleep(1);
}

writeLog("انتهاء عملية الاستيراد التلقائي - إجمالي العروض المستوردة: $totalImported");

// إذا تم تشغيل الملف مباشرة (وليس عبر include)
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    // التحقق من كلمة المرور إذا تم الوصول عبر المتصفح
    if (isset($_SERVER['HTTP_HOST'])) {
        checkPassword();
        
        echo "<!DOCTYPE html>";
        echo "<html lang='ar' dir='rtl'>";
        echo "<head>";
        echo "<meta charset='UTF-8'>";
        echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
        echo "<title>الاستيراد التلقائي - موقع CPA</title>";
        echo "<style>";
        echo "body { font-family: Arial, sans-serif; padding: 2rem; background: #f5f5f5; }";
        echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
        echo ".success { color: #28a745; background: #d4edda; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
        echo ".error { color: #dc3545; background: #f8d7da; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
        echo ".log { background: #f8f9fa; padding: 1rem; border-radius: 5px; margin: 1rem 0; max-height: 400px; overflow-y: auto; }";
        echo "</style>";
        echo "</head>";
        echo "<body>";
        echo "<div class='container'>";
        echo "<h1>🔄 الاستيراد التلقائي</h1>";
        
        if ($totalImported > 0) {
            echo "<div class='success'>✅ تم استيراد $totalImported عرض بنجاح!</div>";
        } else {
            echo "<div class='error'>⚠️ لم يتم استيراد أي عروض جديدة.</div>";
        }
        
        if (!empty($allErrors)) {
            echo "<div class='error'><strong>الأخطاء:</strong><br>" . implode('<br>', $allErrors) . "</div>";
        }
        
        // عرض سجل العمليات
        if (file_exists($LOG_FILE)) {
            echo "<h3>📋 سجل العمليات:</h3>";
            echo "<div class='log'>";
            echo "<pre>" . htmlspecialchars(file_get_contents($LOG_FILE)) . "</pre>";
            echo "</div>";
        }
        
        echo "<p><a href='admin.php' style='background: #667eea; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px;'>🏠 العودة للوحة التحكم</a></p>";
        echo "</div>";
        echo "</body>";
        echo "</html>";
    } else {
        // تشغيل عبر سطر الأوامر
        echo "تم استيراد $totalImported عرض بنجاح!\n";
        if (!empty($allErrors)) {
            echo "الأخطاء:\n" . implode("\n", $allErrors) . "\n";
        }
    }
}
?>
