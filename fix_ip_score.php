<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$test_results = [];

// اختبار درجة IP الحالية
if (isset($_POST['test_current_ip'])) {
    $user_ip = $_SERVER['REMOTE_ADDR'];
    $ipInfo = checkIPQuality($user_ip);
    
    $test_results = [
        'ip' => $user_ip,
        'current_system' => $ipInfo,
        'expected_score' => 100 - $ipInfo['fraud_score'], // ما يجب أن تكون عليه النتيجة
        'actual_score' => $ipInfo['quality_score'] // النتيجة الحالية
    ];
    
    $message = "✅ تم اختبار IP الحالي!";
}

// إصلاح درجة IP في قاعدة البيانات
if (isset($_POST['fix_ip_scores'])) {
    try {
        $conn = getDBConnection();
        
        // جلب جميع IPs المحفوظة
        $ips = $conn->query("SELECT ip_address, quality_score FROM saved_ips");
        $updated = 0;
        
        while ($row = $ips->fetch_assoc()) {
            $ip = $row['ip_address'];
            $old_score = $row['quality_score'];
            
            // إعادة فحص IP
            $ipInfo = checkIPQuality($ip);
            $new_score = $ipInfo['quality_score'];
            
            // تحديث النتيجة إذا تغيرت
            if ($old_score != $new_score) {
                $updateStmt = $conn->prepare("UPDATE saved_ips SET quality_score = ?, is_proxy = ?, is_vpn = ? WHERE ip_address = ?");
                $updateStmt->bind_param("iiis", $new_score, $ipInfo['is_proxy'], $ipInfo['is_vpn'], $ip);
                
                if ($updateStmt->execute()) {
                    $updated++;
                }
                $updateStmt->close();
            }
        }
        
        $conn->close();
        $message = "✅ تم تحديث درجات $updated عنوان IP!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إصلاح درجات IP: " . $e->getMessage();
    }
}

// اختبار عدة IPs
$sample_ips = [
    '*******' => 'Google DNS',
    '*******' => 'Cloudflare DNS', 
    '**************' => 'OpenDNS',
    '***************' => 'GitHub',
    $_SERVER['REMOTE_ADDR'] => 'عنوان IP الخاص بك'
];

if (isset($_POST['test_sample_ips'])) {
    $test_results = ['multiple' => []];
    
    foreach ($sample_ips as $ip => $description) {
        if (filter_var($ip, FILTER_VALIDATE_IP)) {
            $ipInfo = checkIPQuality($ip);
            $expected = 100 - ($ipInfo['fraud_score'] ?? 0);
            
            $test_results['multiple'][] = [
                'ip' => $ip,
                'description' => $description,
                'fraud_score' => $ipInfo['fraud_score'] ?? 0,
                'expected_score' => $expected,
                'actual_score' => $ipInfo['quality_score'],
                'is_correct' => abs($expected - $ipInfo['quality_score']) <= 5, // هامش خطأ 5 نقاط
                'country' => $ipInfo['country'],
                'is_proxy' => $ipInfo['is_proxy'],
                'is_vpn' => $ipInfo['is_vpn']
            ];
        }
    }
    
    $message = "✅ تم اختبار " . count($test_results['multiple']) . " عنوان IP!";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح درجة IP - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info-box {
            background: #fff3cd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #ffc107;
        }
        
        .info-box h3 {
            color: #856404;
            margin-bottom: 1rem;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .ip-address {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }
        
        .score-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .score-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .score-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .score-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .score-correct {
            color: #28a745;
        }
        
        .score-incorrect {
            color: #dc3545;
        }
        
        .score-warning {
            color: #ffc107;
        }
        
        .flag-item {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            margin: 0.2rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .flag-danger {
            background: #dc3545;
            color: white;
        }
        
        .flag-warning {
            background: #ffc107;
            color: #333;
        }
        
        .flag-safe {
            background: #28a745;
            color: white;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح درجة IP</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>⚠️ مشكلة درجة IP:</h3>
            <ul style="margin-right: 1.5rem;">
                <li><strong>المشكلة:</strong> درجة IP 28 بدلاً من 72 (عكس النتيجة)</li>
                <li><strong>السبب:</strong> استخدام fraud_score مباشرة بدلاً من عكسها</li>
                <li><strong>الحل:</strong> fraud_score منخفض = جودة عالية</li>
                <li><strong>المثال:</strong> fraud_score = 28 → quality_score = 72</li>
            </ul>
        </div>
        
        <div class="action-section">
            <h3>🛠️ أدوات الإصلاح</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="test_current_ip" class="btn">
                    🔍 اختبار IP الحالي
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="test_sample_ips" class="btn btn-success">
                    🌐 اختبار عدة IPs
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="fix_ip_scores" class="btn btn-warning">
                    🔧 إصلاح درجات قاعدة البيانات
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_results)): ?>
        <div class="results-section">
            <h3 style="color: #333; margin-bottom: 1rem;">📊 نتائج الاختبار</h3>
            
            <?php if (isset($test_results['multiple'])): ?>
                <?php foreach ($test_results['multiple'] as $result): ?>
                    <div class="result-card">
                        <div class="result-header">
                            <div>
                                <div class="ip-address"><?php echo htmlspecialchars($result['ip']); ?></div>
                                <div style="color: #666; font-size: 0.9rem;"><?php echo htmlspecialchars($result['description']); ?></div>
                            </div>
                            <div>
                                <?php if ($result['is_correct']): ?>
                                    <span class="flag-item flag-safe">✅ صحيح</span>
                                <?php else: ?>
                                    <span class="flag-item flag-danger">❌ خطأ</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="score-comparison">
                            <div class="score-item">
                                <div class="score-label">نقاط الاحتيال</div>
                                <div class="score-value"><?php echo $result['fraud_score']; ?>%</div>
                            </div>
                            
                            <div class="score-item">
                                <div class="score-label">النتيجة المتوقعة</div>
                                <div class="score-value score-correct"><?php echo $result['expected_score']; ?>%</div>
                            </div>
                            
                            <div class="score-item">
                                <div class="score-label">النتيجة الحالية</div>
                                <div class="score-value <?php echo $result['is_correct'] ? 'score-correct' : 'score-incorrect'; ?>">
                                    <?php echo $result['actual_score']; ?>%
                                </div>
                            </div>
                            
                            <div class="score-item">
                                <div class="score-label">الدولة</div>
                                <div class="score-value" style="font-size: 1rem;"><?php echo htmlspecialchars($result['country']); ?></div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 1rem;">
                            <?php if ($result['is_proxy']): ?>
                                <span class="flag-item flag-danger">🔴 بروكسي</span>
                            <?php endif; ?>
                            
                            <?php if ($result['is_vpn']): ?>
                                <span class="flag-item flag-warning">🟡 VPN</span>
                            <?php endif; ?>
                            
                            <?php if (!$result['is_proxy'] && !$result['is_vpn']): ?>
                                <span class="flag-item flag-safe">✅ آمن</span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
                
            <?php else: ?>
                <div class="result-card">
                    <div class="result-header">
                        <div class="ip-address"><?php echo htmlspecialchars($test_results['ip']); ?></div>
                    </div>
                    
                    <div class="score-comparison">
                        <div class="score-item">
                            <div class="score-label">نقاط الاحتيال</div>
                            <div class="score-value"><?php echo $test_results['current_system']['fraud_score'] ?? 0; ?>%</div>
                        </div>
                        
                        <div class="score-item">
                            <div class="score-label">النتيجة المتوقعة</div>
                            <div class="score-value score-correct"><?php echo $test_results['expected_score']; ?>%</div>
                        </div>
                        
                        <div class="score-item">
                            <div class="score-label">النتيجة الحالية</div>
                            <div class="score-value <?php echo abs($test_results['expected_score'] - $test_results['actual_score']) <= 5 ? 'score-correct' : 'score-incorrect'; ?>">
                                <?php echo $test_results['actual_score']; ?>%
                            </div>
                        </div>
                        
                        <div class="score-item">
                            <div class="score-label">الدولة</div>
                            <div class="score-value" style="font-size: 1rem;"><?php echo htmlspecialchars($test_results['current_system']['country']); ?></div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="test_ip_quality.php">🔍 اختبار IP Quality</a>
            <a href="comprehensive_sync.php">🌟 المزامنة الشاملة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
