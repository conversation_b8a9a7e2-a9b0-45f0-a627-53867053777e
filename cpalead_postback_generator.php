<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';

// معالجة إنشاء رابط Postback
if (isset($_POST['generate_postback'])) {
    $site_url = trim($_POST['site_url']);
    $postback_password = trim($_POST['postback_password']);
    $include_all_params = isset($_POST['include_all_params']);
    
    if (!empty($site_url)) {
        $base_url = rtrim($site_url, '/') . '/postback_cpalead.php';
        
        if ($include_all_params) {
            // رابط Postback مع جميع المعاملات
            $postback_url = $base_url . '?';
            $postback_url .= 'campaign_id={campaign_id}';
            $postback_url .= '&campaign_name={campaign_name}';
            $postback_url .= '&subid={subid}';
            $postback_url .= '&subid2={subid2}';
            $postback_url .= '&subid3={subid3}';
            $postback_url .= '&idfa={idfa}';
            $postback_url .= '&gaid={gaid}';
            $postback_url .= '&payout={payout}';
            $postback_url .= '&ip_address={ip_address}';
            $postback_url .= '&gateway_id={gateway_id}';
            $postback_url .= '&lead_id={lead_id}';
            $postback_url .= '&country_iso={country_iso}';
            $postback_url .= '&virtual_currency={virtual_currency}';
            
            if (!empty($postback_password)) {
                $postback_url .= '&password={password}';
            }
        } else {
            // رابط Postback أساسي
            $postback_url = $base_url . '?subid={subid}&ip_address={ip_address}&payout={payout}';
            
            if (!empty($postback_password)) {
                $postback_url .= '&password={password}';
            }
        }
        
        $message = '<div class="alert alert-success">✅ تم إنشاء رابط Postback بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال رابط الموقع!</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد Postback لـ CPALead - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input[type="text"], .form-group input[type="url"] {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 1rem 0;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-left: 0.5rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .postback-result {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .postback-url {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin: 0.5rem 0;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        
        .params-table th, .params-table td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .params-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .params-table tr:hover {
            background: #f8f9fa;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 مولد Postback لـ CPALead</h1>
            <p>إنشاء روابط Postback متوافقة مع جميع معاملات CPALead</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="test_postback.php">🧪 اختبار Postback</a>
                <a href="conversions.php">📊 التحويلات</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- نموذج إنشاء Postback -->
        <div class="form-section">
            <h3>🔗 إنشاء رابط Postback</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="site_url">🌐 رابط الموقع:</label>
                    <input type="url" id="site_url" name="site_url" placeholder="https://yoursite.com" required>
                </div>
                
                <div class="form-group">
                    <label for="postback_password">🔒 كلمة مرور Postback (اختياري):</label>
                    <input type="text" id="postback_password" name="postback_password" placeholder="كلمة مرور للأمان">
                </div>
                
                <div class="checkbox-group">
                    <label for="include_all_params">تضمين جميع معاملات CPALead</label>
                    <input type="checkbox" id="include_all_params" name="include_all_params" checked>
                </div>
                
                <button type="submit" name="generate_postback" class="btn">🔗 إنشاء رابط Postback</button>
            </form>
        </div>
        
        <!-- عرض رابط Postback -->
        <?php if (isset($postback_url)): ?>
        <div class="form-section">
            <h3>📋 رابط Postback المُنشأ</h3>
            
            <div class="postback-result">
                <h4>🔗 رابط Postback لـ CPALead:</h4>
                <div class="postback-url" id="postback-url"><?php echo htmlspecialchars($postback_url); ?></div>
                <button class="copy-btn" onclick="copyToClipboard('postback-url')">📋 نسخ الرابط</button>
            </div>
            
            <div class="alert alert-info">
                <h4>📋 خطوات الإعداد في CPALead:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li>اذهب إلى لوحة تحكم CPALead</li>
                    <li>انتقل إلى "Postback" → "Configuration"</li>
                    <li>فعّل Postback وألصق الرابط أعلاه</li>
                    <li>احفظ الإعدادات</li>
                    <li>اختبر Postback للتأكد من عمله</li>
                </ol>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- معاملات CPALead -->
        <div class="form-section">
            <h3>📊 معاملات CPALead المدعومة</h3>
            
            <table class="params-table">
                <thead>
                    <tr>
                        <th>المعامل</th>
                        <th>الوصف</th>
                        <th>مثال</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{campaign_id}</td>
                        <td>معرف الحملة</td>
                        <td>1234</td>
                    </tr>
                    <tr>
                        <td>{campaign_name}</td>
                        <td>اسم الحملة</td>
                        <td>Mobile App Install</td>
                    </tr>
                    <tr>
                        <td>{subid}</td>
                        <td>معرف التتبع الأساسي</td>
                        <td>user123</td>
                    </tr>
                    <tr>
                        <td>{subid2}</td>
                        <td>معرف التتبع الثانوي</td>
                        <td>source_fb</td>
                    </tr>
                    <tr>
                        <td>{subid3}</td>
                        <td>معرف التتبع الإضافي</td>
                        <td>campaign_summer</td>
                    </tr>
                    <tr>
                        <td>{idfa}</td>
                        <td>Apple IDFA للتطبيقات</td>
                        <td>XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX</td>
                    </tr>
                    <tr>
                        <td>{gaid}</td>
                        <td>Google GAID للتطبيقات</td>
                        <td>XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX</td>
                    </tr>
                    <tr>
                        <td>{payout}</td>
                        <td>مبلغ العمولة</td>
                        <td>2.50</td>
                    </tr>
                    <tr>
                        <td>{ip_address}</td>
                        <td>عنوان IP المستخدم</td>
                        <td>*************</td>
                    </tr>
                    <tr>
                        <td>{gateway_id}</td>
                        <td>معرف البوابة</td>
                        <td>gate123</td>
                    </tr>
                    <tr>
                        <td>{lead_id}</td>
                        <td>معرف العميل المحتمل</td>
                        <td>lead456</td>
                    </tr>
                    <tr>
                        <td>{country_iso}</td>
                        <td>رمز البلد</td>
                        <td>US</td>
                    </tr>
                    <tr>
                        <td>{password}</td>
                        <td>كلمة مرور Postback</td>
                        <td>secret123</td>
                    </tr>
                    <tr>
                        <td>{virtual_currency}</td>
                        <td>العملة الافتراضية</td>
                        <td>100</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="form-section">
            <h3>📚 معلومات مهمة</h3>
            
            <div class="info-box">
                <h4>🔒 الأمان:</h4>
                <ul>
                    <li>يتم فحص IP المصدر (************) للتأكد من أن Postback من CPALead</li>
                    <li>يمكن إضافة كلمة مرور إضافية للأمان</li>
                    <li>جميع البيانات يتم تسجيلها في قاعدة البيانات</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>📊 التتبع:</h4>
                <ul>
                    <li>يتم ربط كل تحويل باسم المستخدم تلقائياً</li>
                    <li>حفظ جميع معاملات CPALead للتحليل المفصل</li>
                    <li>سجلات مفصلة لجميع التحويلات والأخطاء</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>🧪 الاختبار:</h4>
                <ul>
                    <li>استخدم <a href="test_postback.php">صفحة اختبار Postback</a> للتأكد من العمل</li>
                    <li>تحقق من سجلات التحويلات في <a href="conversions.php">صفحة التحويلات</a></li>
                    <li>راجع ملفات السجل للتأكد من استقبال البيانات</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ الرابط بنجاح!');
            }, function(err) {
                console.error('فشل في نسخ النص: ', err);
            });
        }
    </script>
</body>
</html>
