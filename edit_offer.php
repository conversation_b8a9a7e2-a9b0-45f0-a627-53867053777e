<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;
$offer = null;

// جلب معرف العرض
$offer_id = intval($_GET['id'] ?? 0);

if ($offer_id <= 0) {
    header('Location: manage_offers.php');
    exit;
}

// جلب بيانات العرض
try {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT * FROM offers WHERE id = ?");
    $stmt->bind_param("i", $offer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Location: manage_offers.php');
        exit;
    }
    
    $offer = $result->fetch_assoc();
    $stmt->close();
    $conn->close();
    
} catch (Exception $e) {
    $message = "❌ خطأ في جلب بيانات العرض: " . $e->getMessage();
}

// معالجة تحديث العرض
if (isset($_POST['update_offer']) && $offer) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $image_url = trim($_POST['image_url']);
    $offer_url = trim($_POST['offer_url']);
    $countries = trim($_POST['countries']);
    $device = trim($_POST['device']);
    $amount = floatval($_POST['amount']);
    $payout_type = trim($_POST['payout_type']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // التحقق من البيانات المطلوبة
    if (empty($title)) {
        $message = "❌ عنوان العرض مطلوب!";
    } elseif (empty($offer_url)) {
        $message = "❌ رابط العرض مطلوب!";
    } elseif (!filter_var($offer_url, FILTER_VALIDATE_URL)) {
        $message = "❌ رابط العرض غير صحيح!";
    } else {
        // التحقق من صحة رابط الصورة
        if (!empty($image_url) && !filter_var($image_url, FILTER_VALIDATE_URL)) {
            $encodedTitle = urlencode(substr($title, 0, 30));
            $image_url = "https://via.placeholder.com/400x250/667eea/ffffff?text=" . $encodedTitle;
        }
        
        try {
            $conn = getDBConnection();
            
            // التحقق من وجود حقل الوصف
            if (hasDescriptionField()) {
                $stmt = $conn->prepare("UPDATE offers SET title = ?, description = ?, image_url = ?, offer_url = ?, countries = ?, device = ?, amount = ?, payout_type = ?, is_active = ?, updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("ssssssdsii", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $offer_id);
            } else {
                $stmt = $conn->prepare("UPDATE offers SET title = ?, image_url = ?, offer_url = ?, countries = ?, device = ?, amount = ?, payout_type = ?, is_active = ?, updated_at = NOW() WHERE id = ?");
                $stmt->bind_param("sssssdsii", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $offer_id);
            }
            
            if ($stmt->execute()) {
                $success = true;
                $message = "✅ تم تحديث العرض بنجاح!";
                
                // تحديث البيانات المحلية
                $offer['title'] = $title;
                $offer['description'] = $description;
                $offer['image_url'] = $image_url;
                $offer['offer_url'] = $offer_url;
                $offer['countries'] = $countries;
                $offer['device'] = $device;
                $offer['amount'] = $amount;
                $offer['payout_type'] = $payout_type;
                $offer['is_active'] = $is_active;
                
                // إضافة إشعار
                if (function_exists('addNotification')) {
                    addNotification(
                        'offer_updated',
                        'تم تحديث عرض',
                        "تم تحديث العرض: $title",
                        ['offer_id' => $offer_id, 'title' => $title]
                    );
                }
            } else {
                $message = "❌ فشل في تحديث العرض!";
            }
            
            $stmt->close();
            $conn->close();
            
        } catch (Exception $e) {
            $message = "❌ خطأ في تحديث العرض: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحرير العرض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: inherit;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group small {
            color: #666;
            font-size: 0.8rem;
            margin-top: 0.3rem;
            display: block;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .offer-info {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .offer-info h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .info-label {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 0.3rem;
        }
        
        .info-value {
            font-weight: bold;
            color: #333;
        }
        
        .required {
            color: #dc3545;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✏️ تحرير العرض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($offer): ?>
        <div class="offer-info">
            <h3>📊 معلومات العرض</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">معرف العرض</div>
                    <div class="info-value">#<?php echo $offer['id']; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">تاريخ الإنشاء</div>
                    <div class="info-value"><?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">آخر تحديث</div>
                    <div class="info-value"><?php echo isset($offer['updated_at']) ? date('Y-m-d H:i', strtotime($offer['updated_at'])) : 'لم يتم التحديث'; ?></div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">نوع العرض</div>
                    <div class="info-value"><?php echo empty($offer['external_id']) ? 'يدوي' : 'من API'; ?></div>
                </div>
            </div>
        </div>
        
        <div class="form-section">
            <h3 style="margin-bottom: 1.5rem; color: #333;">📝 تحرير بيانات العرض</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="title">عنوان العرض <span class="required">*</span></label>
                    <input type="text" id="title" name="title" required 
                           value="<?php echo htmlspecialchars($offer['title']); ?>">
                    <small>عنوان جذاب ووصفي للعرض</small>
                </div>
                
                <?php if (hasDescriptionField()): ?>
                <div class="form-group">
                    <label for="description">وصف العرض</label>
                    <textarea id="description" name="description"><?php echo htmlspecialchars($offer['description'] ?? ''); ?></textarea>
                    <small>وصف مفصل يشرح العرض ومتطلباته</small>
                </div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label for="offer_url">رابط العرض <span class="required">*</span></label>
                    <input type="url" id="offer_url" name="offer_url" required 
                           value="<?php echo htmlspecialchars($offer['offer_url']); ?>">
                    <small>الرابط الذي سيتم توجيه المستخدمين إليه</small>
                </div>
                
                <div class="form-group">
                    <label for="image_url">رابط صورة العرض</label>
                    <input type="url" id="image_url" name="image_url" 
                           value="<?php echo htmlspecialchars($offer['image_url']); ?>">
                    <small>رابط صورة العرض</small>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">المبلغ ($)</label>
                        <input type="number" id="amount" name="amount" step="0.01" min="0"
                               value="<?php echo $offer['amount']; ?>">
                        <small>مبلغ العمولة بالدولار</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="payout_type">نوع العمولة</label>
                        <select id="payout_type" name="payout_type">
                            <option value="CPI" <?php echo $offer['payout_type'] == 'CPI' ? 'selected' : ''; ?>>CPI - تكلفة التثبيت</option>
                            <option value="CPA" <?php echo $offer['payout_type'] == 'CPA' ? 'selected' : ''; ?>>CPA - تكلفة الإجراء</option>
                            <option value="CPL" <?php echo $offer['payout_type'] == 'CPL' ? 'selected' : ''; ?>>CPL - تكلفة العميل المحتمل</option>
                            <option value="CPS" <?php echo $offer['payout_type'] == 'CPS' ? 'selected' : ''; ?>>CPS - تكلفة البيع</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="countries">البلدان المدعومة</label>
                        <input type="text" id="countries" name="countries" 
                               value="<?php echo htmlspecialchars($offer['countries']); ?>">
                        <small>رموز البلدان مفصولة بفواصل، أو فارغ لجميع البلدان</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="device">الجهاز المدعوم</label>
                        <select id="device" name="device">
                            <option value="all" <?php echo $offer['device'] == 'all' ? 'selected' : ''; ?>>جميع الأجهزة</option>
                            <option value="mobile" <?php echo $offer['device'] == 'mobile' ? 'selected' : ''; ?>>الهاتف المحمول</option>
                            <option value="desktop" <?php echo $offer['device'] == 'desktop' ? 'selected' : ''; ?>>سطح المكتب</option>
                            <option value="tablet" <?php echo $offer['device'] == 'tablet' ? 'selected' : ''; ?>>الجهاز اللوحي</option>
                            <option value="android" <?php echo $offer['device'] == 'android' ? 'selected' : ''; ?>>أندرويد</option>
                            <option value="ios" <?php echo $offer['device'] == 'ios' ? 'selected' : ''; ?>>iOS</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="is_active" name="is_active" <?php echo $offer['is_active'] ? 'checked' : ''; ?>>
                        <label for="is_active">العرض نشط</label>
                    </div>
                    <small>قم بإلغاء التحديد لإخفاء العرض من الصفحة الرئيسية</small>
                </div>
                
                <div style="text-align: center;">
                    <button type="submit" name="update_offer" class="btn btn-success">
                        ✅ حفظ التغييرات
                    </button>
                    
                    <a href="manage_offers.php" class="btn btn-secondary">
                        ↩️ العودة للقائمة
                    </a>
                </div>
            </form>
        </div>
        
        <?php else: ?>
        <div style="text-align: center; padding: 3rem; color: #666;">
            <h3>العرض غير موجود</h3>
            <p>لم يتم العثور على العرض المطلوب.</p>
            <a href="manage_offers.php" class="btn">العودة لإدارة العروض</a>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="manage_offers.php">📋 إدارة العروض</a>
            <a href="add_manual_offer.php">➕ إضافة عرض جديد</a>
            <a href="api_manager.php">📡 لوحة التحكم</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
