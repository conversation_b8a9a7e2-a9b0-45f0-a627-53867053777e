<?php
require_once 'config.php';
require_once 'countries.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة تغيير حالة العرض
if (isset($_POST['toggle_status'])) {
    $offer_id = (int)$_POST['offer_id'];
    $new_status = (int)$_POST['new_status'];
    
    $updateStmt = $conn->prepare("UPDATE offers SET is_active = ?, updated_at = NOW() WHERE id = ?");
    $updateStmt->bind_param("ii", $new_status, $offer_id);
    
    if ($updateStmt->execute()) {
        $status_text = $new_status ? 'تفعيل' : 'إيقاف';
        $message = '<div class="alert alert-success">✅ تم ' . $status_text . ' العرض بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في تحديث حالة العرض!</div>';
    }
    $updateStmt->close();
}

// معالجة حذف العرض
if (isset($_POST['delete_offer'])) {
    $offer_id = (int)$_POST['offer_id'];
    
    $deleteStmt = $conn->prepare("DELETE FROM offers WHERE id = ?");
    $deleteStmt->bind_param("i", $offer_id);
    
    if ($deleteStmt->execute()) {
        $message = '<div class="alert alert-success">✅ تم حذف العرض بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في حذف العرض!</div>';
    }
    $deleteStmt->close();
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$country_filter = isset($_GET['country']) ? $_GET['country'] : '';

// بناء استعلام البحث
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "title LIKE ?";
    $params[] = "%$search%";
    $types .= 's';
}

if ($status_filter === 'active') {
    $where_conditions[] = "is_active = 1";
} elseif ($status_filter === 'inactive') {
    $where_conditions[] = "is_active = 0";
}

if (!empty($country_filter)) {
    $where_conditions[] = "(countries LIKE ? OR countries IS NULL OR countries = '')";
    $params[] = "%$country_filter%";
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// الحصول على العروض
$sql = "
    SELECT o.*, 
           COUNT(DISTINCT c.id) as clicks_count,
           COUNT(DISTINCT conv.id) as conversions_count,
           MAX(c.clicked_at) as last_click
    FROM offers o
    LEFT JOIN clicks c ON o.id = c.offer_id
    LEFT JOIN conversions conv ON o.id = conv.offer_id
    $where_clause
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT 50
";

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$offers = $stmt->get_result();

// الحصول على إحصائيات
$stats = $conn->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive
    FROM offers
")->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .filters-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .form-group {
            margin-bottom: 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .offer-title {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-active {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .stats-badge {
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 0.1rem;
        }
        
        .actions-cell {
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .table-container {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 إدارة العروض</h1>
            <p>عرض وإدارة جميع العروض النشطة والمتوقفة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="sync_manager.php">🔄 المزامنة التلقائية</a>
                <a href="countries_stats.php">📊 إحصائيات البلدان</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['active']; ?></div>
                <div class="stats-label">✅ عروض نشطة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['inactive']; ?></div>
                <div class="stats-label">❌ عروض متوقفة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $offers->num_rows; ?></div>
                <div class="stats-label">🔍 نتائج البحث</div>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="filters-section">
            <form method="GET" class="filters-form">
                <div class="form-group">
                    <label for="search">🔍 البحث في العناوين:</label>
                    <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="ابحث في عناوين العروض...">
                </div>
                
                <div class="form-group">
                    <label for="status">📊 حالة العرض:</label>
                    <select id="status" name="status">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع العروض</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>عروض نشطة</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>عروض متوقفة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="country">🌍 البلد:</label>
                    <select id="country" name="country">
                        <option value="">جميع البلدان</option>
                        <?php 
                        $popularCountries = getPopularCPACountries();
                        foreach ($popularCountries as $code => $name) {
                            $selected = $country_filter === $code ? 'selected' : '';
                            echo "<option value='$code' $selected>$name</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">🔍 بحث</button>
                    <a href="offers_manager.php" class="btn btn-warning">🔄 إعادة تعيين</a>
                </div>
            </form>
        </div>
        
        <!-- جدول العروض -->
        <div class="offers-table">
            <h3>📋 العروض (<?php echo $offers->num_rows; ?> عرض)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>العنوان</th>
                            <th>البلدان</th>
                            <th>الجهاز</th>
                            <th>المبلغ</th>
                            <th>الحالة</th>
                            <th>الإحصائيات</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($offer = $offers->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo $offer['id']; ?></td>
                                <td class="offer-title" title="<?php echo htmlspecialchars($offer['title']); ?>">
                                    <?php echo htmlspecialchars(substr($offer['title'], 0, 50)); ?>
                                    <?php if (strlen($offer['title']) > 50) echo '...'; ?>
                                </td>
                                <td style="font-size: 0.8rem;">
                                    <?php echo formatCountriesDisplay($offer['countries'], 2); ?>
                                </td>
                                <td>
                                    <?php if ($offer['device']): ?>
                                        <span style="background: #17a2b8; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                            <?php echo ucfirst($offer['device']); ?>
                                        </span>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($offer['amount'] > 0): ?>
                                        <span style="background: #ffc107; color: #333; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">
                                            $<?php echo $offer['amount']; ?> <?php echo $offer['payout_type']; ?>
                                        </span>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($offer['is_active']): ?>
                                        <span class="status-active">نشط</span>
                                    <?php else: ?>
                                        <span class="status-inactive">متوقف</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="stats-badge"><?php echo $offer['clicks_count']; ?> ضغطة</span>
                                    <span class="stats-badge"><?php echo $offer['conversions_count']; ?> تحويل</span>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($offer['created_at'])); ?></td>
                                <td class="actions-cell">
                                    <!-- تغيير الحالة -->
                                    <form style="display: inline;" method="POST">
                                        <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                        <input type="hidden" name="new_status" value="<?php echo $offer['is_active'] ? 0 : 1; ?>">
                                        <button type="submit" name="toggle_status" class="btn btn-sm <?php echo $offer['is_active'] ? 'btn-warning' : 'btn-success'; ?>">
                                            <?php echo $offer['is_active'] ? '⏸️ إيقاف' : '▶️ تفعيل'; ?>
                                        </button>
                                    </form>
                                    
                                    <!-- حذف العرض -->
                                    <form style="display: inline;" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                        <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                        <button type="submit" name="delete_offer" class="btn btn-sm btn-danger">🗑️ حذف</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>

<?php $conn->close(); ?>
