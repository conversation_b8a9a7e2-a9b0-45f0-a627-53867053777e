# إعداد موقع CPA على InfinityFree 🚀

## 📋 بيانات قاعدة البيانات المُحدثة:
- **الخادم**: `sql303.infinityfree.com`
- **اسم المستخدم**: `if0_39395085`
- **كلمة المرور**: `Qweeee12`
- **اسم قاعدة البيانات**: `if0_39395085_q12`

## 🔧 خطوات الإعداد على InfinityFree:

### 1. رفع الملفات
1. قم بتسجيل الدخول إلى لوحة تحكم InfinityFree
2. اذهب إلى **File Manager** أو استخدم FTP
3. ارفع جميع ملفات PHP إلى مجلد `htdocs` أو `public_html`

### 2. إعداد قاعدة البيانات
1. اذهب إلى **MySQL Databases** في لوحة التحكم
2. انسخ محتوى ملف `database.sql` والصقه في **phpMyAdmin**
3. قم بتشغيل الاستعلام لإنشاء الجداول

### 3. التحقق من الإعدادات
تأكد من أن ملف `config.php` يحتوي على الإعدادات الصحيحة:
```php
define('DB_HOST', 'sql303.infinityfree.com');
define('DB_USER', 'if0_39395085');
define('DB_PASS', 'Qweeee12');
define('DB_NAME', 'if0_39395085_q12');
```

## ⚠️ ملاحظات مهمة للاستضافة المجانية:

### 🚫 القيود المحتملة:
- **حد الاستعلامات**: قد تكون هناك قيود على عدد استعلامات قاعدة البيانات
- **الذاكرة**: قيود على استخدام الذاكرة
- **المعالجة**: قيود على وقت تنفيذ السكريبت
- **الملفات**: قيود على حجم وعدد الملفات

### 🔧 تحسينات للاستضافة المجانية:

#### 1. تحسين الاتصال بقاعدة البيانات
أضف هذا الكود في بداية `config.php`:
```php
// تحسينات للاستضافة المجانية
ini_set('mysql.connect_timeout', 60);
ini_set('default_socket_timeout', 60);
```

#### 2. معالجة الأخطاء
```php
// في حالة فشل الاتصال، حاول مرة أخرى
function getDBConnection($retry = 3) {
    for ($i = 0; $i < $retry; $i++) {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if (!$conn->connect_error) {
            $conn->set_charset("utf8mb4");
            return $conn;
        }
        sleep(1); // انتظار ثانية واحدة قبل المحاولة مرة أخرى
    }
    die("فشل الاتصال بقاعدة البيانات بعد $retry محاولات");
}
```

### 📊 مراقبة الأداء:
- راقب استخدام قاعدة البيانات من لوحة التحكم
- تحقق من سجلات الأخطاء بانتظام
- استخدم التخزين المؤقت عند الإمكان

## 🌐 روابط الموقع المتوقعة:
بعد الرفع، ستكون الروابط كالتالي:
- **الصفحة الرئيسية**: `http://yoursubdomain.infinityfreeapp.com/`
- **لوحة التحكم**: `http://yoursubdomain.infinityfreeapp.com/admin.php`
- **التحويلات**: `http://yoursubdomain.infinityfreeapp.com/conversions.php`
- **Postback**: `http://yoursubdomain.infinityfreeapp.com/postback.php`

## 🔍 اختبار الموقع:

### 1. اختبار الاتصال بقاعدة البيانات:
قم بإنشاء ملف `test_db.php`:
```php
<?php
require_once 'config.php';
$conn = getDBConnection();
if ($conn) {
    echo "✅ الاتصال بقاعدة البيانات نجح!";
    $conn->close();
} else {
    echo "❌ فشل الاتصال بقاعدة البيانات!";
}
?>
```

### 2. اختبار العروض:
- اذهب إلى الصفحة الرئيسية
- تحقق من ظهور العروض التجريبية
- جرب الضغط على عرض

### 3. اختبار لوحة التحكم:
- اذهب إلى `/admin.php`
- استخدم كلمة المرور: `s`
- جرب إضافة عرض جديد

### 4. اختبار Postback:
```
http://yoursite.com/postback.php?offer_id=1&ip=***********
```

## 🛠️ استكشاف الأخطاء الشائعة:

### خطأ "Connection refused":
- تحقق من صحة بيانات قاعدة البيانات
- تأكد من أن قاعدة البيانات نشطة

### خطأ "Database not found":
- تأكد من إنشاء قاعدة البيانات في phpMyAdmin
- تحقق من اسم قاعدة البيانات

### خطأ "Access denied":
- تحقق من اسم المستخدم وكلمة المرور
- تأكد من صلاحيات المستخدم

## 📞 الدعم:
- **InfinityFree Support**: للمشاكل المتعلقة بالاستضافة
- **phpMyAdmin**: لإدارة قاعدة البيانات
- **File Manager**: لإدارة الملفات

---

**نصيحة**: احتفظ بنسخة احتياطية من الملفات وقاعدة البيانات بانتظام! 💾
