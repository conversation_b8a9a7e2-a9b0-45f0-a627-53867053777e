<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';
$success = false;

try {
    // التحقق من وجود الحقل أولاً
    $check_column = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
    
    if ($check_column->num_rows == 0) {
        // إضافة حقل الوصف
        $sql = "ALTER TABLE offers ADD COLUMN description TEXT COLLATE utf8mb4_unicode_ci AFTER title";
        
        if ($conn->query($sql)) {
            $message = "✅ تم إضافة حقل الوصف بنجاح!";
            $success = true;
        } else {
            $message = "❌ فشل في إضافة حقل الوصف: " . $conn->error;
        }
    } else {
        $message = "✅ حقل الوصف موجود مسبقاً!";
        $success = true;
    }
} catch (Exception $e) {
    $message = "❌ خطأ: " . $e->getMessage();
}

// إذا تم إضافة الحقل بنجاح، قم بتحديث العروض الموجودة
if ($success) {
    try {
        // تحديث العروض التي لا تحتوي على وصف
        $update_sql = "UPDATE offers SET description = CONCAT('عرض من CPALead - ', title) WHERE description IS NULL OR description = ''";
        $conn->query($update_sql);
        
        $message .= "<br>✅ تم تحديث العروض الموجودة بوصف افتراضي!";
    } catch (Exception $e) {
        $message .= "<br>⚠️ تحذير: " . $e->getMessage();
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح حقل الوصف - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 600px;
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .message {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .nav-links {
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.8rem 1.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .refresh-btn {
            background: #28a745;
        }
        
        .refresh-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon <?php echo $success ? 'success' : 'error'; ?>">
            <?php echo $success ? '✅' : '❌'; ?>
        </div>
        
        <h1>إصلاح حقل الوصف</h1>
        
        <div class="message">
            <?php echo $message; ?>
        </div>
        
        <?php if ($success): ?>
            <div style="background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <strong>🎉 تم الإصلاح بنجاح!</strong><br>
                يمكنك الآن استيراد العروض من CPALead مع الوصف.
            </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="api_manager.php">🔧 إدارة API</a>
            <a href="add_test_offers.php" class="refresh-btn">🧪 إضافة عروض تجريبية</a>
        </div>
        
        <?php if ($success): ?>
            <script>
                // إعادة توجيه تلقائي بعد 3 ثواني
                setTimeout(function() {
                    window.location.href = 'index.php';
                }, 3000);
            </script>
        <?php endif; ?>
    </div>
</body>
</html>
