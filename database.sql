-- <PERSON><PERSON><PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS if0_39395085_q12 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE if0_39395085_q12;

-- جد<PERSON><PERSON> العروض
CREATE TABLE IF NOT EXISTS offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL COLLATE utf8mb4_unicode_ci,
    image_url VARCHAR(500) NOT NULL COLLATE utf8mb4_unicode_ci,
    offer_url VARCHAR(500) NOT NULL COLLATE utf8mb4_unicode_ci,
    countries TEXT COLLATE utf8mb4_unicode_ci,
    device VARCHAR(50) COLLATE utf8mb4_unicode_ci,
    amount DECIMAL(10,2) DEFAULT 0,
    payout_type VARCHAR(20) COLLATE utf8mb4_unicode_ci,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_created (created_at),
    INDEX idx_updated (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الضغطات
CREATE TABLE IF NOT EXISTS clicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    offer_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL COLLATE utf8mb4_unicode_ci,
    username VARCHAR(50) COLLATE utf8mb4_unicode_ci,
    source VARCHAR(100) DEFAULT 'direct' COLLATE utf8mb4_unicode_ci,
    user_agent TEXT COLLATE utf8mb4_unicode_ci,
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    INDEX idx_offer_ip_time (offer_id, ip_address, clicked_at),
    INDEX idx_username (username),
    INDEX idx_source (source)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول التحويلات
CREATE TABLE IF NOT EXISTS conversions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    offer_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL COLLATE utf8mb4_unicode_ci,
    username VARCHAR(50) COLLATE utf8mb4_unicode_ci,
    source VARCHAR(100) DEFAULT 'direct' COLLATE utf8mb4_unicode_ci,
    user_agent TEXT COLLATE utf8mb4_unicode_ci,
    converted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
    INDEX idx_offer_ip_time (offer_id, ip_address, converted_at),
    INDEX idx_username (username),
    INDEX idx_source (source)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول حفظ الـ IP
CREATE TABLE IF NOT EXISTS saved_ips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL UNIQUE COLLATE utf8mb4_unicode_ci,
    username VARCHAR(50) NOT NULL COLLATE utf8mb4_unicode_ci,
    country VARCHAR(100) COLLATE utf8mb4_unicode_ci,
    city VARCHAR(100) COLLATE utf8mb4_unicode_ci,
    isp VARCHAR(200) COLLATE utf8mb4_unicode_ci,
    is_proxy BOOLEAN DEFAULT FALSE,
    is_vpn BOOLEAN DEFAULT FALSE,
    quality_score INT DEFAULT 0,
    saved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_ip (ip_address),
    INDEX idx_username (username),
    INDEX idx_saved_at (saved_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج بعض العروض التجريبية
INSERT INTO offers (title, image_url, offer_url) VALUES
('عرض خاص - اربح 100$ يومياً', 'https://via.placeholder.com/300x200/4CAF50/white?text=عرض+1', 'https://example.com/offer1'),
('استثمار مضمون - عائد 200%', 'https://via.placeholder.com/300x200/2196F3/white?text=عرض+2', 'https://example.com/offer2'),
('تطبيق جديد - احصل على مكافآت', 'https://via.placeholder.com/300x200/FF9800/white?text=عرض+3', 'https://example.com/offer3'),
('موقع تسوق - خصم 50%', 'https://via.placeholder.com/300x200/E91E63/white?text=عرض+4', 'https://example.com/offer4');
