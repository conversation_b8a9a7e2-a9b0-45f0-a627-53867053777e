<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$test_results = [];

// اختبار المتطلبات الأساسية
function testSyncRequirements() {
    $tests = [];
    
    // اختبار 1: ملف معرفات API
    if (file_exists('api_ids.txt')) {
        $content = file_get_contents('api_ids.txt');
        $lines = array_filter(array_map('trim', explode("\n", $content)));
        $valid_apis = array_filter($lines, function($line) {
            return !empty($line) && !str_starts_with($line, '//') && !str_starts_with($line, '#');
        });
        
        if (!empty($valid_apis)) {
            $tests[] = ['name' => 'ملف معرفات API', 'status' => 'success', 'message' => count($valid_apis) . ' معرف API موجود'];
        } else {
            $tests[] = ['name' => 'ملف معرفات API', 'status' => 'warning', 'message' => 'الملف موجود لكن لا يحتوي على معرفات صالحة'];
        }
    } else {
        $tests[] = ['name' => 'ملف معرفات API', 'status' => 'error', 'message' => 'ملف api_ids.txt غير موجود'];
    }
    
    // اختبار 2: حقل الوصف
    if (hasDescriptionField()) {
        $tests[] = ['name' => 'حقل الوصف', 'status' => 'success', 'message' => 'حقل الوصف موجود في قاعدة البيانات'];
    } else {
        $tests[] = ['name' => 'حقل الوصف', 'status' => 'warning', 'message' => 'حقل الوصف غير موجود - سيتم إضافته تلقائياً'];
    }
    
    // اختبار 3: الدوال المطلوبة
    $required_functions = ['safeInsertOffer', 'extractDescription', 'ensureDescriptionField'];
    foreach ($required_functions as $func) {
        if (function_exists($func)) {
            $tests[] = ['name' => "دالة $func", 'status' => 'success', 'message' => 'متاحة'];
        } else {
            $tests[] = ['name' => "دالة $func", 'status' => 'error', 'message' => 'غير متاحة'];
        }
    }
    
    // اختبار 4: إعدادات PHP
    $php_settings = [
        'curl' => extension_loaded('curl'),
        'json' => extension_loaded('json'),
        'mysqli' => extension_loaded('mysqli')
    ];
    
    foreach ($php_settings as $setting => $available) {
        if ($available) {
            $tests[] = ['name' => "إضافة PHP: $setting", 'status' => 'success', 'message' => 'متاحة'];
        } else {
            $tests[] = ['name' => "إضافة PHP: $setting", 'status' => 'error', 'message' => 'غير متاحة'];
        }
    }
    
    // اختبار 5: صلاحيات الكتابة
    $log_file = 'sync_results.log';
    if (is_writable(dirname($log_file))) {
        $tests[] = ['name' => 'صلاحيات الكتابة', 'status' => 'success', 'message' => 'يمكن كتابة ملفات السجل'];
    } else {
        $tests[] = ['name' => 'صلاحيات الكتابة', 'status' => 'error', 'message' => 'لا يمكن كتابة ملفات السجل'];
    }
    
    return $tests;
}

// إنشاء ملف معرفات API تجريبي
if (isset($_POST['create_api_file'])) {
    $sample_content = "// ملف معرفات API من CPALead\n";
    $sample_content .= "// احصل على معرف API من: https://cpalead.com/dashboard\n";
    $sample_content .= "// أضف معرفاتك الحقيقية أدناه (سطر واحد لكل معرف)\n\n";
    $sample_content .= "// أمثلة (استبدلها بمعرفاتك الحقيقية):\n";
    $sample_content .= "// 12345\n";
    $sample_content .= "// 67890\n\n";
    $sample_content .= "// للاختبار فقط - معرف وهمي:\n";
    $sample_content .= "test123\n";
    
    if (file_put_contents('api_ids.txt', $sample_content)) {
        $message = "✅ تم إنشاء ملف api_ids.txt بنجاح!";
    } else {
        $message = "❌ فشل في إنشاء ملف api_ids.txt!";
    }
}

// تشغيل الاختبارات
if (isset($_POST['run_tests'])) {
    $test_results = testSyncRequirements();
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($test_results as $test) {
        if ($test['status'] == 'success') {
            $success_count++;
        } elseif ($test['status'] == 'error') {
            $error_count++;
        }
    }
    
    if ($error_count == 0) {
        $message = "✅ جميع الاختبارات نجحت! المزامنة جاهزة للتشغيل.";
    } else {
        $message = "⚠️ يوجد {$error_count} مشكلة تحتاج إلى إصلاح قبل تشغيل المزامنة.";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المزامنة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .test-card {
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .test-card.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-card.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-card.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .test-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .test-status {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .test-message {
            color: #666;
            font-size: 0.9rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار المزامنة</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 متطلبات المزامنة:</h3>
            <ul style="margin-right: 1.5rem;">
                <li>ملف api_ids.txt يحتوي على معرفات API صالحة من CPALead</li>
                <li>حقل الوصف في جدول العروض (يتم إضافته تلقائياً)</li>
                <li>إضافات PHP: curl, json, mysqli</li>
                <li>صلاحيات كتابة ملفات السجل</li>
                <li>الدوال الآمنة للتعامل مع قاعدة البيانات</li>
            </ul>
        </div>
        
        <div class="action-section">
            <h3>🛠️ أدوات الاختبار</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="run_tests" class="btn btn-success">
                    🧪 تشغيل اختبارات المزامنة
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_api_file" class="btn btn-warning">
                    📝 إنشاء ملف معرفات API
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_results)): ?>
        <div>
            <h3 style="color: #333; margin-bottom: 1rem;">📊 نتائج الاختبارات</h3>
            
            <div class="tests-grid">
                <?php foreach ($test_results as $test): ?>
                    <div class="test-card <?php echo $test['status']; ?>">
                        <div class="test-name">
                            <?php echo htmlspecialchars($test['name']); ?>
                        </div>
                        <div class="test-status">
                            <?php
                            switch ($test['status']) {
                                case 'success':
                                    echo '✅ نجح';
                                    break;
                                case 'error':
                                    echo '❌ فشل';
                                    break;
                                case 'warning':
                                    echo '⚠️ تحذير';
                                    break;
                                default:
                                    echo '❓ غير معروف';
                            }
                            ?>
                        </div>
                        <div class="test-message">
                            <?php echo htmlspecialchars($test['message']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="run_sync.php">🚀 تشغيل المزامنة</a>
            <a href="fix_sync_issues.php">🔧 إصلاح مشاكل المزامنة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
