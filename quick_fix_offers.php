<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$results = [];

// إصلاح سريع للمشاكل الشائعة
if (isset($_POST['quick_fix'])) {
    try {
        $conn = getDBConnection();
        $fixed = 0;
        
        // 1. إنشاء جدول الإعدادات إذا لم يكن موجود
        $settings_check = $conn->query("SHOW TABLES LIKE 'settings'");
        if ($settings_check->num_rows == 0) {
            $create_settings = "
                CREATE TABLE settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($conn->query($create_settings)) {
                $results[] = "✅ تم إنشاء جدول الإعدادات";
                $fixed++;
            }
        }
        
        // 2. إضافة إعداد وضع العروض الافتراضي
        $mode_check = $conn->query("SELECT * FROM settings WHERE setting_key = 'offers_mode'");
        if ($mode_check->num_rows == 0) {
            $insert_mode = "INSERT INTO settings (setting_key, setting_value) VALUES ('offers_mode', 'manual')";
            if ($conn->query($insert_mode)) {
                $results[] = "✅ تم تعيين وضع العروض إلى 'يدوي'";
                $fixed++;
            }
        } else {
            // تحديث الوضع إلى يدوي لعرض العروض التجريبية
            $update_mode = "UPDATE settings SET setting_value = 'manual' WHERE setting_key = 'offers_mode'";
            if ($conn->query($update_mode)) {
                $results[] = "✅ تم تحديث وضع العروض إلى 'يدوي'";
                $fixed++;
            }
        }
        
        // 3. التحقق من وجود حقل external_id
        $external_id_check = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        if ($external_id_check->num_rows == 0) {
            $add_external_id = "ALTER TABLE offers ADD COLUMN external_id VARCHAR(100) AFTER is_active";
            if ($conn->query($add_external_id)) {
                $results[] = "✅ تم إضافة حقل external_id";
                $fixed++;
            }
        }
        
        // 4. تحديث العروض بدون external_id
        $update_external_id = "UPDATE offers SET external_id = CONCAT('manual_legacy_', id) WHERE external_id IS NULL OR external_id = ''";
        $conn->query($update_external_id);
        $updated_offers = $conn->affected_rows;
        if ($updated_offers > 0) {
            $results[] = "✅ تم تحديث $updated_offers عرض بمعرف خارجي";
            $fixed++;
        }
        
        // 5. تفعيل جميع العروض
        $activate_offers = "UPDATE offers SET is_active = 1";
        $conn->query($activate_offers);
        $activated_offers = $conn->affected_rows;
        if ($activated_offers > 0) {
            $results[] = "✅ تم تفعيل $activated_offers عرض";
            $fixed++;
        }
        
        // 6. إضافة عرض تجريبي إذا لم توجد عروض
        $offers_count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
        if ($offers_count == 0) {
            $sample_offer = "
                INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id) 
                VALUES (
                    'عرض تجريبي - اشترك في Netflix واحصل على \$10',
                    'هذا عرض تجريبي لاختبار النظام. اشترك في Netflix لمدة شهر واحصل على 10 دولار كمكافأة.',
                    'https://via.placeholder.com/400x250/e50914/ffffff?text=Netflix+Offer',
                    'https://netflix.com/signup',
                    'US,UK,CA,AU',
                    'all',
                    10.00,
                    'CPA',
                    1,
                    'manual_quickfix_" . time() . "'
                )
            ";
            
            if ($conn->query($sample_offer)) {
                $results[] = "✅ تم إضافة عرض تجريبي";
                $fixed++;
            }
        }
        
        $conn->close();
        
        if ($fixed > 0) {
            $message = "✅ تم إصلاح $fixed مشكلة بنجاح!";
        } else {
            $message = "ℹ️ لا توجد مشاكل تحتاج إلى إصلاح";
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في الإصلاح السريع: " . $e->getMessage();
    }
}

// فحص حالة النظام
$system_status = [];
try {
    $conn = getDBConnection();
    
    // فحص جدول العروض
    $offers_table = $conn->query("SHOW TABLES LIKE 'offers'");
    $system_status['offers_table'] = $offers_table->num_rows > 0 ? 'موجود' : 'غير موجود';
    
    // فحص جدول الإعدادات
    $settings_table = $conn->query("SHOW TABLES LIKE 'settings'");
    $system_status['settings_table'] = $settings_table->num_rows > 0 ? 'موجود' : 'غير موجود';
    
    if ($offers_table->num_rows > 0) {
        // عدد العروض
        $offers_count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
        $system_status['offers_count'] = $offers_count;
        
        // العروض النشطة
        $active_count = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
        $system_status['active_offers'] = $active_count;
        
        // فحص حقل external_id
        $external_id_check = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        $system_status['external_id_field'] = $external_id_check->num_rows > 0 ? 'موجود' : 'غير موجود';
    }
    
    if ($settings_table->num_rows > 0) {
        // وضع العروض
        $mode_result = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'offers_mode'");
        if ($mode_result && $mode_result->num_rows > 0) {
            $mode = $mode_result->fetch_assoc()['setting_value'];
            $system_status['offers_mode'] = $mode;
        } else {
            $system_status['offers_mode'] = 'غير محدد';
        }
    }
    
    $conn->close();
} catch (Exception $e) {
    $system_status['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإصلاح السريع للعروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .status-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .status-value {
            color: #666;
        }
        
        .action-section {
            background: #fff3cd;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1.5rem 3rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .result-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid #28a745;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ الإصلاح السريع للعروض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php 
                if (strpos($message, '✅') !== false) echo 'success';
                elseif (strpos($message, 'ℹ️') !== false) echo 'info';
                else echo 'error';
            ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="status-grid">
            <?php foreach ($system_status as $key => $value): ?>
                <div class="status-card <?php 
                    if ($key === 'error' || strpos($value, 'غير موجود') !== false || strpos($value, 'غير محدد') !== false) echo 'error';
                    elseif ($value === '0' || $value === 0) echo 'warning';
                    else echo 'success';
                ?>">
                    <div class="status-title">
                        <?php
                        switch ($key) {
                            case 'offers_table': echo 'جدول العروض'; break;
                            case 'settings_table': echo 'جدول الإعدادات'; break;
                            case 'offers_count': echo 'عدد العروض'; break;
                            case 'active_offers': echo 'العروض النشطة'; break;
                            case 'external_id_field': echo 'حقل المعرف الخارجي'; break;
                            case 'offers_mode': echo 'وضع العروض'; break;
                            case 'error': echo 'خطأ'; break;
                            default: echo $key;
                        }
                        ?>
                    </div>
                    <div class="status-value"><?php echo htmlspecialchars($value); ?></div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="action-section">
            <h3 style="color: #856404; margin-bottom: 1rem;">🛠️ الإصلاح السريع</h3>
            <p style="color: #856404; margin-bottom: 2rem;">
                سيقوم هذا الإجراء بإصلاح المشاكل الشائعة تلقائياً:
                <br>• إنشاء جدول الإعدادات
                <br>• تعيين وضع العروض إلى "يدوي"
                <br>• إضافة حقل المعرف الخارجي
                <br>• تفعيل جميع العروض
                <br>• إضافة عرض تجريبي إذا لم توجد عروض
            </p>
            
            <form method="POST">
                <button type="submit" name="quick_fix" class="btn btn-success"
                        onclick="return confirm('هل تريد تشغيل الإصلاح السريع؟ هذا الإجراء آمن ولن يحذف أي بيانات.')">
                    ⚡ تشغيل الإصلاح السريع
                </button>
            </form>
        </div>
        
        <?php if (!empty($results)): ?>
        <div class="results-section">
            <h3 style="color: #333; margin-bottom: 1rem;">📋 نتائج الإصلاح</h3>
            
            <?php foreach ($results as $result): ?>
                <div class="result-item">
                    <?php echo htmlspecialchars($result); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="debug_offers.php">🔍 تشخيص العروض</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="offers_management.php">📋 إدارة العروض</a>
            <a href="add_sample_offers.php">🎁 عروض تجريبية</a>
        </div>
    </div>
</body>
</html>
