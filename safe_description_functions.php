<?php
require_once 'config.php';

/**
 * دوال آمنة للتعامل مع حقل الوصف
 * تتحقق من وجود الحقل قبل الاستخدام
 */

// التحقق من وجود حقل الوصف
function hasDescriptionField() {
    static $has_field = null;
    
    if ($has_field === null) {
        try {
            $conn = getDBConnection();
            $check = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
            $has_field = $check->num_rows > 0;
            $conn->close();
        } catch (Exception $e) {
            $has_field = false;
        }
    }
    
    return $has_field;
}

// إضافة عرض جديد مع التحقق من حقل الوصف
function safeInsertOffer($title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active = 1) {
    $conn = getDBConnection();
    
    if (hasDescriptionField()) {
        // إدراج مع الوصف
        $stmt = $conn->prepare("
            INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("ssssssdsi", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
    } else {
        // إدراج بدون الوصف
        $stmt = $conn->prepare("
            INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bind_param("sssssdsi", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
    }
    
    $result = $stmt->execute();
    $insert_id = $conn->insert_id;
    $stmt->close();
    $conn->close();
    
    return $result ? $insert_id : false;
}

// الحصول على العروض مع التحقق من حقل الوصف
function safeGetOffers($where_clause = "", $params = [], $types = "") {
    $conn = getDBConnection();
    
    if (hasDescriptionField()) {
        $sql = "SELECT id, title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, created_at FROM offers";
    } else {
        $sql = "SELECT id, title, '' as description, image_url, offer_url, countries, device, amount, payout_type, is_active, created_at FROM offers";
    }
    
    if ($where_clause) {
        $sql .= " " . $where_clause;
    }
    
    if (!empty($params)) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();
    } else {
        $result = $conn->query($sql);
    }
    
    $offers = [];
    while ($row = $result->fetch_assoc()) {
        $offers[] = $row;
    }
    
    $conn->close();
    return $offers;
}

// إضافة حقل الوصف إذا لم يكن موجود
function ensureDescriptionField() {
    if (!hasDescriptionField()) {
        try {
            $conn = getDBConnection();
            $sql = "ALTER TABLE offers ADD COLUMN description TEXT COLLATE utf8mb4_unicode_ci AFTER title";
            $result = $conn->query($sql);
            $conn->close();
            
            // إعادة تعيين الكاش
            // سيتم إعادة فحص الحقل في المرة القادمة
            
            return $result;
        } catch (Exception $e) {
            return false;
        }
    }
    return true;
}

// استخراج الوصف من بيانات CPALead
function extractDescription($offer_data, $title) {
    $description = '';
    
    if (isset($offer_data['description']) && !empty($offer_data['description'])) {
        $description = strip_tags($offer_data['description']);
        $description = substr($description, 0, 500);
    } elseif (isset($offer_data['preview_link']) && !empty($offer_data['preview_link'])) {
        $description = "معاينة العرض متاحة - " . $offer_data['preview_link'];
    } else {
        $description = "عرض من CPALead - " . $title;
    }
    
    return trim($description);
}

// تحديث العروض الموجودة بوصف افتراضي
function updateExistingOffersWithDescription() {
    if (!hasDescriptionField()) {
        return false;
    }
    
    try {
        $conn = getDBConnection();
        $sql = "UPDATE offers SET description = CONCAT('عرض من CPALead - ', title) WHERE description IS NULL OR description = ''";
        $result = $conn->query($sql);
        $affected_rows = $conn->affected_rows;
        $conn->close();
        
        return $affected_rows;
    } catch (Exception $e) {
        return false;
    }
}

// دالة للتحقق من صحة قاعدة البيانات
function validateDatabaseStructure() {
    $issues = [];
    
    try {
        $conn = getDBConnection();
        
        // التحقق من وجود جدول العروض
        $tables = $conn->query("SHOW TABLES LIKE 'offers'");
        if ($tables->num_rows == 0) {
            $issues[] = "جدول العروض غير موجود";
        } else {
            // التحقق من الحقول المطلوبة
            $required_fields = ['id', 'title', 'image_url', 'offer_url', 'is_active'];
            $columns = $conn->query("SHOW COLUMNS FROM offers");
            $existing_fields = [];
            
            while ($column = $columns->fetch_assoc()) {
                $existing_fields[] = $column['Field'];
            }
            
            foreach ($required_fields as $field) {
                if (!in_array($field, $existing_fields)) {
                    $issues[] = "الحقل المطلوب '$field' غير موجود";
                }
            }
            
            // التحقق من حقل الوصف
            if (!in_array('description', $existing_fields)) {
                $issues[] = "حقل الوصف غير موجود - يمكن إضافته باستخدام fix_description_field.php";
            }
        }
        
        $conn->close();
    } catch (Exception $e) {
        $issues[] = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }
    
    return $issues;
}
?>
