<?php
require_once 'config.php';

// يمكن تشغيل هذا الملف عبر Cron Job كل ساعة أو يومياً
// مثال Cron: 0 */6 * * * /usr/bin/php /path/to/auto_sync_offers.php

$conn = getDBConnection();

function writeLog($message) {
    $logFile = 'auto_sync.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

function checkOfferStatus($offer_url) {
    // فحص حالة العرض عبر HTTP request
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $offer_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    // تحديد حالة العرض
    if ($error) {
        return 'error';
    } elseif ($httpCode >= 200 && $httpCode < 300) {
        // فحص محتوى الصفحة للكلمات المفتاحية التي تدل على توقف العرض
        $stopKeywords = [
            'offer expired', 'offer not available', 'offer ended',
            'campaign ended', 'no longer available', 'expired',
            'not found', '404', 'unavailable'
        ];
        
        $responseText = strtolower($response);
        foreach ($stopKeywords as $keyword) {
            if (strpos($responseText, $keyword) !== false) {
                return 'stopped';
            }
        }
        return 'active';
    } elseif ($httpCode == 404) {
        return 'not_found';
    } else {
        return 'error';
    }
}

function syncOffersFromAPI() {
    global $conn;
    
    writeLog("بدء مزامنة العروض من API...");
    
    // الحصول على قائمة معرفات API المحفوظة
    $apiIds = [];
    $apiFile = 'api_ids.txt';
    if (file_exists($apiFile)) {
        $apiIds = array_filter(array_map('trim', file($apiFile)));
    }
    
    if (empty($apiIds)) {
        writeLog("لا توجد معرفات API محفوظة");
        return;
    }
    
    $totalNew = 0;
    $totalUpdated = 0;
    
    foreach ($apiIds as $apiId) {
        writeLog("معالجة API ID: $apiId");
        
        $apiUrl = "https://www.cpalead.com/api/offers?id=" . trim($apiId);
        $response = @file_get_contents($apiUrl);
        
        if ($response === false) {
            writeLog("فشل في الاتصال بـ API: $apiId");
            continue;
        }
        
        $data = json_decode($response, true);
        
        if (!$data || !isset($data['status']) || $data['status'] !== 'success') {
            writeLog("استجابة غير صحيحة من API: $apiId");
            continue;
        }
        
        if (!isset($data['offers']) || !is_array($data['offers'])) {
            writeLog("لا توجد عروض في API: $apiId");
            continue;
        }
        
        $offers = $data['offers'];
        writeLog("تم العثور على " . count($offers) . " عرض من API: $apiId");
        
        foreach ($offers as $offer) {
            if (!isset($offer['title']) || !isset($offer['preview_link'])) {
                continue;
            }
            
            $title = trim($offer['title']);
            $preview_link = trim($offer['preview_link']);
            $tracking_link = isset($offer['link']) ? trim($offer['link']) : '';
            $amount = isset($offer['amount']) ? $offer['amount'] : 0;
            $device = isset($offer['device']) ? $offer['device'] : '';
            $payout_type = isset($offer['payout_type']) ? $offer['payout_type'] : '';
            $countries = '';
            
            if (isset($offer['countries']) && is_array($offer['countries'])) {
                $countries = implode(',', $offer['countries']);
            }
            
            // إنشاء عنوان محسن
            $enhanced_title = $title;
            if ($amount > 0) {
                $enhanced_title .= " - $" . $amount . " " . $payout_type;
            }
            if ($device) {
                $enhanced_title .= " (" . ucfirst($device) . ")";
            }
            
            $final_url = !empty($tracking_link) ? $tracking_link : $preview_link;
            
            // استخراج صورة
            $image_url = '';
            if (isset($offer['creatives']['url'])) {
                $image_url = trim($offer['creatives']['url']);
            }
            if (empty($image_url)) {
                $image_url = 'https://via.placeholder.com/300x200/4CAF50/white?text=' . urlencode($title);
            }
            
            // التحقق من وجود العرض
            $checkStmt = $conn->prepare("SELECT id, is_active FROM offers WHERE title = ? OR offer_url = ?");
            $checkStmt->bind_param("ss", $enhanced_title, $final_url);
            $checkStmt->execute();
            $existingOffer = $checkStmt->get_result();
            
            if ($existingOffer->num_rows == 0) {
                // إضافة عرض جديد
                $insertStmt = $conn->prepare("
                    INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                ");
                $insertStmt->bind_param("sssssds", $enhanced_title, $image_url, $final_url, $countries, $device, $amount, $payout_type);
                
                if ($insertStmt->execute()) {
                    $totalNew++;
                    writeLog("تم إضافة عرض جديد: $enhanced_title");
                }
                $insertStmt->close();
            } else {
                // تحديث العرض الموجود (تفعيله إذا كان متوقف)
                $existing = $existingOffer->fetch_assoc();
                if ($existing['is_active'] == 0) {
                    $updateStmt = $conn->prepare("UPDATE offers SET is_active = 1, updated_at = NOW() WHERE id = ?");
                    $updateStmt->bind_param("i", $existing['id']);
                    if ($updateStmt->execute()) {
                        $totalUpdated++;
                        writeLog("تم تفعيل العرض المتوقف: $enhanced_title");
                    }
                    $updateStmt->close();
                }
            }
            $checkStmt->close();
        }
    }
    
    writeLog("انتهاء المزامنة - عروض جديدة: $totalNew، عروض محدثة: $totalUpdated");
}

function checkStoppedOffers() {
    global $conn;
    
    writeLog("بدء فحص العروض المتوقفة...");
    
    // الحصول على العروض النشطة
    $activeOffers = $conn->query("
        SELECT id, title, offer_url 
        FROM offers 
        WHERE is_active = 1 
        ORDER BY created_at DESC 
        LIMIT 50
    ");
    
    $stoppedCount = 0;
    
    while ($offer = $activeOffers->fetch_assoc()) {
        $status = checkOfferStatus($offer['offer_url']);
        
        if ($status === 'stopped' || $status === 'not_found') {
            // إيقاف العرض
            $updateStmt = $conn->prepare("UPDATE offers SET is_active = 0, updated_at = NOW() WHERE id = ?");
            $updateStmt->bind_param("i", $offer['id']);
            
            if ($updateStmt->execute()) {
                $stoppedCount++;
                writeLog("تم إيقاف العرض: " . $offer['title'] . " (السبب: $status)");
            }
            $updateStmt->close();
        } elseif ($status === 'active') {
            writeLog("العرض نشط: " . $offer['title']);
        } else {
            writeLog("خطأ في فحص العرض: " . $offer['title'] . " (الحالة: $status)");
        }
        
        // تأخير قصير لتجنب الحمل الزائد
        sleep(1);
    }
    
    writeLog("انتهاء فحص العروض المتوقفة - تم إيقاف: $stoppedCount عرض");
}

// تشغيل المزامنة
writeLog("=== بدء المزامنة التلقائية ===");

try {
    // 1. مزامنة العروض الجديدة من API
    syncOffersFromAPI();
    
    // 2. فحص العروض المتوقفة
    checkStoppedOffers();
    
    writeLog("=== انتهاء المزامنة التلقائية بنجاح ===");
    
} catch (Exception $e) {
    writeLog("خطأ في المزامنة: " . $e->getMessage());
}

$conn->close();

// إذا تم استدعاء الملف من المتصفح
if (isset($_GET['manual'])) {
    echo "<h2>تمت المزامنة بنجاح!</h2>";
    echo "<p>تحقق من ملف auto_sync.log للتفاصيل</p>";
    echo "<a href='admin.php'>العودة للوحة التحكم</a>";
}
?>
