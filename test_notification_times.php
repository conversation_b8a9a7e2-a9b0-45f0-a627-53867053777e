<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';

// إنشاء إشعارات تجريبية بأوقات مختلفة
if (isset($_POST['create_test_notifications'])) {
    try {
        $test_times = [
            ['time' => 'now', 'title' => 'إشعار الآن', 'offset' => 0],
            ['time' => '1 minute ago', 'title' => 'منذ دقيقة واحدة', 'offset' => -60],
            ['time' => '5 minutes ago', 'title' => 'منذ 5 دقائق', 'offset' => -300],
            ['time' => '1 hour ago', 'title' => 'منذ ساعة واحدة', 'offset' => -3600],
            ['time' => '3 hours ago', 'title' => 'منذ 3 ساعات', 'offset' => -10800],
            ['time' => '1 day ago', 'title' => 'منذ يوم واحد', 'offset' => -86400],
            ['time' => '3 days ago', 'title' => 'منذ 3 أيام', 'offset' => -259200],
            ['time' => '1 week ago', 'title' => 'منذ أسبوع', 'offset' => -604800],
        ];
        
        $created_count = 0;
        
        foreach ($test_times as $test) {
            $test_time = new DateTime('now', new DateTimeZone('Africa/Cairo'));
            $test_time->modify($test['offset'] . ' seconds');
            
            $conn = getDBConnection();
            $stmt = $conn->prepare("
                INSERT INTO notifications (type, title, message, created_at) 
                VALUES ('test_time', ?, ?, ?)
            ");
            
            $message_text = "إشعار تجريبي لاختبار تنسيق الوقت - " . $test['time'];
            $formatted_time = $test_time->format('Y-m-d H:i:s');
            
            $stmt->bind_param("sss", $test['title'], $message_text, $formatted_time);
            
            if ($stmt->execute()) {
                $created_count++;
            }
            
            $stmt->close();
            $conn->close();
        }
        
        $message = "✅ تم إنشاء {$created_count} إشعار تجريبي بأوقات مختلفة!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إنشاء الإشعارات التجريبية: " . $e->getMessage();
    }
}

// حذف الإشعارات التجريبية
if (isset($_POST['delete_test_notifications'])) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("DELETE FROM notifications WHERE type = 'test_time'");
        $stmt->execute();
        $deleted = $stmt->affected_rows;
        $stmt->close();
        $conn->close();
        
        $message = "✅ تم حذف {$deleted} إشعار تجريبي!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في حذف الإشعارات التجريبية: " . $e->getMessage();
    }
}

// الحصول على الإشعارات التجريبية
$test_notifications = [];
try {
    $conn = getDBConnection();
    $result = $conn->query("
        SELECT id, title, message, created_at 
        FROM notifications 
        WHERE type = 'test_time' 
        ORDER BY created_at DESC
    ");
    
    while ($row = $result->fetch_assoc()) {
        $test_notifications[] = $row;
    }
    
    $conn->close();
} catch (Exception $e) {
    $message = "❌ خطأ في جلب الإشعارات التجريبية: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أوقات الإشعارات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .current-time {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .current-time h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .time-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin: 0.5rem 0;
        }
        
        .notifications-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .notification-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .notification-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .notification-time {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .notification-raw-time {
            color: #999;
            font-size: 0.8rem;
            font-family: monospace;
        }
        
        .auto-update-indicator {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            border: 1px solid #ffeaa7;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار أوقات الإشعارات</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="current-time">
            <h3>⏰ الوقت الحالي</h3>
            <div class="time-display" id="current-time">
                <?php 
                $now = new DateTime('now', new DateTimeZone('Africa/Cairo'));
                echo $now->format('Y-m-d g:i:s A'); 
                ?>
            </div>
            <div style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">
                توقيت القاهرة (يتم التحديث كل ثانية)
            </div>
        </div>
        
        <div class="auto-update-indicator">
            ⚡ <strong>التحديث التلقائي نشط:</strong> الأوقات النسبية تتحدث كل دقيقة تلقائياً
        </div>
        
        <div class="controls">
            <h3 style="margin-bottom: 1rem;">🔧 أدوات الاختبار</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_test_notifications" class="btn btn-success">
                    🧪 إنشاء إشعارات تجريبية
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="delete_test_notifications" class="btn btn-danger">
                    🗑️ حذف الإشعارات التجريبية
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_notifications)): ?>
        <div>
            <h3 style="color: #333; margin-bottom: 1rem;">📋 الإشعارات التجريبية (<?php echo count($test_notifications); ?>)</h3>
            
            <div class="notifications-grid">
                <?php foreach ($test_notifications as $notification): ?>
                    <div class="notification-card">
                        <div class="notification-title">
                            <?php echo htmlspecialchars($notification['title']); ?>
                        </div>
                        <div class="notification-time" data-timestamp="<?php echo $notification['created_at']; ?>">
                            <?php echo formatCairoTime($notification['created_at']); ?>
                        </div>
                        <div class="notification-raw-time">
                            الوقت الخام: <?php echo $notification['created_at']; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <div style="text-align: center; padding: 2rem; color: #666;">
            📭 لا توجد إشعارات تجريبية. اضغط "إنشاء إشعارات تجريبية" لبدء الاختبار.
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="fix_notification_times.php">🔧 إصلاح أوقات الإشعارات</a>
            <a href="notifications_panel.php">📢 لوحة الإشعارات</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
    
    <!-- تحديث الأوقات تلقائياً -->
    <script src="time_updater.js"></script>
    
    <script>
        // تحديث الوقت الحالي كل ثانية
        function updateCurrentTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: true,
                timeZone: 'Africa/Cairo'
            };
            
            document.getElementById('current-time').textContent = 
                now.toLocaleString('ar-EG', options);
        }
        
        // تحديث الوقت الحالي كل ثانية
        setInterval(updateCurrentTime, 1000);
        
        // تحديث فوري
        updateCurrentTime();
    </script>
</body>
</html>
