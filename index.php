<?php
require_once 'config.php';
require_once 'countries.php';

// التحقق من كلمة المرور
checkPassword();

$user_ip = getUserIP();
$conn = getDBConnection();
$message = '';

// معالجة حفظ IP
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_ip'])) {
    $savedUsername = saveIP($user_ip);
    if ($savedUsername) {
        $message = '<div class="alert alert-success">✅ تم حفظ عنوان IP بنجاح!<br><strong>اسم المستخدم الخاص بك:</strong> ' . htmlspecialchars($savedUsername) . '<br>لن تظهر لك العروض لمدة 15 يوم.</div>';
    } else {
        $message = '<div class="alert alert-error">❌ تم حفظ عنوان IP مسبقاً خلال الـ 15 يوم الماضية!</div>';
    }
}

// التحقق من حالة IP المحفوظ
$ipSaved = hasRecentSavedIP($user_ip, 15);

// الحصول على بلد المستخدم
$ipInfo = checkIPQuality($user_ip);
$userCountry = $ipInfo['country'];

// الحصول على العروض المتاحة (التي لم يضغط عليها المستخدم خلال 15 يوم)
// العروض متاحة للجميع سواء كان IP محفوظ أم لا
$countryFilter = "";
$params = [$user_ip];
$types = "s";

if ($userCountry && $userCountry !== 'غير معروف' && $userCountry !== 'محلي') {
    // البحث عن العروض التي تدعم بلد المستخدم أو العروض بدون تحديد بلد
    $countryFilter = " AND (o.countries IS NULL OR o.countries = '' OR o.countries LIKE ?)";
    $params[] = "%{$userCountry}%";
    $types .= "s";
}

$sql = "
    SELECT o.id, o.title, o.image_url, o.countries, o.device, o.amount, o.payout_type
    FROM offers o
    WHERE o.is_active = 1
    AND o.id NOT IN (
        SELECT DISTINCT c.offer_id
        FROM clicks c
        WHERE c.ip_address = ?
        AND c.clicked_at > DATE_SUB(NOW(), INTERVAL 15 DAY)
    ) {$countryFilter}
    ORDER BY o.id DESC
";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$offers = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>موقع CPA - العروض المتاحة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .offer-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            text-decoration: none;
            color: inherit;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .offer-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .offer-content {
            padding: 1.5rem;
        }
        
        .offer-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .offer-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        
        .offer-button:hover {
            transform: scale(1.02);
        }
        
        .no-offers {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .no-offers h2 {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .no-offers p {
            color: #999;
        }
        
        .temp-emails {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .temp-emails h3 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 1.5rem;
        }
        
        .email-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .email-link {
            display: block;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 1rem;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: transform 0.2s;
            font-weight: bold;
        }
        
        .email-link:hover {
            transform: translateY(-2px);
        }
        
        .admin-link {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #333;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .ip-info {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .ip-info h3 {
            color: #333;
            margin-bottom: 1rem;
        }

        .ip-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .ip-detail {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
        }

        .ip-detail strong {
            color: #333;
        }

        .quality-score {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .quality-high { color: #28a745; }
        .quality-medium { color: #ffc107; }
        .quality-low { color: #dc3545; }

        .save-ip-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .save-ip-btn:hover {
            transform: translateY(-2px);
        }

        .save-ip-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .ip-saved-message {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .offers-grid {
                grid-template-columns: 1fr;
            }
            
            .email-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 موقع CPA</h1>
            <p>اختر العرض المناسب لك واحصل على أفضل الفرص</p>
        </div>

        <?php echo $message; ?>

        <?php
        // عرض معلومات IP
        $ipInfo = checkIPQuality($user_ip);
        $qualityClass = $ipInfo['quality_score'] >= 70 ? 'quality-high' : ($ipInfo['quality_score'] >= 40 ? 'quality-medium' : 'quality-low');
        $currentUsername = getUsernameForIP($user_ip);
        ?>

        <div class="ip-info">
            <h3>🔍 معلومات عنوان IP الخاص بك</h3>
            <div class="ip-details">
                <div class="ip-detail">
                    <strong>عنوان IP:</strong> <?php echo htmlspecialchars($user_ip); ?>
                </div>
                <div class="ip-detail">
                    <strong>اسم المستخدم:</strong>
                    <span style="color: #667eea; font-weight: bold;"><?php echo htmlspecialchars($currentUsername); ?></span>
                </div>
                <div class="ip-detail">
                    <strong>الدولة:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?>
                </div>
                <div class="ip-detail">
                    <strong>المدينة:</strong> <?php echo htmlspecialchars($ipInfo['city']); ?>
                </div>
                <div class="ip-detail">
                    <strong>مزود الخدمة:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?>
                </div>
                <div class="ip-detail">
                    <strong>بروكسي:</strong> <?php echo $ipInfo['is_proxy'] ? '⚠️ نعم' : '✅ لا'; ?>
                </div>
                <div class="ip-detail">
                    <strong>نقاط الجودة:</strong>
                    <span class="quality-score <?php echo $qualityClass; ?>">
                        <?php echo $ipInfo['quality_score']; ?>/100
                    </span>
                </div>
            </div>

            <?php if (!$ipSaved): ?>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="save_ip" class="save-ip-btn">
                        💾 حفظ عنوان IP (إخفاء العروض لمدة 15 يوم)
                    </button>
                </form>
            <?php else: ?>
                <button class="save-ip-btn" disabled>
                    ✅ تم حفظ عنوان IP مسبقاً
                </button>
            <?php endif; ?>
        </div>

        <?php if ($ipSaved): ?>
            <div class="ip-saved-message">
                <h2>🔒 تم حفظ عنوان IP</h2>
                <p>لقد تم حفظ عنوان IP الخاص بك مسبقاً. لن تظهر لك أي عروض لمدة 15 يوم من تاريخ الحفظ.</p>
                <p>يمكنك العودة بعد انتهاء هذه المدة لمشاهدة العروض الجديدة.</p>
            </div>
        <?php endif; ?>
        
        <?php if ($offers && $offers->num_rows > 0): ?>
            <div class="offers-info" style="background: linear-gradient(135deg, #e8f4fd 0%, #bbdefb 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 1.5rem; text-align: center; border: 2px solid #2196f3;">
                <?php
                $displayCountry = getCountryName($userCountry);
                ?>
                <h3 style="margin: 0 0 0.5rem 0; color: #1565c0; font-size: 1.2rem;">
                    🎯 العروض المخصصة لك
                </h3>
                <p style="margin: 0; color: #1976d2; font-size: 1rem;">
                    <strong>📍 موقعك:</strong> <?php echo htmlspecialchars($displayCountry); ?>
                </p>
                <p style="margin: 0.5rem 0 0 0; color: #1976d2; font-size: 1rem;">
                    <strong>🎁 العروض المتاحة:</strong> <?php echo $offers->num_rows; ?> عرض مناسب لبلدك
                </p>
            </div>
            <div class="offers-grid">
                <?php while ($offer = $offers->fetch_assoc()): ?>
                    <a href="go.php?id=<?php echo $offer['id']; ?>" class="offer-card">
                        <img src="<?php echo htmlspecialchars($offer['image_url']); ?>"
                             alt="<?php echo htmlspecialchars($offer['title']); ?>"
                             class="offer-image">
                        <div class="offer-content">
                            <div class="offer-title"><?php echo htmlspecialchars($offer['title']); ?></div>

                            <?php if ($offer['amount'] > 0): ?>
                                <div class="offer-amount" style="color: #28a745; font-weight: bold; margin: 0.5rem 0;">
                                    💰 $<?php echo $offer['amount']; ?> <?php echo $offer['payout_type']; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($offer['device']): ?>
                                <div class="offer-device" style="color: #666; font-size: 0.9rem; margin: 0.3rem 0;">
                                    📱 <?php echo ucfirst($offer['device']); ?>
                                </div>
                            <?php endif; ?>

                            <div class="offer-countries" style="<?php echo $offer['countries'] ? 'background: #f0f8e8; color: #2d5016;' : 'background: #e3f2fd; color: #1565c0;'; ?> padding: 0.3rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin: 0.5rem 0; text-align: center;">
                                🌍 <?php echo formatCountriesDisplay($offer['countries'], 3); ?>
                            </div>

                            <button class="offer-button">🚀 اضغط للمتابعة</button>
                        </div>
                    </a>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="no-offers" style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); padding: 2rem; border-radius: 15px; text-align: center; border: 2px solid #ff9800;">
                <div style="font-size: 4rem; margin-bottom: 1rem;">😔</div>
                <h2 style="color: #e65100; margin-bottom: 1rem;">لا توجد عروض متاحة حالياً</h2>

                <?php
                $displayCountry = getCountryName($userCountry);
                ?>

                <div style="background: white; padding: 1rem; border-radius: 10px; margin: 1rem 0; border-left: 4px solid #ff9800;">
                    <p style="margin: 0; color: #bf360c;">
                        <strong>📍 بلدك:</strong> <?php echo htmlspecialchars($displayCountry); ?>
                    </p>
                </div>

                <div style="background: #f3e5f5; padding: 1rem; border-radius: 10px; margin: 1rem 0;">
                    <h4 style="color: #4a148c; margin-bottom: 0.5rem;">💡 اقتراحات:</h4>
                    <ul style="text-align: right; color: #6a1b9a; margin: 0; padding-right: 1.5rem;">
                        <li>جرب مرة أخرى لاحقاً - قد تتوفر عروض جديدة</li>
                        <li>احفظ عنوان IP الخاص بك للحصول على عروض حصرية</li>
                        <li>العروض تتجدد كل 15 يوم للمستخدمين</li>
                        <li>تحقق من توفر عروض لبلدان أخرى</li>
                    </ul>
                </div>

                <div style="margin-top: 1.5rem;">
                    <a href="javascript:location.reload()" style="background: #ff9800; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block; margin: 0.5rem;">
                        🔄 تحديث الصفحة
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="temp-emails">
            <h3>📧 مواقع الإيميلات المؤقتة</h3>
            <div class="email-links">
                <a href="https://temp-mail.org/en/" target="_blank" class="email-link">
                    📮 Temp Mail
                </a>
                <a href="https://tempail.com/" target="_blank" class="email-link">
                    ✉️ Tempail
                </a>
                <a href="https://temp-mail.io/en/" target="_blank" class="email-link">
                    📬 Temp Mail IO
                </a>
                <a href="https://run2mail.com/en/" target="_blank" class="email-link">
                    🏃 Run2Mail
                </a>
            </div>
        </div>
    </div>
    
    <a href="admin.php" class="admin-link">⚙️ لوحة التحكم</a>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?>
