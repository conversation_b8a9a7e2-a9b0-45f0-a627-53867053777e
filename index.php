<?php
require_once 'config.php';
require_once 'countries.php';
require_once 'notifications.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$user_ip = getUserIP();
$conn = getDBConnection();
$message = '';

// الحصول على عدد الإشعارات غير المقروءة
$unread_notifications = getUnreadNotificationsCount();

// معالجة حفظ IP
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_ip'])) {
    $savedUsername = saveIP($user_ip);
    if ($savedUsername) {
        $message = '<div class="alert alert-success">✅ تم حفظ عنوان IP بنجاح!<br><strong>اسم المستخدم الخاص بك:</strong> ' . htmlspecialchars($savedUsername) . '<br>لن تظهر لك العروض لمدة 15 يوم.</div>';
    } else {
        $message = '<div class="alert alert-error">❌ تم حفظ عنوان IP مسبقاً خلال الـ 15 يوم الماضية!</div>';
    }
}

// التحقق من حالة IP المحفوظ
$ipSaved = hasRecentSavedIP($user_ip, 15);

// الحصول على بلد المستخدم
$ipInfo = checkIPQuality($user_ip);
$detectedCountry = $ipInfo['country'];

// التحقق من الدولة المختارة من المستخدم أو استخدام الدولة المكتشفة
$selectedCountry = isset($_GET['country']) && !empty($_GET['country']) ? $_GET['country'] : $detectedCountry;

// جلب العروض المميزة أولاً
$featured_offers = null;
$table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
if ($table_check->num_rows > 0) {
    // بناء الاستعلام للعروض المميزة مع التحقق من وجود حقل الوصف
    if (hasDescriptionField()) {
        $featured_select_fields = "o.id, o.title, o.description, o.image_url, o.countries, o.device, o.amount, o.payout_type, f.position";
    } else {
        $featured_select_fields = "o.id, o.title, '' as description, o.image_url, o.countries, o.device, o.amount, o.payout_type, f.position";
    }

    $featured_sql = "
        SELECT {$featured_select_fields}
        FROM featured_offers f
        JOIN offers o ON f.offer_id = o.id
        WHERE f.is_active = 1 AND o.is_active = 1
        AND o.id NOT IN (
            SELECT DISTINCT c.offer_id
            FROM clicks c
            WHERE c.ip_address = ?
            AND c.clicked_at > DATE_SUB(NOW(), INTERVAL 15 DAY)
        )
        ORDER BY f.position ASC, f.created_at ASC
        LIMIT 6
    ";

    $featured_stmt = $conn->prepare($featured_sql);
    $featured_stmt->bind_param("s", $user_ip);
    $featured_stmt->execute();
    $featured_offers = $featured_stmt->get_result();
}

// الحصول على العروض العادية (التي لم يضغط عليها المستخدم خلال 15 يوم)
// فلترة العروض حسب الدولة المختارة
$countryFilter = "";
$params = [$user_ip];
$types = "s";

if ($selectedCountry && $selectedCountry !== 'غير معروف' && $selectedCountry !== 'محلي' && $selectedCountry !== 'all' && !empty($selectedCountry)) {
    // البحث عن العروض التي تدعم الدولة المختارة أو العروض العالمية
    $countryFilter = " AND (o.countries IS NULL OR o.countries = '' OR o.countries LIKE ? OR o.countries LIKE ? OR o.countries LIKE ?)";
    $params[] = "%{$selectedCountry}%";
    $params[] = "%ALL%";
    $params[] = "%Global%";
    $types .= "sss";
}
// إذا تم اختيار "جميع الدول" أو لم يتم تحديد دولة، لا نضيف فلتر للدولة

// بناء الاستعلام مع التحقق من وجود حقل الوصف
if (hasDescriptionField()) {
    $select_fields = "o.id, o.title, o.description, o.image_url, o.countries, o.device, o.amount, o.payout_type";
} else {
    $select_fields = "o.id, o.title, '' as description, o.image_url, o.countries, o.device, o.amount, o.payout_type";
}

// استثناء العروض المميزة من العروض العادية
$featured_exclusion = "";
if ($table_check->num_rows > 0) {
    $featured_exclusion = " AND o.id NOT IN (SELECT f.offer_id FROM featured_offers f WHERE f.is_active = 1)";
}

$sql = "
    SELECT {$select_fields}
    FROM offers o
    WHERE o.is_active = 1
    AND o.id NOT IN (
        SELECT DISTINCT c.offer_id
        FROM clicks c
        WHERE c.ip_address = ?
        AND c.clicked_at > DATE_SUB(NOW(), INTERVAL 15 DAY)
    ) {$countryFilter} {$featured_exclusion}
    ORDER BY o.id DESC
";

$stmt = $conn->prepare($sql);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$offers = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>موقع CPA - العروض المتاحة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .country-selector {
            margin-top: 1rem;
            text-align: center;
        }

        .country-selector label {
            color: white;
            font-weight: bold;
            margin-left: 0.5rem;
            font-size: 1rem;
        }

        .country-selector select {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: none;
            font-size: 1rem;
            min-width: 200px;
            background: white;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .country-selector select:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .country-selector select:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
        }

        .featured-section {
            margin: 2rem 0;
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #ff9800;
        }

        .featured-section h2 {
            color: #e65100;
            text-align: center;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .featured-offer {
            background: linear-gradient(135deg, #ffffff 0%, #fff8e1 100%);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
            border: 2px solid #ffcc02;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .featured-offer::before {
            content: "⭐";
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 1.5rem;
            color: #ff9800;
            animation: sparkle 2s infinite;
        }

        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }

        .featured-offer:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 152, 0, 0.3);
            border-color: #ff6f00;
        }

        .featured-offer img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 1rem;
            border: 2px solid #ffcc02;
        }

        .featured-offer h3 {
            color: #e65100;
            margin-bottom: 0.8rem;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .featured-offer p {
            color: #bf360c;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .featured-offer .amount {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
            box-shadow: 0 4px 10px rgba(255, 152, 0, 0.3);
        }

        .featured-offer .btn {
            background: linear-gradient(135deg, #ff6f00 0%, #e65100 100%);
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(230, 81, 0, 0.3);
            width: 100%;
            text-align: center;
        }

        .featured-offer .btn:hover {
            background: linear-gradient(135deg, #e65100 0%, #bf360c 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(230, 81, 0, 0.4);
        }

        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .offer-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            text-decoration: none;
            color: inherit;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .offer-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .offer-content {
            padding: 1.5rem;
        }
        
        .offer-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .offer-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        
        .offer-button:hover {
            transform: scale(1.02);
        }
        
        .no-offers {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .no-offers h2 {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .no-offers p {
            color: #999;
        }
        
        .temp-emails {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .temp-emails h3 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
            font-size: 1.5rem;
        }
        
        .email-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .email-link {
            display: block;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 1rem;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: transform 0.2s;
            font-weight: bold;
        }
        
        .email-link:hover {
            transform: translateY(-2px);
        }
        
        .admin-link {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #333;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .notifications-link {
            position: fixed;
            bottom: 20px;
            left: 150px;
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .notifications-link:hover {
            background: #5a67d8;
        }

        .notification-badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 0.2rem 0.5rem;
            font-size: 0.7rem;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .ip-info {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .ip-info h3 {
            color: #333;
            margin-bottom: 1rem;
        }

        .ip-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .ip-detail {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 5px;
        }

        .ip-detail strong {
            color: #333;
        }

        .quality-score {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .quality-high { color: #28a745; }
        .quality-medium { color: #ffc107; }
        .quality-low { color: #dc3545; }

        .save-ip-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .save-ip-btn:hover {
            transform: translateY(-2px);
        }

        .save-ip-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .ip-saved-message {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .offers-grid {
                grid-template-columns: 1fr;
            }
            
            .email-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 موقع CPA</h1>
            <p>اختر العرض المناسب لك واحصل على أفضل الفرص</p>

            <!-- روابط إدارة للمشرفين -->
            <?php if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']): ?>
            <div style="text-align: center; margin: 1rem 0;">
                <a href="add_manual_offer.php" style="background: #28a745; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none; margin: 0 0.3rem; font-size: 0.8rem;">➕ إضافة عرض</a>
                <a href="manage_offers.php" style="background: #17a2b8; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none; margin: 0 0.3rem; font-size: 0.8rem;">📋 إدارة العروض</a>
                <a href="bulk_manage_offers.php" style="background: #6f42c1; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none; margin: 0 0.3rem; font-size: 0.8rem;">☑️ إدارة متعددة</a>
                <a href="api_manager.php" style="background: #667eea; color: white; padding: 0.5rem 1rem; border-radius: 5px; text-decoration: none; margin: 0 0.3rem; font-size: 0.8rem;">📡 لوحة التحكم</a>
            </div>
            <?php endif; ?>

            <!-- قائمة اختيار الدولة -->
            <div class="country-selector">
                <form method="GET" style="display: inline-block;">
                    <label for="country-select">🌍 اختر دولتك:</label>
                    <select id="country-select" name="country" onchange="this.form.submit()">
                        <option value="all" <?php echo ($selectedCountry === 'all' || empty($selectedCountry)) ? 'selected' : ''; ?>>جميع الدول</option>
                        <?php
                        $countries = [
                            'EG' => 'مصر',
                            'SA' => 'السعودية',
                            'AE' => 'الإمارات',
                            'KW' => 'الكويت',
                            'QA' => 'قطر',
                            'BH' => 'البحرين',
                            'OM' => 'عمان',
                            'JO' => 'الأردن',
                            'LB' => 'لبنان',
                            'SY' => 'سوريا',
                            'IQ' => 'العراق',
                            'YE' => 'اليمن',
                            'LY' => 'ليبيا',
                            'TN' => 'تونس',
                            'DZ' => 'الجزائر',
                            'MA' => 'المغرب',
                            'SD' => 'السودان',
                            'US' => 'الولايات المتحدة',
                            'GB' => 'المملكة المتحدة',
                            'DE' => 'ألمانيا',
                            'FR' => 'فرنسا',
                            'CA' => 'كندا',
                            'AU' => 'أستراليا'
                        ];

                        foreach ($countries as $code => $name) {
                            $selected = ($selectedCountry === $code) ? 'selected' : '';
                            echo "<option value=\"$code\" $selected>$name</option>";
                        }
                        ?>
                    </select>
                </form>
            </div>
        </div>

        <?php echo $message; ?>

        <?php
        // عرض معلومات IP
        $ipInfo = checkIPQuality($user_ip);
        $qualityClass = $ipInfo['quality_score'] >= 70 ? 'quality-high' : ($ipInfo['quality_score'] >= 40 ? 'quality-medium' : 'quality-low');
        $currentUsername = getUsernameForIP($user_ip);
        ?>

        <div class="ip-info">
            <h3>🔍 معلومات عنوان IP الخاص بك</h3>
            <div class="ip-details">
                <div class="ip-detail">
                    <strong>عنوان IP:</strong> <?php echo htmlspecialchars($user_ip); ?>
                </div>
                <div class="ip-detail">
                    <strong>اسم المستخدم:</strong>
                    <span style="color: #667eea; font-weight: bold;"><?php echo htmlspecialchars($currentUsername); ?></span>
                </div>
                <div class="ip-detail">
                    <strong>الدولة:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?>
                </div>
                <div class="ip-detail">
                    <strong>المدينة:</strong> <?php echo htmlspecialchars($ipInfo['city']); ?>
                </div>
                <div class="ip-detail">
                    <strong>مزود الخدمة:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?>
                </div>
                <div class="ip-detail">
                    <strong>بروكسي:</strong> <?php echo $ipInfo['is_proxy'] ? '⚠️ نعم' : '✅ لا'; ?>
                </div>
                <div class="ip-detail">
                    <strong>نقاط الجودة:</strong>
                    <span class="quality-score <?php echo $qualityClass; ?>">
                        <?php echo $ipInfo['quality_score']; ?>/100
                    </span>
                </div>
            </div>

            <?php if (!$ipSaved): ?>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="save_ip" class="save-ip-btn">
                        💾 حفظ عنوان IP (إخفاء العروض لمدة 15 يوم)
                    </button>
                </form>
            <?php else: ?>
                <button class="save-ip-btn" disabled>
                    ✅ تم حفظ عنوان IP مسبقاً
                </button>
            <?php endif; ?>
        </div>

        <?php if ($ipSaved): ?>
            <div class="ip-saved-message">
                <h2>🔒 تم حفظ عنوان IP</h2>
                <p>لقد تم حفظ عنوان IP الخاص بك مسبقاً. لن تظهر لك أي عروض لمدة 15 يوم من تاريخ الحفظ.</p>
                <p>يمكنك العودة بعد انتهاء هذه المدة لمشاهدة العروض الجديدة.</p>
            </div>
        <?php endif; ?>
        
        <!-- العروض المميزة -->
        <?php if ($featured_offers && $featured_offers->num_rows > 0): ?>
        <div class="featured-section">
            <h2>⭐ العروض المميزة ⭐</h2>
            <div class="featured-grid">
                <?php while ($featured = $featured_offers->fetch_assoc()): ?>
                    <a href="go.php?id=<?php echo $featured['id']; ?>" class="featured-offer">
                        <img src="<?php echo htmlspecialchars($featured['image_url']); ?>"
                             alt="<?php echo htmlspecialchars($featured['title']); ?>"
                             onerror="this.src='https://via.placeholder.com/280x180/ff9800/ffffff?text=عرض+مميز'">

                        <h3><?php echo htmlspecialchars($featured['title']); ?></h3>

                        <?php if (!empty($featured['description'])): ?>
                            <p><?php echo htmlspecialchars(substr($featured['description'], 0, 100)); ?>
                            <?php if (strlen($featured['description']) > 100): ?>...<?php endif; ?></p>
                        <?php endif; ?>

                        <?php if ($featured['amount'] > 0): ?>
                            <div class="amount">💰 $<?php echo number_format($featured['amount'], 2); ?> <?php echo $featured['payout_type']; ?></div>
                        <?php endif; ?>

                        <div class="btn">🚀 احصل على العرض الآن</div>
                    </a>
                <?php endwhile; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($offers && $offers->num_rows > 0): ?>
            <div class="offers-info" style="background: linear-gradient(135deg, #e8f4fd 0%, #bbdefb 100%); padding: 1.5rem; border-radius: 12px; margin-bottom: 1.5rem; text-align: center; border: 2px solid #2196f3;">
                <?php
                if ($selectedCountry === 'all' || empty($selectedCountry)) {
                    $displayText = "جميع الدول";
                    $availableText = "عرض من جميع أنحاء العالم";
                } else {
                    $displayCountry = getCountryName($selectedCountry);
                    $displayText = $displayCountry;
                    $availableText = "عرض مناسب للدولة المختارة";
                }
                $detectedCountryName = getCountryName($detectedCountry);
                ?>
                <h3 style="margin: 0 0 0.5rem 0; color: #1565c0; font-size: 1.2rem;">
                    🎯 العروض المخصصة لك
                </h3>
                <p style="margin: 0; color: #1976d2; font-size: 1rem;">
                    <strong>📍 الدولة المختارة:</strong> <?php echo htmlspecialchars($displayText); ?>
                    <?php if ($selectedCountry !== 'all' && $selectedCountry !== $detectedCountry && !empty($selectedCountry)): ?>
                        <span style="color: #666; font-size: 0.9rem;">(تم الكشف عن: <?php echo htmlspecialchars($detectedCountryName); ?>)</span>
                    <?php endif; ?>
                </p>
                <p style="margin: 0.5rem 0 0 0; color: #1976d2; font-size: 1rem;">
                    <strong>🎁 العروض المتاحة:</strong> <?php echo $offers->num_rows; ?> <?php echo $availableText; ?>
                    <?php if ($featured_offers && $featured_offers->num_rows > 0): ?>
                        <span style="color: #ff9800; font-weight: bold;"> + <?php echo $featured_offers->num_rows; ?> عرض مميز</span>
                    <?php endif; ?>
                </p>
            </div>
            <div class="offers-grid">
                <?php while ($offer = $offers->fetch_assoc()): ?>
                    <a href="go.php?id=<?php echo $offer['id']; ?>" class="offer-card">
                        <img src="<?php echo htmlspecialchars($offer['image_url']); ?>"
                             alt="<?php echo htmlspecialchars($offer['title']); ?>"
                             class="offer-image">
                        <div class="offer-content">
                            <div class="offer-title"><?php echo htmlspecialchars($offer['title']); ?></div>

                            <?php if (!empty($offer['description'])): ?>
                                <div class="offer-description" style="color: #666; font-size: 0.9rem; line-height: 1.4; margin: 0.8rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 5px; border-left: 3px solid #667eea;">
                                    📝 <?php echo htmlspecialchars(substr($offer['description'], 0, 150)); ?>
                                    <?php if (strlen($offer['description']) > 150): ?>
                                        <span style="color: #667eea;">...</span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($offer['amount'] > 0): ?>
                                <div class="offer-amount" style="color: #28a745; font-weight: bold; margin: 0.5rem 0;">
                                    💰 $<?php echo $offer['amount']; ?> <?php echo $offer['payout_type']; ?>
                                </div>
                            <?php endif; ?>

                            <?php if ($offer['device']): ?>
                                <div class="offer-device" style="color: #666; font-size: 0.9rem; margin: 0.3rem 0;">
                                    📱 <?php echo ucfirst($offer['device']); ?>
                                </div>
                            <?php endif; ?>

                            <div class="offer-countries" style="<?php echo $offer['countries'] ? 'background: #f0f8e8; color: #2d5016;' : 'background: #e3f2fd; color: #1565c0;'; ?> padding: 0.3rem 0.5rem; border-radius: 12px; font-size: 0.8rem; margin: 0.5rem 0; text-align: center;">
                                🌍 <?php echo formatCountriesDisplay($offer['countries'], 3); ?>
                            </div>

                            <button class="offer-button">🚀 اضغط للمتابعة</button>
                        </div>
                    </a>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="no-offers" style="background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%); padding: 2rem; border-radius: 15px; text-align: center; border: 2px solid #ff9800;">
                <div style="font-size: 4rem; margin-bottom: 1rem;">😔</div>
                <h2 style="color: #e65100; margin-bottom: 1rem;">لا توجد عروض متاحة حالياً</h2>

                <?php
                if ($selectedCountry === 'all' || empty($selectedCountry)) {
                    $displayText = "جميع الدول";
                } else {
                    $displayCountry = getCountryName($selectedCountry);
                    $displayText = $displayCountry;
                }
                $detectedCountryName = getCountryName($detectedCountry);
                ?>

                <div style="background: white; padding: 1rem; border-radius: 10px; margin: 1rem 0; border-left: 4px solid #ff9800;">
                    <p style="margin: 0; color: #bf360c;">
                        <strong>📍 الدولة المختارة:</strong> <?php echo htmlspecialchars($displayText); ?>
                        <?php if ($selectedCountry !== 'all' && $selectedCountry !== $detectedCountry && !empty($selectedCountry)): ?>
                            <br><span style="color: #666; font-size: 0.9rem;">تم الكشف عن: <?php echo htmlspecialchars($detectedCountryName); ?></span>
                        <?php endif; ?>
                    </p>
                </div>

                <div style="background: #f3e5f5; padding: 1rem; border-radius: 10px; margin: 1rem 0;">
                    <h4 style="color: #4a148c; margin-bottom: 0.5rem;">💡 اقتراحات:</h4>
                    <ul style="text-align: right; color: #6a1b9a; margin: 0; padding-right: 1.5rem;">
                        <li>جرب مرة أخرى لاحقاً - قد تتوفر عروض جديدة</li>
                        <li>احفظ عنوان IP الخاص بك للحصول على عروض حصرية</li>
                        <li>العروض تتجدد كل 15 يوم للمستخدمين</li>
                        <li>تحقق من توفر عروض لبلدان أخرى</li>
                    </ul>
                </div>

                <div style="margin-top: 1.5rem;">
                    <a href="javascript:location.reload()" style="background: #ff9800; color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block; margin: 0.5rem;">
                        🔄 تحديث الصفحة
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="temp-emails">
            <h3>📧 مواقع الإيميلات المؤقتة</h3>
            <div class="email-links">
                <a href="https://temp-mail.org/en/" target="_blank" class="email-link">
                    📮 Temp Mail
                </a>
                <a href="https://tempail.com/" target="_blank" class="email-link">
                    ✉️ Tempail
                </a>
                <a href="https://temp-mail.io/en/" target="_blank" class="email-link">
                    📬 Temp Mail IO
                </a>
                <a href="https://run2mail.com/en/" target="_blank" class="email-link">
                    🏃 Run2Mail
                </a>
            </div>
        </div>
    </div>
    
    <a href="admin.php" class="admin-link">⚙️ لوحة التحكم</a>

    <!-- زر الإشعارات -->
    <a href="notifications_panel.php" class="notifications-link">
        🔔 الإشعارات
        <?php if ($unread_notifications > 0): ?>
            <span class="notification-badge"><?php echo $unread_notifications; ?></span>
        <?php endif; ?>
    </a>

    <!-- تحديث الأوقات تلقائياً -->
    <script src="time_updater.js"></script>
</body>
</html>

<?php
$stmt->close();
$conn->close();
?>
