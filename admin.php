<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة إضافة عرض جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_offer'])) {
    $title = trim($_POST['title']);
    $image_url = trim($_POST['image_url']);
    $offer_url = trim($_POST['offer_url']);
    
    if ($title && $image_url && $offer_url) {
        $stmt = $conn->prepare("INSERT INTO offers (title, image_url, offer_url) VALUES (?, ?, ?)");
        $stmt->bind_param("sss", $title, $image_url, $offer_url);
        
        if ($stmt->execute()) {
            $message = '<div class="alert alert-success">✅ تم إضافة العرض بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل في إضافة العرض!</div>';
        }
        $stmt->close();
    } else {
        $message = '<div class="alert alert-error">❌ يرجى ملء جميع الحقول!</div>';
    }
}

// معالجة حذف عرض
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $offer_id = (int)$_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM offers WHERE id = ?");
    $stmt->bind_param("i", $offer_id);
    
    if ($stmt->execute()) {
        $message = '<div class="alert alert-success">✅ تم حذف العرض بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في حذف العرض!</div>';
    }
    $stmt->close();
}

// الحصول على جميع العروض
$offers = $conn->query("SELECT * FROM offers ORDER BY id DESC");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h2 {
            padding: 1.5rem;
            background: #f8f9fa;
            color: #333;
            margin: 0;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .offer-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .offer-url {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            th, td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .offer-image {
                width: 60px;
                height: 45px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ لوحة التحكم</h1>
            <p>إدارة العروض والتحكم في الموقع</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="?logout=1">🚪 تسجيل الخروج</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="form-section">
            <h2>➕ إضافة عرض جديد</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="title">عنوان العرض:</label>
                    <input type="text" id="title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="image_url">رابط الصورة:</label>
                    <input type="url" id="image_url" name="image_url" required>
                </div>
                
                <div class="form-group">
                    <label for="offer_url">رابط العرض:</label>
                    <input type="url" id="offer_url" name="offer_url" required>
                </div>
                
                <button type="submit" name="add_offer" class="btn">إضافة العرض</button>
            </form>
        </div>
        
        <div class="offers-table">
            <h2>📋 العروض الحالية</h2>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>الصورة</th>
                            <th>العنوان</th>
                            <th>الرابط</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($offers->num_rows > 0): ?>
                            <?php while ($offer = $offers->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $offer['id']; ?></td>
                                    <td>
                                        <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                                             alt="صورة العرض" class="offer-image">
                                    </td>
                                    <td><?php echo htmlspecialchars($offer['title']); ?></td>
                                    <td>
                                        <div class="offer-url" title="<?php echo htmlspecialchars($offer['offer_url']); ?>">
                                            <?php echo htmlspecialchars($offer['offer_url']); ?>
                                        </div>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?></td>
                                    <td>
                                        <a href="?delete=<?php echo $offer['id']; ?>" 
                                           class="btn btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟')">
                                            🗑️ حذف
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" style="text-align: center; color: #666;">
                                    لا توجد عروض حالياً
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>

<?php
// معالجة تسجيل الخروج
if (isset($_GET['logout'])) {
    session_start();
    session_destroy();
    header('Location: index.php');
    exit;
}

$conn->close();
?>
