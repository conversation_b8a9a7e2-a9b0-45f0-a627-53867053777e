<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$fixes = [];
$errors = [];

// إصلاح 1: إضافة حقل الوصف
try {
    $conn = getDBConnection();
    $check_column = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
    
    if ($check_column->num_rows == 0) {
        $sql = "ALTER TABLE offers ADD COLUMN description TEXT COLLATE utf8mb4_unicode_ci AFTER title";
        if ($conn->query($sql)) {
            $fixes[] = "✅ تم إضافة حقل الوصف بنجاح";
        } else {
            $errors[] = "❌ فشل في إضافة حقل الوصف: " . $conn->error;
        }
    } else {
        $fixes[] = "✅ حقل الوصف موجود مسبقاً";
    }
    $conn->close();
} catch (Exception $e) {
    $errors[] = "❌ خطأ في إضافة حقل الوصف: " . $e->getMessage();
}

// إصلاح 2: تحديث العروض الموجودة بوصف افتراضي
try {
    $conn = getDBConnection();
    $check_column = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
    
    if ($check_column->num_rows > 0) {
        $update_sql = "UPDATE offers SET description = CONCAT('عرض من CPALead - ', title) WHERE description IS NULL OR description = ''";
        $result = $conn->query($update_sql);
        $affected = $conn->affected_rows;
        
        if ($affected > 0) {
            $fixes[] = "✅ تم تحديث {$affected} عرض بوصف افتراضي";
        } else {
            $fixes[] = "✅ جميع العروض تحتوي على وصف";
        }
    }
    $conn->close();
} catch (Exception $e) {
    $errors[] = "❌ خطأ في تحديث العروض: " . $e->getMessage();
}

// إصلاح 3: التحقق من جدول الإشعارات
try {
    $conn = getDBConnection();
    $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
    
    if ($check_table->num_rows == 0) {
        $sql = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(50) NOT NULL COLLATE utf8mb4_unicode_ci,
            title VARCHAR(255) NOT NULL COLLATE utf8mb4_unicode_ci,
            message TEXT NOT NULL COLLATE utf8mb4_unicode_ci,
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_type (type),
            INDEX idx_read (is_read),
            INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($sql)) {
            $fixes[] = "✅ تم إنشاء جدول الإشعارات";
        } else {
            $errors[] = "❌ فشل في إنشاء جدول الإشعارات: " . $conn->error;
        }
    } else {
        $fixes[] = "✅ جدول الإشعارات موجود";
    }
    $conn->close();
} catch (Exception $e) {
    $errors[] = "❌ خطأ في جدول الإشعارات: " . $e->getMessage();
}

// إصلاح 4: التحقق من الجداول الأساسية
$required_tables = ['offers', 'clicks', 'conversions', 'saved_ips'];
try {
    $conn = getDBConnection();
    foreach ($required_tables as $table) {
        $check = $conn->query("SHOW TABLES LIKE '$table'");
        if ($check->num_rows > 0) {
            $fixes[] = "✅ جدول $table موجود";
        } else {
            $errors[] = "❌ جدول $table مفقود";
        }
    }
    $conn->close();
} catch (Exception $e) {
    $errors[] = "❌ خطأ في فحص الجداول: " . $e->getMessage();
}

$success = empty($errors);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح شامل للنظام - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 700px;
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .success {
            color: #28a745;
        }
        
        .error {
            color: #dc3545;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .status-message {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 2rem;
            padding: 1rem;
            border-radius: 8px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .fixes-list, .errors-list {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .fixes-list li, .errors-list li {
            background: #f8f9fa;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .errors-list li {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-info {
            background: #17a2b8;
        }
        
        .btn-info:hover {
            background: #138496;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $success ? '✅' : '⚠️'; ?>
            </div>
            <h1>إصلاح شامل للنظام</h1>
        </div>
        
        <div class="status-message <?php echo $success ? 'status-success' : 'status-error'; ?>">
            <?php if ($success): ?>
                🎉 تم إصلاح جميع المشاكل بنجاح! النظام جاهز للاستخدام.
            <?php else: ?>
                ⚠️ تم إصلاح بعض المشاكل، لكن هناك مشاكل تحتاج إلى انتباه.
            <?php endif; ?>
        </div>
        
        <?php if (!empty($fixes)): ?>
        <div>
            <h3 style="color: #28a745; margin-bottom: 1rem;">✅ الإصلاحات المطبقة:</h3>
            <ul class="fixes-list">
                <?php foreach ($fixes as $fix): ?>
                    <li><?php echo htmlspecialchars($fix); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div>
            <h3 style="color: #dc3545; margin-bottom: 1rem;">❌ المشاكل المتبقية:</h3>
            <ul class="errors-list">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="test_system.php" class="btn btn-info">
                🧪 اختبار النظام
            </a>
            
            <a href="add_test_offers.php" class="btn btn-success">
                🎯 إضافة عروض تجريبية
            </a>
            
            <a href="index.php" class="btn">
                🏠 الصفحة الرئيسية
            </a>
            
            <a href="api_manager.php" class="btn">
                🔧 إدارة API
            </a>
        </div>
        
        <?php if ($success): ?>
            <script>
                // إعادة توجيه تلقائي بعد 5 ثواني
                setTimeout(function() {
                    window.location.href = 'index.php';
                }, 5000);
            </script>
        <?php endif; ?>
    </div>
</body>
</html>
