<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$test_results = [];

// دالة لاختبار الاتصال بـ CPALead
function testCPALeadConnection($api_id = 'test') {
    $results = [];
    
    // اختبار عدة URLs محتملة
    $test_urls = [
        "https://cpalead.com/api/offers?id=$api_id",
        "https://www.cpalead.com/api/offers?id=$api_id",
        "http://cpalead.com/api/offers?id=$api_id",
        "http://www.cpalead.com/api/offers?id=$api_id"
    ];
    
    foreach ($test_urls as $url) {
        $result = [
            'url' => $url,
            'status' => 'unknown',
            'http_code' => 0,
            'response_time' => 0,
            'error' => '',
            'response_size' => 0,
            'redirects' => 0
        ];
        
        $start_time = microtime(true);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPALead-Test/1.0)');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Cache-Control: no-cache'
        ]);
        
        $response = curl_exec($ch);
        $result['http_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $result['response_time'] = round((microtime(true) - $start_time) * 1000, 2);
        $result['error'] = curl_error($ch);
        $result['response_size'] = strlen($response);
        $result['redirects'] = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);
        
        curl_close($ch);
        
        // تحديد حالة النتيجة
        if (!empty($result['error'])) {
            $result['status'] = 'error';
        } elseif ($result['http_code'] == 200) {
            $result['status'] = 'success';
            $result['response_preview'] = substr($response, 0, 200);
        } elseif ($result['http_code'] == 301 || $result['http_code'] == 302) {
            $result['status'] = 'redirect';
        } elseif ($result['http_code'] == 400 || $result['http_code'] == 401) {
            $result['status'] = 'auth_error';
        } else {
            $result['status'] = 'http_error';
        }
        
        $results[] = $result;
    }
    
    return $results;
}

// تشغيل الاختبار
if (isset($_POST['test_connection'])) {
    $api_id = !empty($_POST['api_id']) ? trim($_POST['api_id']) : 'test';
    $test_results = testCPALeadConnection($api_id);
    
    $success_count = 0;
    foreach ($test_results as $result) {
        if ($result['status'] == 'success') {
            $success_count++;
        }
    }
    
    if ($success_count > 0) {
        $message = "✅ تم العثور على {$success_count} اتصال ناجح من أصل " . count($test_results) . " محاولة!";
    } else {
        $message = "❌ فشل في جميع محاولات الاتصال. تحقق من معرف API أو حالة الشبكة.";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بـ CPALead - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .test-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .result-card {
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .result-card.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .result-card.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .result-card.redirect {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .result-card.auth_error {
            background: #e2e3e5;
            border-left-color: #6c757d;
        }
        
        .result-card.http_error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .result-url {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
            word-break: break-all;
        }
        
        .result-status {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .result-details {
            font-size: 0.9rem;
            color: #666;
        }
        
        .result-details div {
            margin-bottom: 0.3rem;
        }
        
        .response-preview {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.8rem;
            margin-top: 0.5rem;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 اختبار الاتصال بـ CPALead</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="test-form">
            <h3 style="margin-bottom: 1rem;">🔧 إعدادات الاختبار</h3>
            <p style="color: #666; margin-bottom: 1rem;">
                أدخل معرف API الخاص بك من CPALead لاختبار الاتصال. اتركه فارغاً لاختبار عام.
            </p>
            
            <form method="POST">
                <div class="form-group">
                    <label for="api_id">معرف API (اختياري):</label>
                    <input type="text" id="api_id" name="api_id" 
                           placeholder="مثال: 12345" 
                           value="<?php echo isset($_POST['api_id']) ? htmlspecialchars($_POST['api_id']) : ''; ?>">
                </div>
                
                <button type="submit" name="test_connection" class="btn">
                    🚀 اختبار الاتصال
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_results)): ?>
        <div>
            <h3 style="color: #333; margin-bottom: 1rem;">📊 نتائج الاختبار</h3>
            
            <div class="results-grid">
                <?php foreach ($test_results as $result): ?>
                    <div class="result-card <?php echo $result['status']; ?>">
                        <div class="result-url">
                            <?php echo htmlspecialchars($result['url']); ?>
                        </div>
                        
                        <div class="result-status">
                            <?php
                            switch ($result['status']) {
                                case 'success':
                                    echo '✅ نجح الاتصال';
                                    break;
                                case 'error':
                                    echo '❌ خطأ في الاتصال';
                                    break;
                                case 'redirect':
                                    echo '🔄 إعادة توجيه';
                                    break;
                                case 'auth_error':
                                    echo '🔐 خطأ في التصريح';
                                    break;
                                case 'http_error':
                                    echo '⚠️ خطأ HTTP';
                                    break;
                                default:
                                    echo '❓ غير معروف';
                            }
                            ?>
                        </div>
                        
                        <div class="result-details">
                            <div><strong>رمز HTTP:</strong> <?php echo $result['http_code']; ?></div>
                            <div><strong>وقت الاستجابة:</strong> <?php echo $result['response_time']; ?> مللي ثانية</div>
                            <div><strong>حجم الاستجابة:</strong> <?php echo $result['response_size']; ?> بايت</div>
                            <div><strong>عدد إعادة التوجيه:</strong> <?php echo $result['redirects']; ?></div>
                            
                            <?php if (!empty($result['error'])): ?>
                                <div><strong>الخطأ:</strong> <?php echo htmlspecialchars($result['error']); ?></div>
                            <?php endif; ?>
                            
                            <?php if (isset($result['response_preview'])): ?>
                                <div><strong>معاينة الاستجابة:</strong></div>
                                <div class="response-preview">
                                    <?php echo htmlspecialchars($result['response_preview']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="fix_sync_issues.php">🔧 إصلاح مشاكل المزامنة</a>
            <a href="run_sync.php">🚀 تشغيل المزامنة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
