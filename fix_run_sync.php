<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$fixes = [];
$errors = [];

// تشغيل الإصلاحات
if (isset($_POST['fix_sync'])) {
    // إصلاح 1: إنشاء ملف معرفات API إذا لم يكن موجود
    if (!file_exists('api_ids.txt')) {
        $api_content = "// ملف معرفات API من CPALead\n";
        $api_content .= "// احصل على معرف API من: https://cpalead.com/dashboard\n";
        $api_content .= "// أضف معرفاتك الحقيقية أدناه (سطر واحد لكل معرف)\n\n";
        $api_content .= "// مثال:\n";
        $api_content .= "// 12345\n";
        $api_content .= "// 67890\n\n";
        
        if (file_put_contents('api_ids.txt', $api_content)) {
            $fixes[] = "✅ تم إنشاء ملف api_ids.txt";
        } else {
            $errors[] = "❌ فشل في إنشاء ملف api_ids.txt";
        }
    } else {
        $fixes[] = "✅ ملف api_ids.txt موجود";
    }
    
    // إصلاح 2: التحقق من حقل الوصف
    try {
        require_once 'safe_description_functions.php';
        
        if (hasDescriptionField()) {
            $fixes[] = "✅ حقل الوصف موجود";
        } else {
            if (ensureDescriptionField()) {
                $fixes[] = "✅ تم إضافة حقل الوصف";
            } else {
                $errors[] = "❌ فشل في إضافة حقل الوصف";
            }
        }
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في التحقق من حقل الوصف: " . $e->getMessage();
    }
    
    // إصلاح 3: إنشاء ملفات السجل
    $log_files = ['sync_results.log', 'auto_sync.log'];
    foreach ($log_files as $log_file) {
        if (!file_exists($log_file)) {
            if (file_put_contents($log_file, "")) {
                $fixes[] = "✅ تم إنشاء ملف السجل: $log_file";
            } else {
                $errors[] = "❌ فشل في إنشاء ملف السجل: $log_file";
            }
        } else {
            $fixes[] = "✅ ملف السجل موجود: $log_file";
        }
    }
    
    // إصلاح 4: التحقق من إعدادات PHP
    $required_extensions = ['curl', 'json', 'mysqli'];
    foreach ($required_extensions as $ext) {
        if (extension_loaded($ext)) {
            $fixes[] = "✅ إضافة PHP متاحة: $ext";
        } else {
            $errors[] = "❌ إضافة PHP مفقودة: $ext";
        }
    }
    
    // إصلاح 5: اختبار الاتصال بقاعدة البيانات
    try {
        $conn = getDBConnection();
        $conn->close();
        $fixes[] = "✅ الاتصال بقاعدة البيانات يعمل";
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }
    
    $message = empty($errors) ? 
        "✅ تم إصلاح جميع المشاكل! المزامنة جاهزة للتشغيل." : 
        "⚠️ تم إصلاح بعض المشاكل، لكن هناك مشاكل تحتاج إلى انتباه.";
}

// إضافة معرف API تجريبي
if (isset($_POST['add_test_api'])) {
    $test_api = trim($_POST['test_api']);
    
    if (!empty($test_api)) {
        $current_content = file_exists('api_ids.txt') ? file_get_contents('api_ids.txt') : '';
        $new_content = $current_content . "\n// معرف تجريبي:\n" . $test_api . "\n";
        
        if (file_put_contents('api_ids.txt', $new_content)) {
            $message = "✅ تم إضافة معرف API التجريبي: $test_api";
        } else {
            $message = "❌ فشل في إضافة معرف API التجريبي!";
        }
    } else {
        $message = "❌ يرجى إدخال معرف API!";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح المزامنة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .fixes-list, .errors-list {
            list-style: none;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .fixes-list li, .errors-list li {
            background: #f8f9fa;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #28a745;
        }
        
        .errors-list li {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح المزامنة</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 مشاكل المزامنة الشائعة:</h3>
            <ul style="margin-right: 1.5rem;">
                <li><strong>ملف api_ids.txt مفقود:</strong> يحتوي على معرفات API من CPALead</li>
                <li><strong>حقل الوصف مفقود:</strong> مطلوب في جدول العروض</li>
                <li><strong>ملفات السجل مفقودة:</strong> لتسجيل عمليات المزامنة</li>
                <li><strong>إضافات PHP مفقودة:</strong> curl, json, mysqli</li>
                <li><strong>مشاكل قاعدة البيانات:</strong> اتصال أو صلاحيات</li>
            </ul>
        </div>
        
        <div class="action-section">
            <h3>🛠️ إصلاح تلقائي</h3>
            <p style="margin-bottom: 1rem; color: #666;">
                سيتم فحص وإصلاح جميع المشاكل الشائعة تلقائياً
            </p>
            
            <form method="POST">
                <button type="submit" name="fix_sync" class="btn btn-success">
                    🔧 إصلاح جميع المشاكل
                </button>
            </form>
        </div>
        
        <div class="action-section">
            <h3>📝 إضافة معرف API تجريبي</h3>
            <p style="margin-bottom: 1rem; color: #666;">
                أضف معرف API من CPALead لاختبار المزامنة
            </p>
            
            <form method="POST">
                <div class="form-group">
                    <label for="test_api">معرف API:</label>
                    <input type="text" id="test_api" name="test_api" 
                           placeholder="مثال: 12345" 
                           value="">
                </div>
                
                <button type="submit" name="add_test_api" class="btn btn-warning">
                    ➕ إضافة معرف API
                </button>
            </form>
        </div>
        
        <?php if (!empty($fixes)): ?>
        <div>
            <h3 style="color: #28a745; margin-bottom: 1rem;">✅ الإصلاحات المطبقة:</h3>
            <ul class="fixes-list">
                <?php foreach ($fixes as $fix): ?>
                    <li><?php echo htmlspecialchars($fix); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div>
            <h3 style="color: #dc3545; margin-bottom: 1rem;">❌ المشاكل المتبقية:</h3>
            <ul class="errors-list">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="test_sync.php">🧪 اختبار المزامنة</a>
            <a href="run_sync.php">🚀 تشغيل المزامنة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
