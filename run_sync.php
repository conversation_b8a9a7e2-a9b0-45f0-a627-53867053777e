<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$results = [];
$errors = [];
$success = false;

// دالة لكتابة السجل
function logMessage($message) {
    global $results;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message";
    $results[] = $logEntry;
    
    // كتابة في ملف السجل أيضاً
    file_put_contents('sync_results.log', $logEntry . "\n", FILE_APPEND | LOCK_EX);
}

// دالة لجلب العروض من CPALead
function fetchOffersFromCPALead() {
    global $errors;
    
    // قراءة معرفات API
    $apiIds = [];
    if (file_exists('api_ids.txt')) {
        $apiIds = array_filter(array_map('trim', file('api_ids.txt')));
    }
    
    if (empty($apiIds)) {
        $errors[] = "لا توجد معرفات API محفوظة";
        return [];
    }
    
    $allOffers = [];
    
    foreach ($apiIds as $apiId) {
        $url = "https://cpalead.com/api/offers?id=$apiId";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPALead-Sync/1.0)');
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $errors[] = "خطأ في الاتصال بـ API ($apiId): $error";
            continue;
        }
        
        if ($httpCode !== 200) {
            $errors[] = "خطأ HTTP ($apiId): $httpCode";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if (!$data || !isset($data['offers'])) {
            $errors[] = "استجابة غير صحيحة من API ($apiId)";
            continue;
        }
        
        $allOffers = array_merge($allOffers, $data['offers']);
        logMessage("تم جلب " . count($data['offers']) . " عرض من API: $apiId");
    }
    
    return $allOffers;
}

// دالة لمعالجة العروض
function processOffers($offers) {
    global $errors;
    
    $imported = 0;
    $updated = 0;
    $skipped = 0;
    
    $conn = getDBConnection();
    
    foreach ($offers as $offer) {
        try {
            // تنظيف البيانات
            $title = isset($offer['title']) ? trim($offer['title']) : '';
            $link = isset($offer['link']) ? trim($offer['link']) : '';
            $amount = isset($offer['amount']) ? floatval($offer['amount']) : 0;
            $countries = isset($offer['countries']) ? trim($offer['countries']) : '';
            $device = isset($offer['device']) ? trim($offer['device']) : 'all';
            
            if (empty($title) || empty($link)) {
                $skipped++;
                continue;
            }
            
            // تحسين العنوان
            $enhanced_title = $title;
            if ($amount > 0) {
                $enhanced_title .= " - $" . number_format($amount, 2);
            }
            if (!empty($device) && $device !== 'all') {
                $enhanced_title .= " (" . ucfirst($device) . ")";
            }
            
            // التحقق من وجود العرض
            $checkStmt = $conn->prepare("SELECT id, is_active FROM offers WHERE title = ? OR offer_url = ?");
            $checkStmt->bind_param("ss", $enhanced_title, $link);
            $checkStmt->execute();
            $existing = $checkStmt->get_result();
            
            if ($existing->num_rows == 0) {
                // إضافة عرض جديد
                $description = extractDescription($offer, $enhanced_title);
                $image_url = isset($offer['creatives'][0]) ? $offer['creatives'][0] : 'https://via.placeholder.com/300x200/667eea/white?text=CPA+Offer';
                
                $insert_id = safeInsertOffer(
                    $enhanced_title,
                    $description,
                    $image_url,
                    $link,
                    $countries,
                    $device,
                    $amount,
                    'CPI',
                    1
                );
                
                if ($insert_id) {
                    $imported++;
                    logMessage("تم إضافة عرض جديد: $enhanced_title");
                }
            } else {
                // تحديث العرض الموجود
                $existingOffer = $existing->fetch_assoc();
                if ($existingOffer['is_active'] == 0) {
                    $updateStmt = $conn->prepare("UPDATE offers SET is_active = 1, updated_at = NOW() WHERE id = ?");
                    $updateStmt->bind_param("i", $existingOffer['id']);
                    if ($updateStmt->execute()) {
                        $updated++;
                        logMessage("تم تفعيل العرض: $enhanced_title");
                    }
                    $updateStmt->close();
                } else {
                    $skipped++;
                }
            }
            
            $checkStmt->close();
            
        } catch (Exception $e) {
            $errors[] = "خطأ في معالجة العرض: " . $e->getMessage();
        }
    }
    
    $conn->close();
    
    return [
        'imported' => $imported,
        'updated' => $updated,
        'skipped' => $skipped
    ];
}

// تشغيل المزامنة
if (isset($_POST['run_sync']) || isset($_GET['auto'])) {
    logMessage("=== بدء المزامنة ===");
    
    try {
        // التأكد من وجود حقل الوصف
        if (!hasDescriptionField()) {
            ensureDescriptionField();
            logMessage("تم إضافة حقل الوصف");
        }
        
        // جلب العروض من CPALead
        $offers = fetchOffersFromCPALead();
        
        if (empty($offers) && empty($errors)) {
            $errors[] = "لم يتم العثور على عروض";
        } elseif (!empty($offers)) {
            // معالجة العروض
            $stats = processOffers($offers);
            
            logMessage("النتائج: " . $stats['imported'] . " جديد، " . $stats['updated'] . " محدث، " . $stats['skipped'] . " متجاهل");
            $success = true;
        }
        
        logMessage("=== انتهاء المزامنة ===");
        
    } catch (Exception $e) {
        $errors[] = "خطأ عام في المزامنة: " . $e->getMessage();
        logMessage("خطأ: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشغيل المزامنة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .sync-form {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 تشغيل المزامنة</h1>
        
        <div class="sync-form">
            <h3>مزامنة العروض من CPALead</h3>
            <p style="margin: 1rem 0; color: #666;">
                سيتم جلب العروض الجديدة من CPALead API وإضافتها إلى قاعدة البيانات
            </p>
            
            <form method="POST">
                <button type="submit" name="run_sync" class="btn btn-success">
                    🚀 تشغيل المزامنة الآن
                </button>
            </form>
        </div>
        
        <?php if (!empty($results) || !empty($errors)): ?>
        <div class="results-section">
            <?php if ($success && empty($errors)): ?>
                <div class="alert alert-success">
                    ✅ تمت المزامنة بنجاح!
                </div>
            <?php elseif (!empty($errors)): ?>
                <div class="alert alert-error">
                    ❌ حدثت بعض الأخطاء أثناء المزامنة:
                    <ul style="margin-top: 0.5rem;">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($results)): ?>
                <div class="alert alert-info">
                    <strong>📋 سجل المزامنة:</strong>
                </div>
                <div class="log-container">
<?php echo htmlspecialchars(implode("\n", $results)); ?>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="sync_manager.php" class="btn">🔧 إدارة المزامنة</a>
            <a href="api_manager.php" class="btn">📡 إدارة API</a>
            <a href="index.php" class="btn">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
