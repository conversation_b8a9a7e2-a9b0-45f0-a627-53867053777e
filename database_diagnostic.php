<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$issues = validateDatabaseStructure();
$has_description = hasDescriptionField();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص قاعدة البيانات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 5px solid #667eea;
        }
        
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .status-description {
            color: #666;
            line-height: 1.6;
        }
        
        .issues-list {
            list-style: none;
            padding: 0;
        }
        
        .issues-list li {
            background: #fff;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #dc3545;
        }
        
        .action-buttons {
            text-align: center;
            margin: 2rem 0;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .database-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .info-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .info-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص قاعدة البيانات</h1>
        
        <!-- حالة حقل الوصف -->
        <div class="status-card <?php echo $has_description ? 'status-success' : 'status-error'; ?>">
            <div class="status-title">
                <?php echo $has_description ? '✅ حقل الوصف موجود' : '❌ حقل الوصف مفقود'; ?>
            </div>
            <div class="status-description">
                <?php if ($has_description): ?>
                    حقل الوصف متاح في جدول العروض ويمكن استخدامه بأمان.
                <?php else: ?>
                    حقل الوصف غير موجود في جدول العروض. هذا يسبب خطأ "Unknown column 'description'".
                <?php endif; ?>
            </div>
        </div>
        
        <!-- مشاكل قاعدة البيانات -->
        <?php if (!empty($issues)): ?>
        <div class="status-card status-error">
            <div class="status-title">❌ مشاكل في قاعدة البيانات</div>
            <div class="status-description">
                تم العثور على المشاكل التالية:
                <ul class="issues-list">
                    <?php foreach ($issues as $issue): ?>
                        <li><?php echo htmlspecialchars($issue); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php else: ?>
        <div class="status-card status-success">
            <div class="status-title">✅ قاعدة البيانات سليمة</div>
            <div class="status-description">
                جميع الجداول والحقول المطلوبة موجودة وتعمل بشكل صحيح.
            </div>
        </div>
        <?php endif; ?>
        
        <!-- معلومات قاعدة البيانات -->
        <div class="database-info">
            <h3>📊 معلومات قاعدة البيانات</h3>
            <div class="info-grid">
                <?php
                try {
                    $conn = getDBConnection();
                    
                    // عدد العروض
                    $offers_count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
                    
                    // عدد الضغطات
                    $clicks_count = $conn->query("SELECT COUNT(*) as count FROM clicks")->fetch_assoc()['count'];
                    
                    // عدد التحويلات
                    $conversions_count = $conn->query("SELECT COUNT(*) as count FROM conversions")->fetch_assoc()['count'];
                    
                    // عدد IPs المحفوظة
                    $saved_ips_count = $conn->query("SELECT COUNT(*) as count FROM saved_ips")->fetch_assoc()['count'];
                    
                    $conn->close();
                } catch (Exception $e) {
                    $offers_count = $clicks_count = $conversions_count = $saved_ips_count = 'خطأ';
                }
                ?>
                
                <div class="info-item">
                    <div class="info-value"><?php echo $offers_count; ?></div>
                    <div class="info-label">العروض</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?php echo $clicks_count; ?></div>
                    <div class="info-label">الضغطات</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?php echo $conversions_count; ?></div>
                    <div class="info-label">التحويلات</div>
                </div>
                
                <div class="info-item">
                    <div class="info-value"><?php echo $saved_ips_count; ?></div>
                    <div class="info-label">IPs محفوظة</div>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <?php if (!$has_description): ?>
                <a href="fix_description_field.php" class="btn btn-danger">
                    🔧 إصلاح حقل الوصف فوراً
                </a>
            <?php endif; ?>
            
            <a href="add_test_offers.php" class="btn btn-success">
                🧪 إضافة عروض تجريبية
            </a>
            
            <a href="api_manager.php" class="btn">
                🔧 إدارة API
            </a>
            
            <a href="index.php" class="btn">
                🏠 الصفحة الرئيسية
            </a>
        </div>
        
        <!-- تعليمات الإصلاح -->
        <?php if (!$has_description): ?>
        <div class="status-card status-warning">
            <div class="status-title">⚠️ خطوات الإصلاح</div>
            <div class="status-description">
                <strong>لإصلاح مشكلة "Unknown column 'description'":</strong><br><br>
                
                <strong>الطريقة 1 - إصلاح تلقائي:</strong><br>
                1. اضغط على زر "إصلاح حقل الوصف فوراً" أعلاه<br>
                2. انتظر حتى يتم الإصلاح<br>
                3. ستتم إعادة توجيهك للصفحة الرئيسية<br><br>
                
                <strong>الطريقة 2 - إصلاح يدوي:</strong><br>
                1. اذهب إلى phpMyAdmin أو أي أداة إدارة قواعد البيانات<br>
                2. اختر قاعدة البيانات الخاصة بك<br>
                3. اختر جدول "offers"<br>
                4. نفذ هذا الأمر: <code>ALTER TABLE offers ADD COLUMN description TEXT COLLATE utf8mb4_unicode_ci AFTER title</code><br><br>
                
                <strong>بعد الإصلاح:</strong><br>
                • ستتمكن من استيراد العروض من CPALead مع الوصف<br>
                • ستظهر أوصاف العروض في الصفحة الرئيسية<br>
                • ستعمل جميع الميزات الجديدة بشكل طبيعي
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
