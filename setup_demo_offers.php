<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة إضافة العروض التجريبية
if (isset($_POST['create_demo_offers'])) {
    // عروض تجريبية
    $demo_offers = [
        [
            'title' => 'تطبيق ألعاب مجاني - iOS',
            'image_url' => 'https://via.placeholder.com/300x200/4CAF50/white?text=iOS+Game',
            'offer_url' => 'https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=1&ip={user_ip}',
            'countries' => 'US,CA,GB,AU',
            'device' => 'mobile',
            'amount' => 2.50,
            'payout_type' => 'install'
        ],
        [
            'title' => 'استطلاع رأي - ربح سريع',
            'image_url' => 'https://via.placeholder.com/300x200/2196F3/white?text=Survey',
            'offer_url' => 'https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=2&ip={user_ip}',
            'countries' => 'US,UK,CA',
            'device' => 'desktop',
            'amount' => 1.75,
            'payout_type' => 'completion'
        ],
        [
            'title' => 'تسجيل مجاني - موقع تسوق',
            'image_url' => 'https://via.placeholder.com/300x200/FF9800/white?text=Shopping',
            'offer_url' => 'https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=3&ip={user_ip}',
            'countries' => 'US,CA,AU,NZ',
            'device' => 'all',
            'amount' => 3.00,
            'payout_type' => 'registration'
        ],
        [
            'title' => 'تطبيق توصيل طعام - Android',
            'image_url' => 'https://via.placeholder.com/300x200/E91E63/white?text=Food+App',
            'offer_url' => 'https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=4&ip={user_ip}',
            'countries' => 'US,CA,GB',
            'device' => 'mobile',
            'amount' => 4.25,
            'payout_type' => 'install'
        ],
        [
            'title' => 'خدمة بث مجانية - تجربة',
            'image_url' => 'https://via.placeholder.com/300x200/9C27B0/white?text=Streaming',
            'offer_url' => 'https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=5&ip={user_ip}',
            'countries' => 'US,CA,GB,AU,DE',
            'device' => 'all',
            'amount' => 5.50,
            'payout_type' => 'trial'
        ]
    ];
    
    $added_count = 0;
    
    foreach ($demo_offers as $offer) {
        // التحقق من عدم وجود العرض مسبقاً
        $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ?");
        $checkStmt->bind_param("s", $offer['title']);
        $checkStmt->execute();
        $exists = $checkStmt->get_result();
        
        if ($exists->num_rows == 0) {
            $insertStmt = $conn->prepare("
                INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)
            ");
            $insertStmt->bind_param(
                "sssssds",
                $offer['title'],
                $offer['image_url'],
                $offer['offer_url'],
                $offer['countries'],
                $offer['device'],
                $offer['amount'],
                $offer['payout_type']
            );
            
            if ($insertStmt->execute()) {
                $added_count++;
            }
            $insertStmt->close();
        }
        $checkStmt->close();
    }
    
    if ($added_count > 0) {
        $message = '<div class="alert alert-success">✅ تم إضافة ' . $added_count . ' عرض تجريبي بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-info">ℹ️ العروض التجريبية موجودة مسبقاً.</div>';
    }
}

// الحصول على إحصائيات العروض
$stats = $conn->query("
    SELECT 
        COUNT(*) as total_offers,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_offers
    FROM offers
")->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد العروض التجريبية - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        .offers-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .offer-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .offer-card h5 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .offer-details {
            font-size: 0.9rem;
            color: #666;
        }
        
        .offer-details span {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 0.2rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .offers-preview {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 إعداد العروض التجريبية</h1>
            <p>إضافة عروض تجريبية لاختبار نظام Postback</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="postback_wizard.php">🧙‍♂️ معالج Postback</a>
                <a href="cpalead_simulator.php">🎭 محاكي CPALead</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total_offers']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['active_offers']; ?></div>
                <div class="stats-label">✅ عروض نشطة</div>
            </div>
        </div>
        
        <!-- إضافة العروض التجريبية -->
        <div class="form-section">
            <h3>🎯 إضافة العروض التجريبية</h3>
            
            <?php if ($stats['total_offers'] == 0): ?>
                <div class="alert alert-warning">
                    ⚠️ <strong>لا توجد عروض في قاعدة البيانات!</strong><br>
                    يجب إضافة عروض تجريبية لاختبار نظام Postback بشكل صحيح.
                </div>
            <?php endif; ?>
            
            <div class="info-box">
                <h4>📋 ما تحتويه العروض التجريبية:</h4>
                <ul>
                    <li>5 عروض متنوعة (تطبيقات، استطلاعات، تسجيلات)</li>
                    <li>روابط مرتبطة بـ CPALead مع معاملات التتبع</li>
                    <li>عمولات مختلفة ($1.75 - $5.50)</li>
                    <li>دعم أجهزة مختلفة (موبايل، ديسكتوب)</li>
                    <li>بلدان متعددة (US, CA, GB, AU)</li>
                </ul>
            </div>
            
            <!-- معاينة العروض -->
            <div class="offers-preview">
                <div class="offer-card">
                    <h5>🎮 تطبيق ألعاب مجاني - iOS</h5>
                    <div class="offer-details">
                        <span>$2.50</span>
                        <span>موبايل</span>
                        <span>US,CA,GB,AU</span>
                    </div>
                </div>
                <div class="offer-card">
                    <h5>📊 استطلاع رأي - ربح سريع</h5>
                    <div class="offer-details">
                        <span>$1.75</span>
                        <span>ديسكتوب</span>
                        <span>US,UK,CA</span>
                    </div>
                </div>
                <div class="offer-card">
                    <h5>🛒 تسجيل مجاني - موقع تسوق</h5>
                    <div class="offer-details">
                        <span>$3.00</span>
                        <span>جميع الأجهزة</span>
                        <span>US,CA,AU,NZ</span>
                    </div>
                </div>
                <div class="offer-card">
                    <h5>🍕 تطبيق توصيل طعام - Android</h5>
                    <div class="offer-details">
                        <span>$4.25</span>
                        <span>موبايل</span>
                        <span>US,CA,GB</span>
                    </div>
                </div>
                <div class="offer-card">
                    <h5>📺 خدمة بث مجانية - تجربة</h5>
                    <div class="offer-details">
                        <span>$5.50</span>
                        <span>جميع الأجهزة</span>
                        <span>US,CA,GB,AU,DE</span>
                    </div>
                </div>
            </div>
            
            <form method="POST">
                <button type="submit" name="create_demo_offers" class="btn btn-success">🎯 إضافة العروض التجريبية</button>
            </form>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="form-section">
            <h3>📚 خطوات ما بعد الإضافة</h3>
            
            <div class="info-box">
                <h4>🚀 الخطوات التالية:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li><strong>إضافة العروض التجريبية</strong> (الخطوة الحالية)</li>
                    <li><strong>إعداد Postback:</strong> <a href="postback_wizard.php">استخدم المعالج التفاعلي</a></li>
                    <li><strong>اختبار النظام:</strong> <a href="cpalead_simulator.php">استخدم المحاكي</a></li>
                    <li><strong>مراجعة النتائج:</strong> <a href="conversions.php">تحقق من التحويلات</a></li>
                    <li><strong>إعداد CPALead:</strong> أضف رابط Postback في لوحة التحكم</li>
                </ol>
            </div>
            
            <div class="info-box">
                <h4>🔧 أدوات مفيدة:</h4>
                <a href="postback_wizard.php" class="btn">🧙‍♂️ معالج Postback</a>
                <a href="cpalead_simulator.php" class="btn">🎭 محاكي CPALead</a>
                <a href="postback_debug.php" class="btn">🔍 تشخيص المشاكل</a>
                <a href="offers_manager.php" class="btn">📋 إدارة العروض</a>
            </div>
        </div>
    </div>
</body>
</html>

<?php $conn->close(); ?>
