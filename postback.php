<?php
require_once 'config.php';

// معالجة Postback من شبكة CPA
// يمكن استقبال البيانات عبر GET أو POST

$offer_id = null;
$user_ip = null;

// محاولة الحصول على offer_id من المعاملات المختلفة
if (isset($_GET['offer_id'])) {
    $offer_id = (int)$_GET['offer_id'];
} elseif (isset($_POST['offer_id'])) {
    $offer_id = (int)$_POST['offer_id'];
} elseif (isset($_GET['subid'])) {
    $offer_id = (int)$_GET['subid'];
} elseif (isset($_POST['subid'])) {
    $offer_id = (int)$_POST['subid'];
}

// محاولة الحصول على IP من المعاملات المختلفة
if (isset($_GET['ip'])) {
    $user_ip = $_GET['ip'];
} elseif (isset($_POST['ip'])) {
    $user_ip = $_POST['ip'];
} elseif (isset($_GET['user_ip'])) {
    $user_ip = $_GET['user_ip'];
} elseif (isset($_POST['user_ip'])) {
    $user_ip = $_POST['user_ip'];
} else {
    // إذا لم يتم تمرير IP، استخدم IP الطلب الحالي
    $user_ip = getUserIP();
}

// التحقق من صحة البيانات
if (!$offer_id || !$user_ip) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required parameters: offer_id and ip'
    ]);
    exit;
}

// التحقق من وجود العرض
$conn = getDBConnection();
$stmt = $conn->prepare("SELECT id, title FROM offers WHERE id = ?");
$stmt->bind_param("i", $offer_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $stmt->close();
    $conn->close();
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Offer not found'
    ]);
    exit;
}

$offer = $result->fetch_assoc();
$stmt->close();

// التحقق من عدم وجود تحويل سابق خلال 24 ساعة
if (hasRecentConversion($offer_id, $user_ip, 24)) {
    $conn->close();
    http_response_code(409);
    echo json_encode([
        'status' => 'error',
        'message' => 'Conversion already recorded within 24 hours'
    ]);
    exit;
}

// البحث عن اسم المستخدم من الضغطات السابقة
$username = '';
$usernameStmt = $conn->prepare("
    SELECT username
    FROM clicks
    WHERE offer_id = ? AND ip_address = ?
    ORDER BY clicked_at DESC
    LIMIT 1
");
$usernameStmt->bind_param("is", $offer_id, $user_ip);
$usernameStmt->execute();
$usernameResult = $usernameStmt->get_result();

if ($usernameResult->num_rows > 0) {
    $username = $usernameResult->fetch_assoc()['username'];
} else {
    // البحث في جدول saved_ips
    $savedIpStmt = $conn->prepare("SELECT username FROM saved_ips WHERE ip_address = ?");
    $savedIpStmt->bind_param("s", $user_ip);
    $savedIpStmt->execute();
    $savedIpResult = $savedIpStmt->get_result();

    if ($savedIpResult->num_rows > 0) {
        $username = $savedIpResult->fetch_assoc()['username'];
    } else {
        // إنشاء اسم مستخدم جديد
        $username = generateRandomUsername();
    }
    $savedIpStmt->close();
}
$usernameStmt->close();

// الحصول على معلومات إضافية
$source = $_GET['source'] ?? $_POST['source'] ?? 'postback';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// تسجيل التحويل مع اسم المستخدم
$stmt = $conn->prepare("INSERT INTO conversions (offer_id, ip_address, username, source, user_agent) VALUES (?, ?, ?, ?, ?)");
$stmt->bind_param("issss", $offer_id, $user_ip, $username, $source, $user_agent);

if ($stmt->execute()) {
    $conversion_id = $conn->insert_id;
    $stmt->close();
    $conn->close();

    // إرسال استجابة نجح
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'Conversion recorded successfully',
        'data' => [
            'conversion_id' => $conversion_id,
            'offer_id' => $offer_id,
            'offer_title' => $offer['title'],
            'ip_address' => $user_ip,
            'username' => $username,
            'source' => $source,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
} else {
    $stmt->close();
    $conn->close();

    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to record conversion'
    ]);
}

// تسجيل الطلب في ملف log (اختياري)
$log_entry = date('Y-m-d H:i:s') . " - Postback received: offer_id=$offer_id, ip=$user_ip\n";
file_put_contents('postback.log', $log_entry, FILE_APPEND | LOCK_EX);
?>
