<?php
// ملف اختبار الاتصال بقاعدة البيانات
require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; padding: 2rem; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo "table { width: 100%; border-collapse: collapse; margin: 1rem 0; }";
echo "th, td { padding: 0.5rem; border: 1px solid #ddd; text-align: right; }";
echo "th { background: #f8f9fa; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🔍 اختبار قاعدة البيانات</h1>";

// معلومات الاتصال
echo "<div class='info'>";
echo "<h3>📋 معلومات الاتصال:</h3>";
echo "<p><strong>الخادم:</strong> " . DB_HOST . "</p>";
echo "<p><strong>المستخدم:</strong> " . DB_USER . "</p>";
echo "<p><strong>قاعدة البيانات:</strong> " . DB_NAME . "</p>";
echo "</div>";

try {
    // اختبار الاتصال
    echo "<h3>🔗 اختبار الاتصال...</h3>";
    $conn = getDBConnection();
    
    if ($conn) {
        echo "<div class='success'>✅ نجح الاتصال بقاعدة البيانات!</div>";
        
        // اختبار الجداول
        echo "<h3>📊 فحص الجداول:</h3>";
        
        $tables = ['offers', 'clicks', 'conversions', 'saved_ips'];
        $table_status = [];
        
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_result->fetch_assoc()['count'];
                $table_status[$table] = ['exists' => true, 'count' => $count];
            } else {
                $table_status[$table] = ['exists' => false, 'count' => 0];
            }
        }
        
        echo "<table>";
        echo "<tr><th>اسم الجدول</th><th>الحالة</th><th>عدد السجلات</th></tr>";
        foreach ($table_status as $table => $status) {
            $status_text = $status['exists'] ? '✅ موجود' : '❌ غير موجود';
            $status_class = $status['exists'] ? 'success' : 'error';
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td style='color: " . ($status['exists'] ? '#28a745' : '#dc3545') . "'>$status_text</td>";
            echo "<td>" . $status['count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختبار العروض
        if ($table_status['offers']['exists']) {
            echo "<h3>🎯 العروض المتاحة:</h3>";
            $offers_result = $conn->query("SELECT id, title FROM offers LIMIT 5");
            if ($offers_result && $offers_result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>المعرف</th><th>العنوان</th></tr>";
                while ($offer = $offers_result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $offer['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($offer['title']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='info'>ℹ️ لا توجد عروض في قاعدة البيانات</div>";
            }
        }

        // اختبار أسماء المستخدمين
        if ($table_status['saved_ips']['exists']) {
            echo "<h3>👤 أسماء المستخدمين المُنشأة:</h3>";
            $usernames_result = $conn->query("SELECT username, ip_address, quality_score FROM saved_ips LIMIT 5");
            if ($usernames_result && $usernames_result->num_rows > 0) {
                echo "<table>";
                echo "<tr><th>اسم المستخدم</th><th>عنوان IP</th><th>نقاط الجودة</th></tr>";
                while ($user = $usernames_result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td style='color: #667eea; font-weight: bold;'>" . htmlspecialchars($user['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($user['ip_address']) . "</td>";
                    echo "<td>" . $user['quality_score'] . "/100</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<div class='info'>ℹ️ لا توجد أسماء مستخدمين في قاعدة البيانات</div>";
            }
        }

        // اختبار إنشاء اسم مستخدم عشوائي
        echo "<h3>🎲 اختبار مولد أسماء المستخدمين الأجنبية (بدون أرقام):</h3>";
        echo "<div class='info'>";
        echo "<p><strong>أمثلة على الأنماط المختلفة:</strong></p>";
        for ($i = 1; $i <= 12; $i++) {
            $randomUsername = generateRandomUsername();
            echo "<p><strong>اسم مستخدم $i:</strong> <span style='color: #667eea; font-weight: bold;'>$randomUsername</span></p>";
        }
        echo "<p style='margin-top: 1rem; color: #666; font-size: 0.9rem;'>";
        echo "<strong>الأنماط الجديدة:</strong> صفة+اسم، اسم أول+اسم، اسم+صفة، صفتان، اسمان، اسم واحد، اسم أول واحد";
        echo "</p>";
        echo "<p style='margin-top: 0.5rem; color: #28a745; font-size: 0.9rem;'>";
        echo "<strong>✅ مميزات جديدة:</strong> بدون أرقام، أنماط أكثر تنوعاً، أسماء أكثر طبيعية";
        echo "</p>";
        echo "</div>";
        
        // معلومات الخادم
        echo "<h3>🖥️ معلومات الخادم:</h3>";
        echo "<div class='info'>";
        echo "<p><strong>إصدار MySQL:</strong> " . $conn->server_info . "</p>";
        echo "<p><strong>إصدار PHP:</strong> " . phpversion() . "</p>";
        echo "<p><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        $conn->close();
        
    } else {
        echo "<div class='error'>❌ فشل الاتصال بقاعدة البيانات!</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ: " . $e->getMessage() . "</div>";
}

echo "<div style='margin-top: 2rem; text-align: center;'>";
echo "<a href='index.php' style='background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px;'>🏠 العودة للصفحة الرئيسية</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
