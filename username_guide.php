<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل استخدام أسماء المستخدمين - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .guide-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .guide-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .guide-section h3 {
            color: #555;
            margin: 1.5rem 0 1rem 0;
        }
        
        .patterns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .pattern-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .pattern-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .pattern-examples {
            color: #666;
            font-size: 0.9rem;
        }
        
        .usage-steps {
            background: #e8f4fd;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .usage-steps ol {
            margin-right: 1.5rem;
        }
        
        .usage-steps li {
            margin-bottom: 0.5rem;
        }
        
        .benefits-list {
            background: #f0f8e8;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .benefits-list ul {
            margin-right: 1.5rem;
        }
        
        .benefits-list li {
            margin-bottom: 0.5rem;
        }
        
        .warning-box {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
            margin: 1rem 0;
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
        
        .highlight {
            background: #667eea;
            color: white;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .patterns-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 دليل استخدام أسماء المستخدمين</h1>
            <p>دليل شامل لفهم واستخدام أسماء المستخدمين المُنشأة تلقائياً</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
            </div>
        </div>
        
        <div class="guide-section">
            <h2>🎯 ما هي أسماء المستخدمين التلقائية؟</h2>
            <p>أسماء المستخدمين التلقائية هي أسماء فريدة يتم إنشاؤها تلقائياً لكل عنوان IP يزور موقعك. هذه الأسماء تُستخدم لتتبع المستخدمين وإنشاء هوية مميزة لكل زائر دون الحاجة لتسجيل حساب.</p>
            
            <div class="benefits-list">
                <h3>✅ فوائد استخدام أسماء المستخدمين التلقائية:</h3>
                <ul>
                    <li><strong>تتبع فريد:</strong> كل IP له اسم مستخدم فريد لا يتكرر</li>
                    <li><strong>سهولة التذكر:</strong> أسماء طبيعية وسهلة التذكر</li>
                    <li><strong>مظهر احترافي:</strong> أسماء تبدو حقيقية ومألوفة</li>
                    <li><strong>تنوع كبير:</strong> ملايين الاحتمالات المختلفة</li>
                    <li><strong>بدون أرقام:</strong> أسماء نظيفة وجميلة</li>
                </ul>
            </div>
        </div>
        
        <div class="guide-section">
            <h2>🎨 أنماط أسماء المستخدمين</h2>
            <p>يتم إنشاء أسماء المستخدمين باستخدام 9 أنماط مختلفة لضمان التنوع والفرادة:</p>
            
            <div class="patterns-grid">
                <div class="pattern-card">
                    <h4>🔥 صفة + اسم</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> FastTiger, CoolDragon, SmartWolf, BraveEagle
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>👤 اسم أول + اسم</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> AlexEagle, MikePhoenix, JohnLion, DavidWolf
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>🔄 اسم + صفة</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> TigerFast, DragonCool, WolfSmart, EagleBrave
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>⚡ اسم أول + صفة</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> AlexCool, MikeFast, JohnSmart, DavidBrave
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>💫 صفتان</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> FastCool, SmartBrave, EpicWild, SuperPro
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>🦁 اسمان</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> TigerWolf, DragonEagle, LionPhoenix, SharkViper
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>🎯 اسم واحد</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> Dragon, Phoenix, Tiger, Warrior, Champion
                    </div>
                </div>
                
                <div class="pattern-card">
                    <h4>👨 اسم أول واحد</h4>
                    <div class="pattern-examples">
                        <strong>أمثلة:</strong> Alex, Mike, John, David, Ryan, Kevin
                    </div>
                </div>
            </div>
        </div>
        
        <div class="guide-section">
            <h2>🚀 كيفية عمل النظام</h2>
            
            <div class="usage-steps">
                <h3>📋 خطوات العمل التلقائي:</h3>
                <ol>
                    <li><strong>زيارة المستخدم:</strong> عندما يزور مستخدم الموقع لأول مرة</li>
                    <li><strong>فحص IP:</strong> يتم فحص عنوان IP الخاص به</li>
                    <li><strong>البحث في قاعدة البيانات:</strong> البحث عن اسم مستخدم موجود لهذا IP</li>
                    <li><strong>إنشاء اسم جديد:</strong> إذا لم يوجد، يتم إنشاء اسم مستخدم فريد</li>
                    <li><strong>الحفظ:</strong> يتم ربط اسم المستخدم بعنوان IP في قاعدة البيانات</li>
                    <li><strong>العرض:</strong> يتم عرض اسم المستخدم في معلومات IP</li>
                </ol>
            </div>
        </div>
        
        <div class="guide-section">
            <h2>💡 طرق الاستخدام العملية</h2>
            
            <h3>1. 🎯 تتبع المستخدمين في CPA</h3>
            <p>يمكن استخدام أسماء المستخدمين لتتبع أداء المستخدمين في حملات CPA:</p>
            <div class="code-example">
المستخدم: FastTiger
- ضغط على العرض رقم 1 في 2024-01-15
- تحويل ناجح بقيمة $2.50
- معدل التحويل: 100%
            </div>
            
            <h3>2. 📊 تحليل البيانات</h3>
            <p>تجميع الإحصائيات حسب أسماء المستخدمين لفهم سلوك الزوار:</p>
            <div class="code-example">
أفضل المستخدمين:
1. CoolDragon - 5 تحويلات
2. SmartWolf - 3 تحويلات  
3. AlexEagle - 2 تحويل
            </div>
            
            <h3>3. 🎮 التخصيص والتفاعل</h3>
            <p>استخدام أسماء المستخدمين لإنشاء تجربة شخصية:</p>
            <div class="code-example">
"مرحباً FastTiger! لديك عرض جديد مناسب لك"
"تهانينا CoolDragon! لقد حققت تحويل ناجح"
            </div>
        </div>
        
        <div class="guide-section">
            <h2>⚙️ الإعدادات والتخصيص</h2>
            
            <h3>🔧 تعديل قوائم الكلمات</h3>
            <p>يمكن تخصيص أسماء المستخدمين عبر تعديل القوائم في ملف <span class="highlight">config.php</span>:</p>
            
            <div class="code-example">
// إضافة صفات جديدة
$adjectives = [
    'Fast', 'Smart', 'Cool', 'Epic',
    'YourNewAdjective' // أضف هنا
];

// إضافة أسماء جديدة  
$nouns = [
    'Tiger', 'Dragon', 'Wolf', 'Eagle',
    'YourNewNoun' // أضف هنا
];
            </div>
            
            <div class="warning-box">
                <strong>⚠️ تنبيه:</strong> عند تعديل القوائم، تأكد من استخدام أسماء إنجليزية صحيحة وتجنب الكلمات المسيئة أو غير المناسبة.
            </div>
        </div>
        
        <div class="guide-section">
            <h2>🔍 استكشاف الأخطاء</h2>
            
            <h3>❌ مشاكل شائعة وحلولها:</h3>
            
            <div class="pattern-card">
                <h4>🔄 تكرار أسماء المستخدمين</h4>
                <p><strong>المشكلة:</strong> ظهور نفس اسم المستخدم لأكثر من IP</p>
                <p><strong>الحل:</strong> النظام يتحقق تلقائياً من عدم التكرار ويعيد المحاولة حتى 10 مرات</p>
            </div>
            
            <div class="pattern-card">
                <h4>📝 أسماء غير مناسبة</h4>
                <p><strong>المشكلة:</strong> إنشاء أسماء قد تكون غير مناسبة</p>
                <p><strong>الحل:</strong> مراجعة وتنقيح قوائم الكلمات في config.php</p>
            </div>
            
            <div class="pattern-card">
                <h4>🗄️ مشاكل قاعدة البيانات</h4>
                <p><strong>المشكلة:</strong> عدم حفظ أسماء المستخدمين</p>
                <p><strong>الحل:</strong> التأكد من وجود حقل username في جدول saved_ips</p>
            </div>
        </div>
        
        <div class="guide-section">
            <h2>📈 أفضل الممارسات</h2>
            
            <div class="benefits-list">
                <h3>✨ نصائح للاستخدام الأمثل:</h3>
                <ul>
                    <li><strong>مراقبة دورية:</strong> راجع أسماء المستخدمين المُنشأة بانتظام</li>
                    <li><strong>تحليل الأداء:</strong> استخدم أسماء المستخدمين في تقارير الأداء</li>
                    <li><strong>التخصيص:</strong> اضبط قوائم الكلمات حسب جمهورك المستهدف</li>
                    <li><strong>النسخ الاحتياطي:</strong> احتفظ بنسخة احتياطية من قاعدة البيانات</li>
                    <li><strong>الاختبار:</strong> اختبر النظام بانتظام للتأكد من عمله</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="usernames.php" class="nav-links" style="display: inline-block;">
                👤 عرض أسماء المستخدمين المُنشأة
            </a>
        </div>
    </div>
</body>
</html>
