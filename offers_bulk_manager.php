<?php
require_once 'config.php';
require_once 'countries.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة حذف عروض متعددة
if (isset($_POST['delete_selected'])) {
    $selected_ids = $_POST['selected_offers'] ?? [];
    if (!empty($selected_ids)) {
        $placeholders = str_repeat('?,', count($selected_ids) - 1) . '?';
        $stmt = $conn->prepare("DELETE FROM offers WHERE id IN ($placeholders)");
        $stmt->bind_param(str_repeat('i', count($selected_ids)), ...$selected_ids);
        
        if ($stmt->execute()) {
            $deleted_count = $stmt->affected_rows;
            $message = '<div class="alert alert-success">✅ تم حذف ' . $deleted_count . ' عرض بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل في حذف العروض!</div>';
        }
        $stmt->close();
    } else {
        $message = '<div class="alert alert-error">❌ لم يتم تحديد أي عروض للحذف!</div>';
    }
}

// معالجة تفعيل/إيقاف عروض متعددة
if (isset($_POST['toggle_selected'])) {
    $selected_ids = $_POST['selected_offers'] ?? [];
    $new_status = (int)$_POST['new_status'];
    
    if (!empty($selected_ids)) {
        $placeholders = str_repeat('?,', count($selected_ids) - 1) . '?';
        $stmt = $conn->prepare("UPDATE offers SET is_active = ?, updated_at = NOW() WHERE id IN ($placeholders)");
        $params = array_merge([$new_status], $selected_ids);
        $types = 'i' . str_repeat('i', count($selected_ids));
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            $updated_count = $stmt->affected_rows;
            $status_text = $new_status ? 'تفعيل' : 'إيقاف';
            $message = '<div class="alert alert-success">✅ تم ' . $status_text . ' ' . $updated_count . ' عرض بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل في تحديث العروض!</div>';
        }
        $stmt->close();
    } else {
        $message = '<div class="alert alert-error">❌ لم يتم تحديد أي عروض!</div>';
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$country_filter = isset($_GET['country']) ? $_GET['country'] : '';

// بناء استعلام البحث
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "title LIKE ?";
    $params[] = "%$search%";
    $types .= 's';
}

if ($status_filter === 'active') {
    $where_conditions[] = "is_active = 1";
} elseif ($status_filter === 'inactive') {
    $where_conditions[] = "is_active = 0";
}

if (!empty($country_filter)) {
    $where_conditions[] = "(countries LIKE ? OR countries IS NULL OR countries = '')";
    $params[] = "%$country_filter%";
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// الحصول على العروض
$sql = "
    SELECT o.*, 
           COUNT(DISTINCT c.id) as clicks_count,
           COUNT(DISTINCT conv.id) as conversions_count
    FROM offers o
    LEFT JOIN clicks c ON o.id = c.offer_id
    LEFT JOIN conversions conv ON o.id = conv.offer_id
    $where_clause
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT 100
";

$stmt = $conn->prepare($sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$offers = $stmt->get_result();

// الحصول على إحصائيات
$stats = $conn->query("
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive
    FROM offers
")->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض المتقدمة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .cairo-time {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 1rem 0;
            display: inline-block;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .filters-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .form-group {
            margin-bottom: 0;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        .bulk-actions-bar {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .bulk-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .selected-count {
            background: #667eea;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .offer-title {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .status-active {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .stats-badge {
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 0.1rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .filters-form {
                grid-template-columns: 1fr;
            }
            
            .bulk-actions-bar {
                flex-direction: column;
                gap: 1rem;
            }
            
            .table-container {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 إدارة العروض المتقدمة</h1>
            <p>تحديد وحذف العروض بشكل جماعي</p>
            <div class="cairo-time">
                🕐 التوقيت المصري: <?php echo getCurrentCairoTime(); ?>
            </div>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="offers_manager.php">📋 إدارة العروض العادية</a>
                <a href="add_test_offers.php">🧪 عروض تجريبية</a>
                <a href="notifications_panel.php">🔔 الإشعارات</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['active']; ?></div>
                <div class="stats-label">✅ عروض نشطة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['inactive']; ?></div>
                <div class="stats-label">❌ عروض متوقفة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $offers->num_rows; ?></div>
                <div class="stats-label">🔍 نتائج البحث</div>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="filters-section">
            <form method="GET" class="filters-form">
                <div class="form-group">
                    <label for="search">🔍 البحث في العناوين:</label>
                    <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="ابحث في عناوين العروض...">
                </div>
                
                <div class="form-group">
                    <label for="status">📊 حالة العرض:</label>
                    <select id="status" name="status">
                        <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>جميع العروض</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>عروض نشطة</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>عروض متوقفة</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="country">🌍 البلد:</label>
                    <select id="country" name="country">
                        <option value="">جميع البلدان</option>
                        <?php 
                        $popularCountries = getPopularCPACountries();
                        foreach ($popularCountries as $code => $name) {
                            $selected = $country_filter === $code ? 'selected' : '';
                            echo "<option value='$code' $selected>$name</option>";
                        }
                        ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn">🔍 بحث</button>
                    <a href="offers_bulk_manager.php" class="btn btn-warning">🔄 إعادة تعيين</a>
                </div>
            </form>
        </div>
        
        <!-- جدول العروض مع التحديد المتعدد -->
        <form method="POST" id="offers-form">
            <div class="offers-table">
                <h3>📋 العروض (<?php echo $offers->num_rows; ?> عرض)</h3>
                
                <?php if ($offers->num_rows > 0): ?>
                <!-- شريط الإجراءات الجماعية -->
                <div class="bulk-actions-bar">
                    <div class="checkbox-container">
                        <input type="checkbox" id="select-all" onchange="toggleAllCheckboxes()">
                        <label for="select-all">تحديد الكل</label>
                        <span class="selected-count" id="selected-count" style="display: none;">0 محدد</span>
                    </div>
                    <div class="bulk-actions">
                        <button type="submit" name="toggle_selected" value="1" class="btn btn-success btn-sm" id="activate-btn" style="display: none;" onclick="return confirm('هل أنت متأكد من تفعيل العروض المحددة؟')">
                            <input type="hidden" name="new_status" value="1">
                            ✅ تفعيل المحدد
                        </button>
                        <button type="submit" name="toggle_selected" value="0" class="btn btn-warning btn-sm" id="deactivate-btn" style="display: none;" onclick="return confirm('هل أنت متأكد من إيقاف العروض المحددة؟')">
                            <input type="hidden" name="new_status" value="0">
                            ⏸️ إيقاف المحدد
                        </button>
                        <button type="submit" name="delete_selected" class="btn btn-danger btn-sm" id="delete-btn" style="display: none;" onclick="return confirm('هل أنت متأكد من حذف العروض المحددة؟ هذا الإجراء لا يمكن التراجع عنه!')">
                            🗑️ حذف المحدد
                        </button>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>تحديد</th>
                                <th>المعرف</th>
                                <th>العنوان</th>
                                <th>الوصف</th>
                                <th>البلدان</th>
                                <th>الجهاز</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإحصائيات</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($offers->num_rows > 0): ?>
                                <?php while ($offer = $offers->fetch_assoc()): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="selected_offers[]" value="<?php echo $offer['id']; ?>" class="offer-checkbox" onchange="updateSelectedCount()">
                                        </td>
                                        <td><?php echo $offer['id']; ?></td>
                                        <td class="offer-title" title="<?php echo htmlspecialchars($offer['title']); ?>">
                                            <?php echo htmlspecialchars(substr($offer['title'], 0, 50)); ?>
                                            <?php if (strlen($offer['title']) > 50) echo '...'; ?>
                                        </td>
                                        <td style="font-size: 0.8rem; max-width: 200px;">
                                            <?php if (!empty($offer['description'])): ?>
                                                <div style="color: #666; line-height: 1.3;" title="<?php echo htmlspecialchars($offer['description']); ?>">
                                                    <?php echo htmlspecialchars(substr($offer['description'], 0, 80)); ?>
                                                    <?php if (strlen($offer['description']) > 80) echo '...'; ?>
                                                </div>
                                            <?php else: ?>
                                                <span style="color: #999; font-style: italic;">لا يوجد وصف</span>
                                            <?php endif; ?>
                                        </td>
                                        <td style="font-size: 0.8rem;">
                                            <?php echo formatCountriesDisplay($offer['countries'], 2); ?>
                                        </td>
                                        <td>
                                            <?php if ($offer['device']): ?>
                                                <span style="background: #17a2b8; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                                    <?php echo ucfirst($offer['device']); ?>
                                                </span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($offer['amount'] > 0): ?>
                                                <span style="background: #ffc107; color: #333; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">
                                                    $<?php echo $offer['amount']; ?> <?php echo $offer['payout_type']; ?>
                                                </span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($offer['is_active']): ?>
                                                <span class="status-active">نشط</span>
                                            <?php else: ?>
                                                <span class="status-inactive">متوقف</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="stats-badge"><?php echo $offer['clicks_count']; ?> ضغطة</span>
                                            <span class="stats-badge"><?php echo $offer['conversions_count']; ?> تحويل</span>
                                        </td>
                                        <td><?php echo formatTime12Hour($offer['created_at']); ?></td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="10" style="text-align: center; padding: 2rem; color: #666;">
                                        لا توجد عروض تطابق معايير البحث
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
    </div>
    
    <script>
        // وظائف التحديد المتعدد
        function toggleAllCheckboxes() {
            const selectAll = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.offer-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateSelectedCount();
        }
        
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('.offer-checkbox:checked');
            const count = checkboxes.length;
            const countElement = document.getElementById('selected-count');
            const activateBtn = document.getElementById('activate-btn');
            const deactivateBtn = document.getElementById('deactivate-btn');
            const deleteBtn = document.getElementById('delete-btn');
            
            if (count > 0) {
                countElement.style.display = 'inline-block';
                countElement.textContent = count + ' محدد';
                activateBtn.style.display = 'inline-block';
                deactivateBtn.style.display = 'inline-block';
                deleteBtn.style.display = 'inline-block';
            } else {
                countElement.style.display = 'none';
                activateBtn.style.display = 'none';
                deactivateBtn.style.display = 'none';
                deleteBtn.style.display = 'none';
            }
            
            // تحديث حالة "تحديد الكل"
            const allCheckboxes = document.querySelectorAll('.offer-checkbox');
            const selectAll = document.getElementById('select-all');
            if (selectAll) {
                selectAll.checked = allCheckboxes.length > 0 && count === allCheckboxes.length;
            }
        }
        
        // تحديث الوقت بنظام 12 ساعة
        setInterval(function() {
            const timeElement = document.querySelector('.cairo-time');
            if (timeElement) {
                const now = new Date();
                const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
                
                const year = cairoTime.getFullYear();
                const month = String(cairoTime.getMonth() + 1).padStart(2, '0');
                const day = String(cairoTime.getDate()).padStart(2, '0');
                
                let hours = cairoTime.getHours();
                const minutes = String(cairoTime.getMinutes()).padStart(2, '0');
                const seconds = String(cairoTime.getSeconds()).padStart(2, '0');
                const ampm = hours >= 12 ? 'PM' : 'AM';
                
                hours = hours % 12;
                hours = hours ? hours : 12;
                
                const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} ${ampm}`;
                timeElement.innerHTML = '🕐 التوقيت المصري: ' + formatted;
            }
        }, 1000);
        
        // تهيئة عداد التحديد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();
        });
    </script>
</body>
</html>

<?php $conn->close(); ?>
