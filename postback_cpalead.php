<?php
require_once 'config.php';

/**
 * CPALead Postback Handler
 * متوافق مع جميع معاملات CPALead Postback
 * 
 * المعاملات المدعومة:
 * - campaign_id: معرف الحملة
 * - campaign_name: اسم الحملة  
 * - subid, subid2, subid3: معرفات التتبع
 * - idfa: Apple IDFA للتطبيقات
 * - gaid: Google GAID للتطبيقات
 * - payout: مبلغ العمولة
 * - ip_address: عنوان IP المستخدم
 * - gateway_id: معرف البوابة
 * - lead_id: معرف العميل المحتمل
 * - country_iso: رمز البلد
 * - password: كلمة مرور Postback
 * - virtual_currency: العملة الافتراضية
 */

// إعداد الأمان - قائمة IPs المسموحة
$allowed_ips = [
    '************', // CPALead Official IP
    '127.0.0.1',    // localhost للاختبار
    '::1',          // IPv6 localhost
];

// الحصول على IP المصدر
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
$forwarded_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? '';
$real_ip = $_SERVER['HTTP_X_REAL_IP'] ?? '';

// استخدام IP الحقيقي إذا كان متوفراً
if (!empty($real_ip)) {
    $client_ip = $real_ip;
} elseif (!empty($forwarded_ip)) {
    $client_ip = explode(',', $forwarded_ip)[0];
}

// فحص IP (يمكن تعطيله للاختبار)
$check_ip = false; // تغيير إلى true في الإنتاج
if ($check_ip && !in_array(trim($client_ip), $allowed_ips)) {
    http_response_code(403);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized IP address',
        'ip' => $client_ip
    ]);
    exit;
}

// الحصول على جميع معاملات CPALead
$campaign_id = $_REQUEST['campaign_id'] ?? null;
$campaign_name = $_REQUEST['campaign_name'] ?? '';
$subid = $_REQUEST['subid'] ?? $campaign_id;
$subid2 = $_REQUEST['subid2'] ?? '';
$subid3 = $_REQUEST['subid3'] ?? '';
$idfa = $_REQUEST['idfa'] ?? '';
$gaid = $_REQUEST['gaid'] ?? '';
$payout = (float)($_REQUEST['payout'] ?? 0);
$ip_address = $_REQUEST['ip_address'] ?? $_REQUEST['ip'] ?? $_REQUEST['user_ip'] ?? getUserIP();
$gateway_id = $_REQUEST['gateway_id'] ?? '';
$lead_id = $_REQUEST['lead_id'] ?? '';
$country_iso = $_REQUEST['country_iso'] ?? '';
$password = $_REQUEST['password'] ?? '';
$virtual_currency = (float)($_REQUEST['virtual_currency'] ?? 0);

// معاملات إضافية للتوافق
$offer_id = $_REQUEST['offer_id'] ?? $subid;
$source = $_REQUEST['source'] ?? 'cpalead';

// كلمة مرور Postback (اختياري)
$postback_password = ''; // يمكن تعيين كلمة مرور هنا

// التحقق من كلمة المرور إذا كانت مطلوبة
if (!empty($postback_password) && $password !== $postback_password) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid postback password'
    ]);
    exit;
}

// التحقق من المعاملات المطلوبة
if (!$offer_id || !$ip_address) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required parameters',
        'required' => ['offer_id/subid/campaign_id', 'ip_address'],
        'received' => [
            'offer_id' => $offer_id,
            'ip_address' => $ip_address
        ]
    ]);
    exit;
}

// الاتصال بقاعدة البيانات
$conn = getDBConnection();

// التحقق من وجود العرض
$stmt = $conn->prepare("SELECT id, title FROM offers WHERE id = ? AND is_active = 1");
$stmt->bind_param("i", $offer_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $stmt->close();
    $conn->close();
    
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Offer not found or inactive',
        'offer_id' => $offer_id
    ]);
    exit;
}

$offer = $result->fetch_assoc();
$stmt->close();

// البحث عن اسم المستخدم
$username = '';

// البحث في الضغطات السابقة
$usernameStmt = $conn->prepare("
    SELECT username 
    FROM clicks 
    WHERE offer_id = ? AND ip_address = ? 
    ORDER BY clicked_at DESC 
    LIMIT 1
");
$usernameStmt->bind_param("is", $offer_id, $ip_address);
$usernameStmt->execute();
$usernameResult = $usernameStmt->get_result();

if ($usernameResult->num_rows > 0) {
    $username = $usernameResult->fetch_assoc()['username'];
} else {
    // البحث في الـ IPs المحفوظة
    $savedIpStmt = $conn->prepare("SELECT username FROM saved_ips WHERE ip_address = ?");
    $savedIpStmt->bind_param("s", $ip_address);
    $savedIpStmt->execute();
    $savedIpResult = $savedIpStmt->get_result();
    
    if ($savedIpResult->num_rows > 0) {
        $username = $savedIpResult->fetch_assoc()['username'];
    } else {
        // إنشاء اسم مستخدم جديد
        $username = generateRandomUsername();
    }
    $savedIpStmt->close();
}
$usernameStmt->close();

// الحصول على معلومات إضافية
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// تسجيل التحويل مع جميع المعلومات
$insertStmt = $conn->prepare("
    INSERT INTO conversions (
        offer_id, ip_address, username, source, user_agent,
        campaign_id, campaign_name, subid, subid2, subid3,
        idfa, gaid, payout, gateway_id, lead_id, 
        country_iso, virtual_currency
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
");

$insertStmt->bind_param(
    "issssissssssdsssd",
    $offer_id, $ip_address, $username, $source, $user_agent,
    $campaign_id, $campaign_name, $subid, $subid2, $subid3,
    $idfa, $gaid, $payout, $gateway_id, $lead_id,
    $country_iso, $virtual_currency
);

if ($insertStmt->execute()) {
    $conversion_id = $conn->insert_id;
    $insertStmt->close();
    
    // تسجيل في ملف السجل
    $log_message = date('Y-m-d H:i:s') . " - CPALead Conversion: ";
    $log_message .= "ID: $conversion_id, Offer: $offer_id, User: $username, ";
    $log_message .= "IP: $ip_address, Payout: $$payout, Lead: $lead_id\n";
    file_put_contents('cpalead_conversions.log', $log_message, FILE_APPEND | LOCK_EX);
    
    $conn->close();
    
    // إرسال استجابة نجح
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'Conversion recorded successfully',
        'data' => [
            'conversion_id' => $conversion_id,
            'offer_id' => $offer_id,
            'offer_title' => $offer['title'],
            'username' => $username,
            'ip_address' => $ip_address,
            'payout' => $payout,
            'lead_id' => $lead_id,
            'campaign_id' => $campaign_id,
            'campaign_name' => $campaign_name,
            'country_iso' => $country_iso,
            'virtual_currency' => $virtual_currency,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} else {
    $insertStmt->close();
    $conn->close();
    
    // تسجيل الخطأ
    $error_message = date('Y-m-d H:i:s') . " - CPALead Conversion Error: ";
    $error_message .= "Offer: $offer_id, IP: $ip_address, Error: " . $conn->error . "\n";
    file_put_contents('cpalead_errors.log', $error_message, FILE_APPEND | LOCK_EX);
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to record conversion',
        'offer_id' => $offer_id
    ]);
}
?>
