<?php
require_once 'config.php';
require_once 'notifications.php';

/**
 * CPALead Postback Handler
 * متوافق مع جميع معاملات CPALead Postback
 * 
 * المعاملات المدعومة:
 * - campaign_id: معرف الحملة
 * - campaign_name: اسم الحملة  
 * - subid, subid2, subid3: معرفات التتبع
 * - idfa: Apple IDFA للتطبيقات
 * - gaid: Google GAID للتطبيقات
 * - payout: مبلغ العمولة
 * - ip_address: عنوان IP المستخدم
 * - gateway_id: معرف البوابة
 * - lead_id: معرف العميل المحتمل
 * - country_iso: رمز البلد
 * - password: كلمة مرور Postback
 * - virtual_currency: العملة الافتراضية
 */

// إعداد الأمان - قائمة IPs المسموحة
$allowed_ips = [
    '************', // CPALead Official IP
    '127.0.0.1',    // localhost للاختبار
    '::1',          // IPv6 localhost
];

// الحصول على IP المصدر
$client_ip = $_SERVER['REMOTE_ADDR'] ?? '';
$forwarded_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? '';
$real_ip = $_SERVER['HTTP_X_REAL_IP'] ?? '';

// استخدام IP الحقيقي إذا كان متوفراً
if (!empty($real_ip)) {
    $client_ip = $real_ip;
} elseif (!empty($forwarded_ip)) {
    $client_ip = explode(',', $forwarded_ip)[0];
}

// فحص IP (يمكن تعطيله للاختبار)
$check_ip = false; // تغيير إلى true في الإنتاج
if ($check_ip && !in_array(trim($client_ip), $allowed_ips)) {
    http_response_code(403);
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized IP address',
        'ip' => $client_ip
    ]);
    exit;
}

// الحصول على جميع معاملات CPALead
$campaign_id = $_REQUEST['campaign_id'] ?? null;
$campaign_name = $_REQUEST['campaign_name'] ?? '';
$subid = $_REQUEST['subid'] ?? null;
$subid2 = $_REQUEST['subid2'] ?? '';
$subid3 = $_REQUEST['subid3'] ?? '';
$idfa = $_REQUEST['idfa'] ?? '';
$gaid = $_REQUEST['gaid'] ?? '';
$payout = (float)($_REQUEST['payout'] ?? 0);
$ip_address = $_REQUEST['ip_address'] ?? $_REQUEST['ip'] ?? $_REQUEST['user_ip'] ?? getUserIP();
$gateway_id = $_REQUEST['gateway_id'] ?? '';
$lead_id = $_REQUEST['lead_id'] ?? '';
$country_iso = $_REQUEST['country_iso'] ?? '';
$password = $_REQUEST['password'] ?? '';
$virtual_currency = (float)($_REQUEST['virtual_currency'] ?? 0);

// معاملات إضافية للتوافق - ترتيب أولوية للحصول على offer_id
$offer_id = $_REQUEST['offer_id'] ?? $_REQUEST['subid'] ?? $_REQUEST['campaign_id'] ?? null;
$source = $_REQUEST['source'] ?? 'cpalead';

// تنظيف وتحويل offer_id إلى رقم
if ($offer_id) {
    // إذا كان النص يحتوي على رقم، استخرجه
    if (preg_match('/(\d+)/', $offer_id, $matches)) {
        $offer_id = (int)$matches[1];
    } else {
        $offer_id = null; // إذا لم يحتوي على رقم، اجعله null
    }
}

// إذا لم نجد offer_id، نحاول استخراجه من معاملات أخرى
if (!$offer_id) {
    // محاولة استخراج من lead_id إذا كان يحتوي على رقم
    if ($lead_id && preg_match('/(\d+)/', $lead_id, $matches)) {
        $offer_id = (int)$matches[1];
    }
    // محاولة استخراج من gateway_id
    elseif ($gateway_id && preg_match('/(\d+)/', $gateway_id, $matches)) {
        $offer_id = (int)$matches[1];
    }
    // محاولة استخراج من campaign_id
    elseif ($campaign_id && preg_match('/(\d+)/', $campaign_id, $matches)) {
        $offer_id = (int)$matches[1];
    }
    // إذا لم نجد أي رقم، استخدم عرض افتراضي للاختبار
    else {
        // البحث عن أول عرض نشط في قاعدة البيانات
        $conn_temp = getDBConnection();
        $first_offer = $conn_temp->query("SELECT id FROM offers WHERE is_active = 1 ORDER BY id ASC LIMIT 1");
        if ($first_offer && $first_offer->num_rows > 0) {
            $offer_id = $first_offer->fetch_assoc()['id'];
        }
        $conn_temp->close();
    }
}

// كلمة مرور Postback (اختياري)
$postback_password = ''; // يمكن تعيين كلمة مرور هنا

// التحقق من كلمة المرور إذا كانت مطلوبة
if (!empty($postback_password) && $password !== $postback_password) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => 'Invalid postback password'
    ]);
    exit;
}

// التحقق من المعاملات المطلوبة
if (!$offer_id || !$ip_address) {
    // تسجيل الخطأ مع جميع المعاملات المستلمة
    $error_log = getCurrentCairoTime() . " - Missing Parameters Error:\n";
    $error_log .= "All received parameters: " . json_encode($_REQUEST) . "\n";
    $error_log .= "Extracted offer_id: " . ($offer_id ?: 'NULL') . "\n";
    $error_log .= "Extracted ip_address: " . ($ip_address ?: 'NULL') . "\n\n";
    file_put_contents('cpalead_errors.log', $error_log, FILE_APPEND | LOCK_EX);

    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required parameters',
        'required' => ['offer_id/subid/campaign_id', 'ip_address'],
        'received' => [
            'offer_id' => $offer_id,
            'subid' => $subid,
            'campaign_id' => $campaign_id,
            'ip_address' => $ip_address,
            'all_params' => $_REQUEST
        ],
        'suggestions' => [
            'Make sure to include subid parameter in your postback URL',
            'Check CPALead postback configuration',
            'Verify tracking URL includes {subid} macro'
        ]
    ]);
    exit;
}

// الاتصال بقاعدة البيانات
$conn = getDBConnection();

// التحقق من وجود العرض
$stmt = $conn->prepare("SELECT id, title FROM offers WHERE id = ? AND is_active = 1");
$stmt->bind_param("i", $offer_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $stmt->close();
    $conn->close();
    
    http_response_code(404);
    echo json_encode([
        'status' => 'error',
        'message' => 'Offer not found or inactive',
        'offer_id' => $offer_id
    ]);
    exit;
}

$offer = $result->fetch_assoc();
$stmt->close();

// البحث عن اسم المستخدم
$username = '';

// البحث في الضغطات السابقة
$usernameStmt = $conn->prepare("
    SELECT username 
    FROM clicks 
    WHERE offer_id = ? AND ip_address = ? 
    ORDER BY clicked_at DESC 
    LIMIT 1
");
$usernameStmt->bind_param("is", $offer_id, $ip_address);
$usernameStmt->execute();
$usernameResult = $usernameStmt->get_result();

if ($usernameResult->num_rows > 0) {
    $username = $usernameResult->fetch_assoc()['username'];
} else {
    // البحث في الـ IPs المحفوظة
    $savedIpStmt = $conn->prepare("SELECT username FROM saved_ips WHERE ip_address = ?");
    $savedIpStmt->bind_param("s", $ip_address);
    $savedIpStmt->execute();
    $savedIpResult = $savedIpStmt->get_result();
    
    if ($savedIpResult->num_rows > 0) {
        $username = $savedIpResult->fetch_assoc()['username'];
    } else {
        // إنشاء اسم مستخدم جديد
        $username = generateRandomUsername();
    }
    $savedIpStmt->close();
}
$usernameStmt->close();

// الحصول على معلومات إضافية
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// تسجيل التحويل مع جميع المعلومات
$insertStmt = $conn->prepare("
    INSERT INTO conversions (
        offer_id, ip_address, username, source, user_agent,
        campaign_id, campaign_name, subid, subid2, subid3,
        idfa, gaid, payout, gateway_id, lead_id, 
        country_iso, virtual_currency
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
");

$insertStmt->bind_param(
    "issssissssssdsssd",
    $offer_id, $ip_address, $username, $source, $user_agent,
    $campaign_id, $campaign_name, $subid, $subid2, $subid3,
    $idfa, $gaid, $payout, $gateway_id, $lead_id,
    $country_iso, $virtual_currency
);

if ($insertStmt->execute()) {
    $conversion_id = $conn->insert_id;
    $insertStmt->close();

    // إضافة إشعار عند التحويل
    notifyConversion($conversion_id, $offer_id, $offer['title'], $username, $ip_address, $payout, $source);

    // تسجيل في ملف السجل
    $log_message = getCurrentCairoTime() . " - CPALead Conversion: ";
    $log_message .= "ID: $conversion_id, Offer: $offer_id, User: $username, ";
    $log_message .= "IP: $ip_address, Payout: $$payout, Lead: $lead_id\n";
    file_put_contents('cpalead_conversions.log', $log_message, FILE_APPEND | LOCK_EX);
    
    $conn->close();
    
    // إرسال استجابة نجح
    http_response_code(200);
    echo json_encode([
        'status' => 'success',
        'message' => 'Conversion recorded successfully',
        'data' => [
            'conversion_id' => $conversion_id,
            'offer_id' => $offer_id,
            'offer_title' => $offer['title'],
            'username' => $username,
            'ip_address' => $ip_address,
            'payout' => $payout,
            'lead_id' => $lead_id,
            'campaign_id' => $campaign_id,
            'campaign_name' => $campaign_name,
            'country_iso' => $country_iso,
            'virtual_currency' => $virtual_currency,
            'timestamp' => getCurrentCairoTime()
        ]
    ]);
    
} else {
    $insertStmt->close();
    $conn->close();
    
    // تسجيل الخطأ
    $error_message = getCurrentCairoTime() . " - CPALead Conversion Error: ";
    $error_message .= "Offer: $offer_id, IP: $ip_address, Error: " . $conn->error . "\n";
    file_put_contents('cpalead_errors.log', $error_message, FILE_APPEND | LOCK_EX);
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to record conversion',
        'offer_id' => $offer_id
    ]);
}
?>
