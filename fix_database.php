<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$messages = [];

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح قاعدة البيانات - موقع CPA</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; padding: 2rem; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🔧 إصلاح قاعدة البيانات</h1>";

// إصلاح ترميز قاعدة البيانات
echo "<h2>📋 إصلاح ترميز الجداول...</h2>";

try {
    // تحويل قاعدة البيانات إلى UTF8MB4
    $conn->query("ALTER DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $messages[] = "✅ تم تحديث ترميز قاعدة البيانات إلى UTF8MB4";
    
    // إصلاح جدول offers
    $conn->query("ALTER TABLE offers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    
    // إضافة الحقول الجديدة إذا لم تكن موجودة
    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'countries'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN countries TEXT COLLATE utf8mb4_unicode_ci AFTER offer_url");
        $messages[] = "✅ تم إضافة حقل countries لجدول offers";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'device'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN device VARCHAR(50) COLLATE utf8mb4_unicode_ci AFTER countries");
        $messages[] = "✅ تم إضافة حقل device لجدول offers";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'amount'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN amount DECIMAL(10,2) DEFAULT 0 AFTER device");
        $messages[] = "✅ تم إضافة حقل amount لجدول offers";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'payout_type'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN payout_type VARCHAR(20) COLLATE utf8mb4_unicode_ci AFTER amount");
        $messages[] = "✅ تم إضافة حقل payout_type لجدول offers";
    }

    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'is_active'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN is_active BOOLEAN DEFAULT TRUE AFTER payout_type");
        $messages[] = "✅ تم إضافة حقل is_active لجدول offers";
    }

    $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'updated_at'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE offers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        $messages[] = "✅ تم إضافة حقل updated_at لجدول offers";
    }
    
    // إصلاح جدول clicks
    $conn->query("ALTER TABLE clicks CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // إضافة حقل username إذا لم يكن موجود
    $result = $conn->query("SHOW COLUMNS FROM clicks LIKE 'username'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE clicks ADD COLUMN username VARCHAR(50) COLLATE utf8mb4_unicode_ci AFTER ip_address");
        $messages[] = "✅ تم إضافة حقل username لجدول clicks";
    }

    $messages[] = "✅ تم إصلاح ترميز جدول clicks";

    // إصلاح جدول conversions
    $conn->query("ALTER TABLE conversions CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // إضافة حقل username إذا لم يكن موجود
    $result = $conn->query("SHOW COLUMNS FROM conversions LIKE 'username'");
    if ($result->num_rows == 0) {
        $conn->query("ALTER TABLE conversions ADD COLUMN username VARCHAR(50) COLLATE utf8mb4_unicode_ci AFTER ip_address");
        $messages[] = "✅ تم إضافة حقل username لجدول conversions";
    }

    // إضافة حقول CPALead إذا لم تكن موجودة
    $cpalead_fields = [
        'campaign_id' => 'VARCHAR(50) COLLATE utf8mb4_unicode_ci',
        'campaign_name' => 'VARCHAR(255) COLLATE utf8mb4_unicode_ci',
        'subid' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'subid2' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'subid3' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'idfa' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'gaid' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'payout' => 'DECIMAL(10,4) DEFAULT 0',
        'gateway_id' => 'VARCHAR(50) COLLATE utf8mb4_unicode_ci',
        'lead_id' => 'VARCHAR(100) COLLATE utf8mb4_unicode_ci',
        'country_iso' => 'VARCHAR(2) COLLATE utf8mb4_unicode_ci',
        'virtual_currency' => 'DECIMAL(10,2) DEFAULT 0'
    ];

    foreach ($cpalead_fields as $field => $definition) {
        $result = $conn->query("SHOW COLUMNS FROM conversions LIKE '$field'");
        if ($result->num_rows == 0) {
            $conn->query("ALTER TABLE conversions ADD COLUMN $field $definition AFTER user_agent");
            $messages[] = "✅ تم إضافة حقل $field لجدول conversions";
        }
    }

    $messages[] = "✅ تم إصلاح ترميز جدول conversions";
    
    // إصلاح جدول saved_ips
    $conn->query("ALTER TABLE saved_ips CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $messages[] = "✅ تم إصلاح ترميز جدول saved_ips";
    
} catch (Exception $e) {
    $messages[] = "❌ خطأ في إصلاح قاعدة البيانات: " . $e->getMessage();
}

// عرض الرسائل
foreach ($messages as $message) {
    if (strpos($message, '✅') !== false) {
        echo "<div class='success'>$message</div>";
    } else {
        echo "<div class='error'>$message</div>";
    }
}

// فحص حالة الجداول
echo "<h2>🔍 فحص حالة الجداول...</h2>";

$tables = ['offers', 'clicks', 'conversions', 'saved_ips'];
foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLE STATUS LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        $tableInfo = $result->fetch_assoc();
        $collation = $tableInfo['Collation'];
        
        if (strpos($collation, 'utf8mb4') !== false) {
            echo "<div class='success'>✅ جدول $table: $collation</div>";
        } else {
            echo "<div class='warning'>⚠️ جدول $table: $collation (يحتاج إصلاح)</div>";
        }
    }
}

echo "<div class='info'>";
echo "<h3>📋 معلومات مهمة:</h3>";
echo "<ul>";
echo "<li>تم إصلاح مشكلة الترميز (Illegal mix of collations)</li>";
echo "<li>تم إضافة حقول جديدة لدعم معلومات العروض من API</li>";
echo "<li>جميع الجداول تستخدم الآن UTF8MB4 Unicode</li>";
echo "<li>يمكنك الآن استيراد العروض من CPALead API بدون مشاكل</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top: 2rem; text-align: center;'>";
echo "<a href='api_manager.php' style='background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px; margin: 0.5rem;'>🔌 إدارة API</a>";
echo "<a href='admin.php' style='background: #28a745; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 5px; margin: 0.5rem;'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";

$conn->close();
?>
