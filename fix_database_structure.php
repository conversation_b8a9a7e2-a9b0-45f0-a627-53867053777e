<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$results = [];

// إصلاح هيكل قاعدة البيانات
if (isset($_POST['fix_database'])) {
    try {
        $conn = getDBConnection();
        $fixed = 0;
        $errors = 0;
        
        // التحقق من وجود جدول العروض
        $tables = $conn->query("SHOW TABLES LIKE 'offers'");
        if ($tables->num_rows == 0) {
            // إنشاء جدول العروض
            $create_table = "
                CREATE TABLE offers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    image_url TEXT,
                    offer_url TEXT NOT NULL,
                    countries VARCHAR(255),
                    device VARCHAR(50) DEFAULT 'all',
                    amount DECIMAL(10,2) DEFAULT 0.00,
                    payout_type VARCHAR(20) DEFAULT 'CPI',
                    is_active TINYINT(1) DEFAULT 1,
                    external_id VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($conn->query($create_table)) {
                $results[] = "✅ تم إنشاء جدول العروض بنجاح";
                $fixed++;
            } else {
                $results[] = "❌ فشل في إنشاء جدول العروض: " . $conn->error;
                $errors++;
            }
        } else {
            $results[] = "ℹ️ جدول العروض موجود بالفعل";
            
            // التحقق من الحقول المطلوبة وإضافتها إذا لم تكن موجودة
            $columns = $conn->query("SHOW COLUMNS FROM offers");
            $existing_columns = [];
            while ($col = $columns->fetch_assoc()) {
                $existing_columns[] = $col['Field'];
            }
            
            $required_columns = [
                'description' => "ALTER TABLE offers ADD COLUMN description TEXT AFTER title",
                'external_id' => "ALTER TABLE offers ADD COLUMN external_id VARCHAR(100) AFTER is_active",
                'created_at' => "ALTER TABLE offers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER external_id",
                'updated_at' => "ALTER TABLE offers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
            ];
            
            foreach ($required_columns as $column => $sql) {
                if (!in_array($column, $existing_columns)) {
                    if ($conn->query($sql)) {
                        $results[] = "✅ تم إضافة حقل $column";
                        $fixed++;
                    } else {
                        $results[] = "❌ فشل في إضافة حقل $column: " . $conn->error;
                        $errors++;
                    }
                } else {
                    $results[] = "ℹ️ حقل $column موجود بالفعل";
                }
            }
        }
        
        // التحقق من وجود جدول الإعدادات
        $settings_table = $conn->query("SHOW TABLES LIKE 'settings'");
        if ($settings_table->num_rows == 0) {
            $create_settings = "
                CREATE TABLE settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            if ($conn->query($create_settings)) {
                $results[] = "✅ تم إنشاء جدول الإعدادات بنجاح";
                $fixed++;
                
                // إضافة إعداد افتراضي لوضع العروض
                $default_setting = "INSERT INTO settings (setting_key, setting_value) VALUES ('offers_mode', 'automatic')";
                if ($conn->query($default_setting)) {
                    $results[] = "✅ تم إضافة إعداد وضع العروض الافتراضي";
                    $fixed++;
                }
            } else {
                $results[] = "❌ فشل في إنشاء جدول الإعدادات: " . $conn->error;
                $errors++;
            }
        } else {
            $results[] = "ℹ️ جدول الإعدادات موجود بالفعل";
        }
        
        $conn->close();
        
        if ($errors == 0) {
            $message = "✅ تم إصلاح قاعدة البيانات بنجاح! ($fixed عنصر تم إصلاحه)";
        } else {
            $message = "⚠️ تم الإصلاح مع بعض الأخطاء ($fixed تم إصلاحه، $errors خطأ)";
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إصلاح قاعدة البيانات: " . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];
try {
    $conn = getDBConnection();
    
    // فحص جدول العروض
    $offers_table = $conn->query("SHOW TABLES LIKE 'offers'");
    if ($offers_table->num_rows > 0) {
        $database_status['offers_table'] = 'موجود';
        
        // فحص الحقول
        $columns = $conn->query("SHOW COLUMNS FROM offers");
        $existing_columns = [];
        while ($col = $columns->fetch_assoc()) {
            $existing_columns[] = $col['Field'];
        }
        
        $required_columns = ['id', 'title', 'description', 'image_url', 'offer_url', 'is_active', 'external_id', 'created_at'];
        $missing_columns = array_diff($required_columns, $existing_columns);
        
        if (empty($missing_columns)) {
            $database_status['offers_columns'] = 'جميع الحقول موجودة';
        } else {
            $database_status['offers_columns'] = 'حقول مفقودة: ' . implode(', ', $missing_columns);
        }
        
        // عدد العروض
        $count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
        $database_status['offers_count'] = "$count عرض";
    } else {
        $database_status['offers_table'] = 'غير موجود';
    }
    
    // فحص جدول الإعدادات
    $settings_table = $conn->query("SHOW TABLES LIKE 'settings'");
    if ($settings_table->num_rows > 0) {
        $database_status['settings_table'] = 'موجود';
        
        // فحص إعداد وضع العروض
        $mode_setting = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'offers_mode'");
        if ($mode_setting && $mode_setting->num_rows > 0) {
            $mode = $mode_setting->fetch_assoc()['setting_value'];
            $database_status['offers_mode'] = $mode;
        } else {
            $database_status['offers_mode'] = 'غير محدد';
        }
    } else {
        $database_status['settings_table'] = 'غير موجود';
    }
    
    $conn->close();
} catch (Exception $e) {
    $database_status['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح هيكل قاعدة البيانات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .status-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .status-value {
            color: #666;
        }
        
        .action-section {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1.5rem 3rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .result-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }
        
        .result-item.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .result-item.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .result-item.info {
            border-left-color: #17a2b8;
            background: #e3f2fd;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح هيكل قاعدة البيانات</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php 
                if (strpos($message, '✅') !== false) echo 'success';
                elseif (strpos($message, '⚠️') !== false) echo 'warning';
                else echo 'error';
            ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="status-grid">
            <?php foreach ($database_status as $key => $value): ?>
                <div class="status-card <?php 
                    if (strpos($value, 'غير موجود') !== false || strpos($value, 'مفقودة') !== false) echo 'error';
                    elseif (strpos($value, 'غير محدد') !== false) echo 'warning';
                    else echo 'success';
                ?>">
                    <div class="status-title">
                        <?php
                        switch ($key) {
                            case 'offers_table': echo 'جدول العروض'; break;
                            case 'offers_columns': echo 'حقول جدول العروض'; break;
                            case 'offers_count': echo 'عدد العروض'; break;
                            case 'settings_table': echo 'جدول الإعدادات'; break;
                            case 'offers_mode': echo 'وضع العروض'; break;
                            case 'error': echo 'خطأ'; break;
                            default: echo $key;
                        }
                        ?>
                    </div>
                    <div class="status-value"><?php echo htmlspecialchars($value); ?></div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="action-section">
            <h3 style="color: #1976d2; margin-bottom: 1rem;">🛠️ إصلاح قاعدة البيانات</h3>
            <p style="color: #666; margin-bottom: 2rem;">
                سيقوم هذا الإجراء بإنشاء الجداول والحقول المطلوبة لنظام إدارة العروض
            </p>
            
            <form method="POST">
                <button type="submit" name="fix_database" class="btn"
                        onclick="return confirm('هل أنت متأكد من إصلاح قاعدة البيانات؟ هذا الإجراء آمن ولن يحذف أي بيانات موجودة.')">
                    🔧 إصلاح قاعدة البيانات
                </button>
            </form>
        </div>
        
        <?php if (!empty($results)): ?>
        <div class="results-section">
            <h3 style="color: #333; margin-bottom: 1rem;">📋 نتائج الإصلاح</h3>
            
            <?php foreach ($results as $result): ?>
                <div class="result-item <?php 
                    if (strpos($result, '✅') !== false) echo 'success';
                    elseif (strpos($result, '❌') !== false) echo 'error';
                    else echo 'info';
                ?>">
                    <?php echo htmlspecialchars($result); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="test_offers_system.php">🧪 اختبار النظام</a>
            <a href="offers_management.php">📋 إدارة العروض</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
