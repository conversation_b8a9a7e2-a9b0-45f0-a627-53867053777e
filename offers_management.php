<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

// تضمين دوال الوصف الآمنة
if (file_exists('safe_description_functions.php')) {
    require_once 'safe_description_functions.php';
} else {
    // إنشاء الدوال الأساسية إذا لم يكن الملف موجود
    function hasDescriptionField() {
        try {
            $conn = getDBConnection();
            $check = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
            $has_field = $check->num_rows > 0;
            $conn->close();
            return $has_field;
        } catch (Exception $e) {
            return false;
        }
    }

    function ensureDescriptionField() {
        if (!hasDescriptionField()) {
            try {
                $conn = getDBConnection();
                $sql = "ALTER TABLE offers ADD COLUMN description TEXT COLLATE utf8mb4_unicode_ci AFTER title";
                $result = $conn->query($sql);
                $conn->close();
                return $result;
            } catch (Exception $e) {
                return false;
            }
        }
        return true;
    }

    function safeInsertOffer($title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active = 1, $external_id = null) {
        $conn = getDBConnection();

        // التحقق من وجود حقل external_id
        $has_external_id = false;
        $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        if ($result->num_rows > 0) {
            $has_external_id = true;
        }

        if (hasDescriptionField() && $has_external_id) {
            // إدراج مع الوصف والمعرف الخارجي
            $stmt = $conn->prepare("
                INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("ssssssdsis", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $external_id);
        } elseif (hasDescriptionField()) {
            // إدراج مع الوصف فقط
            $stmt = $conn->prepare("
                INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("ssssssdsi", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
        } elseif ($has_external_id) {
            // إدراج بدون الوصف مع المعرف الخارجي
            $stmt = $conn->prepare("
                INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("sssssdsiss", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $external_id);
        } else {
            // إدراج بدون الوصف والمعرف الخارجي
            $stmt = $conn->prepare("
                INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("sssssdsi", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
        }

        $result = $stmt->execute();
        $insert_id = $conn->insert_id;
        $stmt->close();
        $conn->close();

        return $result ? $insert_id : false;
    }
}

$message = '';
$errors = [];

// إضافة عرض جديد يدوياً
if (isset($_POST['add_manual_offer'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $image_url = trim($_POST['image_url']);
    $offer_url = trim($_POST['offer_url']);
    $countries = trim($_POST['countries']);
    $device = trim($_POST['device']);
    $amount = floatval($_POST['amount']);
    $payout_type = trim($_POST['payout_type']);

    if (empty($title) || empty($offer_url)) {
        $errors[] = "العنوان ورابط العرض مطلوبان";
    } else {
        // إنشاء صورة افتراضية إذا لم يتم توفير صورة
        if (empty($image_url)) {
            $encodedTitle = urlencode(substr($title, 0, 30));
            $colors = ['667eea', '764ba2', 'f093fb', 'f5576c', '4facfe', '00f2fe', '43e97b', '38f9d7'];
            $bgColor = $colors[array_rand($colors)];
            $image_url = "https://via.placeholder.com/400x250/{$bgColor}/ffffff?text=" . $encodedTitle;
        }

        $insert_id = safeInsertOffer(
            $title,
            $description,
            $image_url,
            $offer_url,
            $countries,
            $device,
            $amount,
            $payout_type,
            1, // نشط
            'manual_' . time() // معرف خارجي للعروض اليدوية
        );

        if ($insert_id) {
            $message = "✅ تم إضافة العرض بنجاح!";
        } else {
            $errors[] = "فشل في إضافة العرض";
        }
    }
}

// تحديث حالة العرض
if (isset($_POST['toggle_offer'])) {
    $offer_id = intval($_POST['offer_id']);
    $new_status = intval($_POST['new_status']);

    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("UPDATE offers SET is_active = ? WHERE id = ?");
        $stmt->bind_param("ii", $new_status, $offer_id);

        if ($stmt->execute()) {
            $status_text = $new_status ? 'تفعيل' : 'إلغاء تفعيل';
            $message = "✅ تم {$status_text} العرض بنجاح!";
        } else {
            $errors[] = "فشل في تحديث حالة العرض";
        }

        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $errors[] = "خطأ في تحديث العرض: " . $e->getMessage();
    }
}

// حذف عرض
if (isset($_POST['delete_offer'])) {
    $offer_id = intval($_POST['offer_id']);

    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("DELETE FROM offers WHERE id = ?");
        $stmt->bind_param("i", $offer_id);

        if ($stmt->execute()) {
            $message = "✅ تم حذف العرض بنجاح!";
        } else {
            $errors[] = "فشل في حذف العرض";
        }

        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $errors[] = "خطأ في حذف العرض: " . $e->getMessage();
    }
}

// تبديل وضع العروض (يدوي/تلقائي)
if (isset($_POST['toggle_mode'])) {
    $new_mode = $_POST['offers_mode'];

    try {
        $conn = getDBConnection();

        // التحقق من وجود جدول الإعدادات
        $result = $conn->query("SHOW TABLES LIKE 'settings'");
        if ($result->num_rows == 0) {
            // إنشاء جدول الإعدادات
            $conn->query("
                CREATE TABLE settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
        }

        // تحديث أو إدراج إعداد وضع العروض
        $stmt = $conn->prepare("
            INSERT INTO settings (setting_key, setting_value)
            VALUES ('offers_mode', ?)
            ON DUPLICATE KEY UPDATE setting_value = ?
        ");
        $stmt->bind_param("ss", $new_mode, $new_mode);

        if ($stmt->execute()) {
            $mode_text = ($new_mode === 'manual') ? 'اليدوي' : 'التلقائي';
            $message = "✅ تم تغيير وضع العروض إلى: {$mode_text}";
        } else {
            $errors[] = "فشل في تحديث وضع العروض";
        }

        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        $errors[] = "خطأ في تحديث الوضع: " . $e->getMessage();
    }
}

// جلب الوضع الحالي
$current_mode = 'automatic'; // افتراضي
try {
    $conn = getDBConnection();
    $result = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'offers_mode'");
    if ($result && $result->num_rows > 0) {
        $current_mode = $result->fetch_assoc()['setting_value'];
    }
    $conn->close();
} catch (Exception $e) {
    // استخدام الوضع الافتراضي
}

// جلب جميع العروض
$offers = [];
try {
    $conn = getDBConnection();
    $result = $conn->query("
        SELECT id, title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id, created_at
        FROM offers
        ORDER BY created_at DESC
    ");

    while ($row = $result->fetch_assoc()) {
        $offers[] = $row;
    }

    $conn->close();
} catch (Exception $e) {
    $errors[] = "خطأ في جلب العروض: " . $e->getMessage();
}

// إحصائيات العروض
$stats = [
    'total' => count($offers),
    'active' => count(array_filter($offers, function($o) { return $o['is_active']; })),
    'inactive' => count(array_filter($offers, function($o) { return !$o['is_active']; })),
    'manual' => count(array_filter($offers, function($o) { return strpos($o['external_id'], 'manual_') === 0; })),
    'automatic' => count(array_filter($offers, function($o) { return strpos($o['external_id'], 'manual_') !== 0; }))
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }

        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .mode-toggle {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
            border-left: 4px solid #2196f3;
        }

        .mode-toggle h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }

        .mode-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .mode-btn {
            padding: 1rem 2rem;
            border: 2px solid #2196f3;
            border-radius: 8px;
            background: white;
            color: #2196f3;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }

        .mode-btn.active {
            background: #2196f3;
            color: white;
        }

        .mode-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .add-offer-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }

        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }

        .offers-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 2rem;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .offers-table th,
        .offers-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }

        .offers-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }

        .offers-table tr:hover {
            background: #f8f9fa;
        }

        .offer-image {
            width: 80px;
            height: 50px;
            object-fit: cover;
            border-radius: 5px;
        }

        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #28a745;
            color: white;
        }

        .status-inactive {
            background: #dc3545;
            color: white;
        }

        .type-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .type-manual {
            background: #17a2b8;
            color: white;
        }

        .type-automatic {
            background: #6f42c1;
            color: white;
        }

        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }

        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }

        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .offers-table {
                font-size: 0.8rem;
            }

            .offers-table th,
            .offers-table td {
                padding: 0.5rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 إدارة العروض</h1>

        <?php if (!empty($message)): ?>
            <div class="message success">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="message error">
                <?php foreach ($errors as $error): ?>
                    <div>❌ <?php echo htmlspecialchars($error); ?></div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- تبديل وضع العروض -->
        <div class="mode-toggle">
            <h3>⚙️ وضع العروض الحالي</h3>
            <form method="POST">
                <div class="mode-buttons">
                    <button type="submit" name="toggle_mode" value="manual"
                            class="mode-btn <?php echo $current_mode === 'manual' ? 'active' : ''; ?>">
                        ✋ يدوي
                    </button>
                    <button type="submit" name="toggle_mode" value="automatic"
                            class="mode-btn <?php echo $current_mode === 'automatic' ? 'active' : ''; ?>">
                        🤖 تلقائي
                    </button>
                </div>
                <input type="hidden" name="offers_mode" value="">
            </form>
            <p style="margin-top: 1rem; color: #666;">
                <strong>الوضع الحالي:</strong>
                <?php echo $current_mode === 'manual' ? '✋ يدوي - يمكنك إضافة وإدارة العروض يدوياً' : '🤖 تلقائي - العروض تأتي من المزامنة التلقائية'; ?>
            </p>
        </div>

        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active']; ?></div>
                <div class="stat-label">العروض النشطة</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['inactive']; ?></div>
                <div class="stat-label">العروض المعطلة</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['manual']; ?></div>
                <div class="stat-label">العروض اليدوية</div>
            </div>

            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['automatic']; ?></div>
                <div class="stat-label">العروض التلقائية</div>
            </div>
        </div>

        <!-- نموذج إضافة عرض جديد -->
        <?php if ($current_mode === 'manual'): ?>
        <div class="add-offer-form">
            <h3 style="margin-bottom: 1rem; color: #333;">➕ إضافة عرض جديد</h3>

            <form method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="title">عنوان العرض *</label>
                        <input type="text" id="title" name="title" required
                               placeholder="مثال: اشترك في Netflix واحصل على $5">
                    </div>

                    <div class="form-group">
                        <label for="offer_url">رابط العرض *</label>
                        <input type="url" id="offer_url" name="offer_url" required
                               placeholder="https://example.com/offer">
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">وصف العرض</label>
                    <textarea id="description" name="description"
                              placeholder="وصف مفصل للعرض وكيفية الحصول على المكافأة..."></textarea>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="image_url">رابط الصورة (اختياري)</label>
                        <input type="url" id="image_url" name="image_url"
                               placeholder="https://example.com/image.jpg">
                        <small style="color: #666;">إذا تركت فارغاً، سيتم إنشاء صورة تلقائياً</small>
                    </div>

                    <div class="form-group">
                        <label for="amount">المبلغ ($)</label>
                        <input type="number" id="amount" name="amount" step="0.01" min="0"
                               placeholder="5.00">
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label for="countries">البلدان المدعومة</label>
                        <input type="text" id="countries" name="countries"
                               placeholder="US,UK,CA أو اتركه فارغاً لجميع البلدان">
                    </div>

                    <div class="form-group">
                        <label for="device">نوع الجهاز</label>
                        <select id="device" name="device">
                            <option value="all">جميع الأجهزة</option>
                            <option value="mobile">الهاتف المحمول</option>
                            <option value="desktop">سطح المكتب</option>
                            <option value="tablet">الجهاز اللوحي</option>
                            <option value="android">أندرويد</option>
                            <option value="ios">iOS</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="payout_type">نوع الدفع</label>
                        <select id="payout_type" name="payout_type">
                            <option value="CPI">CPI - تكلفة التثبيت</option>
                            <option value="CPA">CPA - تكلفة الإجراء</option>
                            <option value="CPL">CPL - تكلفة العميل المحتمل</option>
                            <option value="CPS">CPS - تكلفة البيع</option>
                            <option value="CPM">CPM - تكلفة الألف ظهور</option>
                        </select>
                    </div>
                </div>

                <button type="submit" name="add_manual_offer" class="btn btn-success">
                    ➕ إضافة العرض
                </button>
            </form>
        </div>
        <?php else: ?>
        <div class="add-offer-form" style="text-align: center; background: #fff3cd; border-left-color: #ffc107;">
            <h3 style="color: #856404; margin-bottom: 1rem;">🤖 الوضع التلقائي نشط</h3>
            <p style="color: #856404;">
                العروض تأتي تلقائياً من المزامنة مع CPALead.
                لإضافة عروض يدوياً، قم بتغيير الوضع إلى "يدوي" أعلاه.
            </p>
            <a href="comprehensive_sync.php" class="btn" style="margin-top: 1rem;">
                🚀 تشغيل المزامنة التلقائية
            </a>
        </div>
        <?php endif; ?>

        <!-- جدول العروض -->
        <div style="overflow-x: auto;">
            <table class="offers-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>البلدان</th>
                        <th>الجهاز</th>
                        <th>النوع</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($offers)): ?>
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 2rem; color: #666;">
                                لا توجد عروض حالياً
                                <?php if ($current_mode === 'manual'): ?>
                                    - قم بإضافة عرض جديد أعلاه
                                <?php else: ?>
                                    - قم بتشغيل المزامنة التلقائية
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($offers as $offer): ?>
                            <tr>
                                <td>
                                    <img src="<?php echo htmlspecialchars($offer['image_url']); ?>"
                                         alt="صورة العرض" class="offer-image"
                                         onerror="this.src='https://via.placeholder.com/80x50/667eea/ffffff?text=صورة'">
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars(substr($offer['title'], 0, 50)); ?></strong>
                                    <?php if (strlen($offer['title']) > 50): ?>...<?php endif; ?>
                                    <br>
                                    <small style="color: #666;">
                                        <?php echo htmlspecialchars(substr($offer['description'], 0, 100)); ?>
                                        <?php if (strlen($offer['description']) > 100): ?>...<?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if ($offer['amount'] > 0): ?>
                                        <strong style="color: #28a745;">$<?php echo number_format($offer['amount'], 2); ?></strong>
                                        <br>
                                        <small style="color: #666;"><?php echo htmlspecialchars($offer['payout_type']); ?></small>
                                    <?php else: ?>
                                        <span style="color: #666;">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $countries = $offer['countries'];
                                    if (empty($countries)) {
                                        echo '<span style="color: #666;">جميع البلدان</span>';
                                    } else {
                                        echo htmlspecialchars(substr($countries, 0, 20));
                                        if (strlen($countries) > 20) echo '...';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $device = $offer['device'];
                                    if ($device === 'all') {
                                        echo '<span style="color: #666;">جميع الأجهزة</span>';
                                    } else {
                                        echo htmlspecialchars(ucfirst($device));
                                    }
                                    ?>
                                </td>
                                <td>
                                    <?php if (strpos($offer['external_id'], 'manual_') === 0): ?>
                                        <span class="type-badge type-manual">✋ يدوي</span>
                                    <?php else: ?>
                                        <span class="type-badge type-automatic">🤖 تلقائي</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($offer['is_active']): ?>
                                        <span class="status-badge status-active">✅ نشط</span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">❌ معطل</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small style="color: #666;">
                                        <?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                        <input type="hidden" name="new_status" value="<?php echo $offer['is_active'] ? 0 : 1; ?>">
                                        <button type="submit" name="toggle_offer"
                                                class="btn btn-sm <?php echo $offer['is_active'] ? 'btn-warning' : 'btn-success'; ?>"
                                                onclick="return confirm('هل أنت متأكد؟')">
                                            <?php echo $offer['is_active'] ? '⏸️ إيقاف' : '▶️ تفعيل'; ?>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                        <button type="submit" name="delete_offer" class="btn btn-sm btn-danger"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا العرض؟ لا يمكن التراجع عن هذا الإجراء.')">
                                            🗑️ حذف
                                        </button>
                                    </form>

                                    <a href="<?php echo htmlspecialchars($offer['offer_url']); ?>"
                                       target="_blank" class="btn btn-sm" style="background: #17a2b8;">
                                        🔗 عرض
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="comprehensive_sync.php">🚀 المزامنة التلقائية</a>
            <a href="api_manager.php">📡 إدارة API</a>
        </div>
    </div>

    <script>
        // تحديث قيمة الوضع عند الضغط على الأزرار
        document.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelector('input[name="offers_mode"]').value = this.value;
            });
        });

        // تأكيد تغيير الوضع
        document.querySelectorAll('button[name="toggle_mode"]').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const mode = this.value === 'manual' ? 'اليدوي' : 'التلقائي';
                if (!confirm(`هل أنت متأكد من تغيير الوضع إلى: ${mode}؟`)) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>