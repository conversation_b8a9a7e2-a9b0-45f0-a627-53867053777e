<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة ربط العروض بـ CPALead
if (isset($_POST['link_offers'])) {
    $cpalead_id = trim($_POST['cpalead_id']);
    $postback_url = trim($_POST['postback_url']);
    
    if (!empty($cpalead_id)) {
        // تحديث جميع العروض لربطها بـ CPALead
        $updateStmt = $conn->prepare("
            UPDATE offers 
            SET offer_url = CONCAT(?, '&subid=', id, '&ip={user_ip}') 
            WHERE offer_url NOT LIKE '%cpalead.com%'
        ");
        
        $cpalead_base_url = "https://www.cpalead.com/view.php?id=" . $cpalead_id . "&pub=" . $cpalead_id;
        $updateStmt->bind_param("s", $cpalead_base_url);
        
        if ($updateStmt->execute()) {
            $affected_rows = $updateStmt->affected_rows;
            $message = '<div class="alert alert-success">✅ تم ربط ' . $affected_rows . ' عرض بـ CPALead بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل في ربط العروض: ' . $conn->error . '</div>';
        }
        $updateStmt->close();
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال معرف CPALead!</div>';
    }
}

// معالجة إعداد Postback لجميع العروض
if (isset($_POST['setup_postback'])) {
    $site_url = trim($_POST['site_url']);
    
    if (!empty($site_url)) {
        // إنشاء رابط Postback موحد
        $postback_url = rtrim($site_url, '/') . '/postback.php?offer_id={subid}&ip={user_ip}&source=cpalead';
        
        $message = '<div class="alert alert-success">✅ تم إنشاء رابط Postback بنجاح!</div>';
        $message .= '<div class="alert alert-info"><strong>📋 انسخ هذا الرابط إلى لوحة تحكم CPALead:</strong><br>';
        $message .= '<code style="background: #f8f9fa; padding: 0.5rem; border-radius: 3px; display: block; margin-top: 0.5rem;">' . htmlspecialchars($postback_url) . '</code></div>';
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال رابط الموقع!</div>';
    }
}

// الحصول على إحصائيات العروض
$stats = $conn->query("
    SELECT 
        COUNT(*) as total_offers,
        COUNT(CASE WHEN offer_url LIKE '%cpalead.com%' THEN 1 END) as cpalead_offers,
        COUNT(CASE WHEN offer_url NOT LIKE '%cpalead.com%' THEN 1 END) as other_offers
    FROM offers
")->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ربط CPALead - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        code {
            background: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 ربط CPALead مع Postback</h1>
            <p>ربط جميع العروض مع CPALead وإعداد Postback التلقائي</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total_offers']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['cpalead_offers']; ?></div>
                <div class="stats-label">🔗 عروض CPALead</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['other_offers']; ?></div>
                <div class="stats-label">📋 عروض أخرى</div>
            </div>
        </div>
        
        <!-- ربط العروض بـ CPALead -->
        <div class="form-section">
            <h3>🔗 ربط جميع العروض بـ CPALead</h3>
            
            <div class="info-box">
                <h4>📋 كيفية الحصول على معرف CPALead:</h4>
                <ul>
                    <li>اذهب إلى لوحة تحكم CPALead</li>
                    <li>انسخ معرف الناشر (Publisher ID) الخاص بك</li>
                    <li>أدخله في الحقل أدناه</li>
                    <li>سيتم ربط جميع العروض تلقائياً</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="cpalead_id">🆔 معرف CPALead (Publisher ID):</label>
                    <input type="text" id="cpalead_id" name="cpalead_id" placeholder="مثال: 1941213" required>
                </div>
                
                <button type="submit" name="link_offers" class="btn">🔗 ربط جميع العروض بـ CPALead</button>
            </form>
        </div>
        
        <!-- إعداد Postback -->
        <div class="form-section">
            <h3>📡 إعداد Postback لـ CPALead</h3>
            
            <div class="info-box">
                <h4>📋 خطوات إعداد Postback:</h4>
                <ul>
                    <li>أدخل رابط موقعك الكامل</li>
                    <li>انسخ رابط Postback المُنشأ</li>
                    <li>اذهب إلى لوحة تحكم CPALead</li>
                    <li>أضف رابط Postback في إعدادات الحساب</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="site_url">🌐 رابط الموقع:</label>
                    <input type="url" id="site_url" name="site_url" placeholder="https://yoursite.com" required>
                </div>
                
                <button type="submit" name="setup_postback" class="btn btn-success">📡 إنشاء رابط Postback</button>
            </form>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="form-section">
            <h3>📚 معلومات مهمة</h3>
            
            <div class="alert alert-info">
                <h4>🎯 كيف يعمل النظام:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li><strong>ربط العروض:</strong> جميع العروض تصبح مرتبطة بحسابك في CPALead</li>
                    <li><strong>تتبع المستخدمين:</strong> كل ضغطة تحتوي على معرف العرض و IP المستخدم</li>
                    <li><strong>Postback التلقائي:</strong> عند حدوث تحويل، يتم إرسال إشعار لموقعك</li>
                    <li><strong>ربط اسم المستخدم:</strong> يتم ربط التحويل باسم المستخدم تلقائياً</li>
                </ol>
            </div>
            
            <div class="alert alert-warning">
                <h4>⚠️ ملاحظات مهمة:</h4>
                <ul style="margin-right: 1.5rem;">
                    <li>تأكد من صحة معرف CPALead قبل الربط</li>
                    <li>رابط Postback يجب أن يكون متاح من الإنترنت</li>
                    <li>اختبر Postback بعد الإعداد للتأكد من عمله</li>
                    <li>يمكنك تشغيل الربط عدة مرات بأمان</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>

<?php $conn->close(); ?>
