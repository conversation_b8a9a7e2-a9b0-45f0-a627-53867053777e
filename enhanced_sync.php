<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$results = [];
$errors = [];
$success = false;

// دالة لكتابة السجل
function logMessage($message) {
    global $results;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message";
    $results[] = $logEntry;
    
    // كتابة في ملف السجل أيضاً
    file_put_contents('enhanced_sync.log', $logEntry . "\n", FILE_APPEND | LOCK_EX);
}

// دالة لجلب العروض النشطة من CPALead
function fetchActiveOffersFromCPALead() {
    global $errors;
    
    // قراءة معرفات API
    $apiIds = [];
    if (file_exists('api_ids.txt')) {
        $lines = file('api_ids.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !str_starts_with($line, '//') && !str_starts_with($line, '#')) {
                $apiIds[] = $line;
            }
        }
    }
    
    if (empty($apiIds)) {
        $errors[] = "لا توجد معرفات API صالحة";
        logMessage("لا توجد معرفات API - تحقق من ملف api_ids.txt");
        return [];
    }
    
    logMessage("تم العثور على " . count($apiIds) . " معرف API");
    $allOffers = [];
    
    foreach ($apiIds as $apiId) {
        logMessage("معالجة API ID: $apiId");
        
        // جرب عدة URLs مع معاملات للعروض النشطة فقط
        $urls = [
            "https://cpalead.com/api/offers?id=$apiId&status=active&limit=100",
            "https://cpalead.com/api/offers?id=$apiId&active=1&limit=100",
            "https://cpalead.com/api/offers?id=$apiId&limit=100",
            "https://www.cpalead.com/api/offers?id=$apiId&status=active&limit=100"
        ];
        
        $success = false;
        foreach ($urls as $url) {
            logMessage("جرب URL: $url");
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Cache-Control: no-cache',
                'User-Agent: CPALead-Sync/2.0'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                logMessage("خطأ cURL ($apiId): $error");
                continue;
            }
            
            logMessage("HTTP Code ($apiId): $httpCode");
            
            if ($httpCode == 200) {
                $data = json_decode($response, true);
                $json_error = json_last_error();
                
                if ($json_error !== JSON_ERROR_NONE) {
                    logMessage("خطأ JSON ($apiId): " . json_last_error_msg());
                    logMessage("استجابة خام: " . substr($response, 0, 300));
                    continue;
                }
                
                // التحقق من هيكل البيانات المختلفة
                $offers = [];
                if (isset($data['offers']) && is_array($data['offers'])) {
                    $offers = $data['offers'];
                } elseif (isset($data['data']) && is_array($data['data'])) {
                    $offers = $data['data'];
                } elseif (isset($data['results']) && is_array($data['results'])) {
                    $offers = $data['results'];
                } elseif (is_array($data)) {
                    $offers = $data;
                }
                
                if (!empty($offers)) {
                    // فلترة العروض النشطة فقط
                    $activeOffers = [];
                    foreach ($offers as $offer) {
                        // التحقق من حالة العرض
                        $isActive = true;
                        
                        if (isset($offer['status'])) {
                            $isActive = (strtolower($offer['status']) === 'active' || $offer['status'] == 1);
                        }
                        
                        if (isset($offer['active'])) {
                            $isActive = ($offer['active'] == 1 || $offer['active'] === true);
                        }
                        
                        if (isset($offer['enabled'])) {
                            $isActive = ($offer['enabled'] == 1 || $offer['enabled'] === true);
                        }
                        
                        if ($isActive) {
                            $activeOffers[] = $offer;
                        }
                    }
                    
                    $allOffers = array_merge($allOffers, $activeOffers);
                    logMessage("تم جلب " . count($activeOffers) . " عرض نشط من أصل " . count($offers) . " عرض من API: $apiId");
                    $success = true;
                    break;
                } else {
                    logMessage("لا توجد عروض في الاستجابة ($apiId)");
                    if (isset($data) && is_array($data)) {
                        logMessage("هيكل البيانات: " . json_encode(array_keys($data), JSON_UNESCAPED_UNICODE));
                    }
                }
            } elseif ($httpCode == 400) {
                logMessage("معرف API غير صحيح ($apiId): $httpCode");
            } elseif ($httpCode == 401) {
                logMessage("غير مصرح ($apiId): $httpCode");
            } elseif ($httpCode == 403) {
                logMessage("ممنوع - تحقق من صلاحيات API ($apiId): $httpCode");
            } elseif ($httpCode == 500) {
                logMessage("خطأ في خادم CPALead ($apiId): $httpCode - جرب لاحقاً");
            } else {
                logMessage("رمز HTTP غير متوقع ($apiId): $httpCode");
            }
        }
        
        if (!$success) {
            $errors[] = "فشل في جلب البيانات من API: $apiId";
        }
    }
    
    logMessage("إجمالي العروض النشطة المجلبة: " . count($allOffers));
    return $allOffers;
}

// دالة لمعالجة الصور
function processOfferImage($offer) {
    $imageUrl = 'https://via.placeholder.com/300x200/667eea/white?text=CPA+Offer';
    
    // البحث عن الصورة في عدة حقول محتملة
    if (isset($offer['creatives']) && is_array($offer['creatives']) && !empty($offer['creatives'])) {
        $imageUrl = $offer['creatives'][0];
    } elseif (isset($offer['creative']) && !empty($offer['creative'])) {
        $imageUrl = $offer['creative'];
    } elseif (isset($offer['image_url']) && !empty($offer['image_url'])) {
        $imageUrl = $offer['image_url'];
    } elseif (isset($offer['image']) && !empty($offer['image'])) {
        $imageUrl = $offer['image'];
    } elseif (isset($offer['banner']) && !empty($offer['banner'])) {
        $imageUrl = $offer['banner'];
    } elseif (isset($offer['thumbnail']) && !empty($offer['thumbnail'])) {
        $imageUrl = $offer['thumbnail'];
    }
    
    // التحقق من صحة URL الصورة
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        logMessage("URL صورة غير صحيح، استخدام الصورة الافتراضية");
        $imageUrl = 'https://via.placeholder.com/300x200/667eea/white?text=CPA+Offer';
    }
    
    // اختبار الوصول للصورة
    $headers = @get_headers($imageUrl, 1);
    if (!$headers || strpos($headers[0], '200') === false) {
        logMessage("لا يمكن الوصول للصورة: $imageUrl");
        $imageUrl = 'https://via.placeholder.com/300x200/667eea/white?text=CPA+Offer';
    }
    
    return $imageUrl;
}

// دالة لمعالجة العروض النشطة
function processActiveOffers($offers) {
    global $errors;
    
    if (empty($offers)) {
        logMessage("لا توجد عروض للمعالجة");
        return ['imported' => 0, 'updated' => 0, 'skipped' => 0, 'deactivated' => 0];
    }
    
    $imported = 0;
    $updated = 0;
    $skipped = 0;
    $deactivated = 0;
    
    $conn = getDBConnection();
    
    // إلغاء تفعيل جميع العروض أولاً
    $conn->query("UPDATE offers SET is_active = 0 WHERE is_active = 1");
    $deactivated = $conn->affected_rows;
    logMessage("تم إلغاء تفعيل $deactivated عرض قديم");
    
    // معالجة العروض الجديدة
    foreach ($offers as $index => $offer) {
        try {
            logMessage("معالجة العرض " . ($index + 1) . " من " . count($offers));
            
            // استخراج البيانات الأساسية
            $title = '';
            $link = '';
            $amount = 0;
            $countries = '';
            $device = 'all';
            $offerId = '';
            
            // استخراج معرف العرض
            if (isset($offer['id'])) {
                $offerId = $offer['id'];
            } elseif (isset($offer['offer_id'])) {
                $offerId = $offer['offer_id'];
            }
            
            // استخراج العنوان
            if (isset($offer['title'])) {
                $title = trim($offer['title']);
            } elseif (isset($offer['name'])) {
                $title = trim($offer['name']);
            } elseif (isset($offer['offer_name'])) {
                $title = trim($offer['offer_name']);
            }
            
            // استخراج الرابط
            if (isset($offer['link'])) {
                $link = trim($offer['link']);
            } elseif (isset($offer['url'])) {
                $link = trim($offer['url']);
            } elseif (isset($offer['offer_url'])) {
                $link = trim($offer['offer_url']);
            }
            
            // استخراج المبلغ
            if (isset($offer['amount'])) {
                $amount = floatval($offer['amount']);
            } elseif (isset($offer['payout'])) {
                $amount = floatval($offer['payout']);
            } elseif (isset($offer['price'])) {
                $amount = floatval($offer['price']);
            }
            
            // استخراج البلدان
            if (isset($offer['countries'])) {
                $countries = is_array($offer['countries']) ? implode(',', $offer['countries']) : trim($offer['countries']);
            } elseif (isset($offer['country'])) {
                $countries = is_array($offer['country']) ? implode(',', $offer['country']) : trim($offer['country']);
            }
            
            // استخراج الجهاز
            if (isset($offer['device'])) {
                $device = trim($offer['device']);
            } elseif (isset($offer['platform'])) {
                $device = trim($offer['platform']);
            }
            
            // التحقق من البيانات المطلوبة
            if (empty($title)) {
                logMessage("تم تجاهل عرض بدون عنوان");
                $skipped++;
                continue;
            }
            
            if (empty($link)) {
                logMessage("تم تجاهل عرض بدون رابط: $title");
                $skipped++;
                continue;
            }
            
            // تحسين العنوان
            $enhanced_title = $title;
            if ($amount > 0) {
                $enhanced_title .= " - $" . number_format($amount, 2);
            }
            if (!empty($device) && $device !== 'all') {
                $enhanced_title .= " (" . ucfirst($device) . ")";
            }
            
            // معالجة الصورة
            $image_url = processOfferImage($offer);
            
            // التحقق من وجود العرض
            $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ? OR offer_url = ? OR (external_id = ? AND external_id != '')");
            $checkStmt->bind_param("sss", $enhanced_title, $link, $offerId);
            $checkStmt->execute();
            $existing = $checkStmt->get_result();
            
            if ($existing->num_rows == 0) {
                // إضافة عرض جديد
                $description = extractDescription($offer, $enhanced_title);
                
                $insert_id = safeInsertOffer(
                    $enhanced_title,
                    $description,
                    $image_url,
                    $link,
                    $countries,
                    $device,
                    $amount,
                    'CPI',
                    1, // نشط
                    $offerId // معرف خارجي
                );
                
                if ($insert_id) {
                    $imported++;
                    logMessage("تم إضافة عرض جديد: $enhanced_title");
                } else {
                    logMessage("فشل في إضافة العرض: $enhanced_title");
                }
            } else {
                // تفعيل العرض الموجود وتحديث بياناته
                $existingOffer = $existing->fetch_assoc();
                $updateStmt = $conn->prepare("UPDATE offers SET is_active = 1, image_url = ?, amount = ?, countries = ?, device = ?, updated_at = NOW() WHERE id = ?");
                $updateStmt->bind_param("sdssi", $image_url, $amount, $countries, $device, $existingOffer['id']);
                
                if ($updateStmt->execute()) {
                    $updated++;
                    logMessage("تم تحديث وتفعيل العرض: $enhanced_title");
                } else {
                    logMessage("فشل في تحديث العرض: $enhanced_title");
                }
                $updateStmt->close();
            }
            
            $checkStmt->close();
            
        } catch (Exception $e) {
            $error_msg = "خطأ في معالجة العرض: " . $e->getMessage();
            $errors[] = $error_msg;
            logMessage($error_msg);
        }
    }
    
    $conn->close();
    
    return [
        'imported' => $imported,
        'updated' => $updated,
        'skipped' => $skipped,
        'deactivated' => $deactivated
    ];
}

// تشغيل المزامنة المحسنة
if (isset($_POST['run_enhanced_sync']) || isset($_GET['auto'])) {
    logMessage("=== بدء المزامنة المحسنة ===");
    
    try {
        // التأكد من وجود حقل الوصف
        if (!hasDescriptionField()) {
            logMessage("حقل الوصف غير موجود - محاولة إضافته");
            if (ensureDescriptionField()) {
                logMessage("تم إضافة حقل الوصف بنجاح");
            } else {
                logMessage("فشل في إضافة حقل الوصف - سيتم المتابعة بدونه");
            }
        }
        
        // التحقق من وجود حقل external_id
        $conn = getDBConnection();
        $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        if ($result->num_rows == 0) {
            $conn->query("ALTER TABLE offers ADD COLUMN external_id VARCHAR(50) DEFAULT NULL AFTER id");
            logMessage("تم إضافة حقل external_id");
        }
        $conn->close();
        
        // جلب العروض النشطة من CPALead
        logMessage("بدء جلب العروض النشطة من CPALead");
        $offers = fetchActiveOffersFromCPALead();
        
        if (empty($offers)) {
            if (empty($errors)) {
                $errors[] = "لم يتم العثور على عروض نشطة";
            }
            logMessage("لم يتم جلب أي عروض نشطة");
        } else {
            logMessage("تم جلب " . count($offers) . " عرض نشط - بدء المعالجة");
            
            // معالجة العروض
            $stats = processActiveOffers($offers);
            
            $result_msg = "النتائج النهائية: " . $stats['imported'] . " جديد، " . $stats['updated'] . " محدث، " . $stats['skipped'] . " متجاهل، " . $stats['deactivated'] . " تم إلغاء تفعيله";
            logMessage($result_msg);
            
            if ($stats['imported'] > 0 || $stats['updated'] > 0) {
                $success = true;
            }
        }
        
        logMessage("=== انتهاء المزامنة المحسنة ===");
        
    } catch (Exception $e) {
        $error_msg = "خطأ عام في المزامنة المحسنة: " . $e->getMessage();
        $errors[] = $error_msg;
        logMessage($error_msg);
        logMessage("Stack trace: " . $e->getTraceAsString());
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المزامنة المحسنة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .sync-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem 3rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: bold;
            display: block;
            margin: 2rem auto;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .sync-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        
        .results {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .log-entry {
            background: white;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #667eea;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 المزامنة المحسنة للعروض النشطة</h1>
        
        <div style="background: #e3f2fd; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
            <h3 style="color: #1976d2; margin-bottom: 1rem;">✨ الميزات الجديدة:</h3>
            <ul style="margin-right: 1.5rem; color: #1976d2;">
                <li>🎯 جلب العروض النشطة فقط من CPALead</li>
                <li>🖼️ معالجة محسنة لصور العروض</li>
                <li>🔄 تحديث العروض الموجودة</li>
                <li>❌ إلغاء تفعيل العروض القديمة</li>
                <li>🔍 فحص صحة روابط الصور</li>
                <li>📊 تسجيل مفصل للعمليات</li>
            </ul>
        </div>
        
        <form method="POST">
            <button type="submit" name="run_enhanced_sync" class="sync-button">
                🚀 تشغيل المزامنة المحسنة
            </button>
        </form>
        
        <?php if (!empty($results) || !empty($errors)): ?>
        <div class="results">
            <h3>📋 سجل المزامنة:</h3>
            
            <?php foreach ($errors as $error): ?>
                <div class="log-entry error">❌ <?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
            
            <?php foreach ($results as $result): ?>
                <div class="log-entry <?php echo (strpos($result, 'تم') !== false) ? 'success' : ''; ?>">
                    <?php echo htmlspecialchars($result); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="run_sync.php">🔄 المزامنة العادية</a>
            <a href="test_sync.php">🧪 اختبار المزامنة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
