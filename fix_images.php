<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$results = [];

// دالة لفحص صحة رابط الصورة
function checkImageUrl($url) {
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    return ($httpCode >= 200 && $httpCode < 300 && strpos($contentType, 'image/') !== false);
}

// دالة لإنشاء صورة افتراضية مخصصة
function generateDefaultImage($title, $amount = 0) {
    $encodedTitle = urlencode(substr($title, 0, 30));
    $color = dechex(rand(0x000000, 0xFFFFFF));
    $bgColor = dechex(rand(0x000000, 0x999999));
    
    $text = $encodedTitle;
    if ($amount > 0) {
        $text .= "+$" . number_format($amount, 2);
    }
    
    return "https://via.placeholder.com/300x200/{$bgColor}/{$color}?text=" . $text;
}

// إصلاح الصور المكسورة
if (isset($_POST['fix_images'])) {
    try {
        $conn = getDBConnection();
        
        // جلب جميع العروض
        $offers = $conn->query("SELECT id, title, image_url, amount FROM offers WHERE is_active = 1");
        $total = $offers->num_rows;
        $fixed = 0;
        $broken = 0;
        
        $results[] = "بدء فحص $total عرض...";
        
        while ($offer = $offers->fetch_assoc()) {
            $currentImage = $offer['image_url'];
            $title = $offer['title'];
            $amount = $offer['amount'];
            
            // فحص الصورة الحالية
            if (!checkImageUrl($currentImage)) {
                $broken++;
                
                // إنشاء صورة افتراضية جديدة
                $newImage = generateDefaultImage($title, $amount);
                
                // تحديث قاعدة البيانات
                $updateStmt = $conn->prepare("UPDATE offers SET image_url = ? WHERE id = ?");
                $updateStmt->bind_param("si", $newImage, $offer['id']);
                
                if ($updateStmt->execute()) {
                    $fixed++;
                    $results[] = "✅ تم إصلاح صورة العرض: " . substr($title, 0, 50);
                } else {
                    $results[] = "❌ فشل في إصلاح صورة العرض: " . substr($title, 0, 50);
                }
                
                $updateStmt->close();
            }
        }
        
        $conn->close();
        
        $results[] = "=== النتائج النهائية ===";
        $results[] = "إجمالي العروض: $total";
        $results[] = "الصور المكسورة: $broken";
        $results[] = "الصور المصلحة: $fixed";
        
        $message = "✅ تم إصلاح $fixed صورة من أصل $broken صورة مكسورة!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إصلاح الصور: " . $e->getMessage();
    }
}

// تحديث جميع الصور إلى صور افتراضية جديدة
if (isset($_POST['update_all_images'])) {
    try {
        $conn = getDBConnection();
        
        $offers = $conn->query("SELECT id, title, amount FROM offers WHERE is_active = 1");
        $total = $offers->num_rows;
        $updated = 0;
        
        $results[] = "بدء تحديث $total صورة...";
        
        while ($offer = $offers->fetch_assoc()) {
            $title = $offer['title'];
            $amount = $offer['amount'];
            
            // إنشاء صورة افتراضية جديدة
            $newImage = generateDefaultImage($title, $amount);
            
            // تحديث قاعدة البيانات
            $updateStmt = $conn->prepare("UPDATE offers SET image_url = ? WHERE id = ?");
            $updateStmt->bind_param("si", $newImage, $offer['id']);
            
            if ($updateStmt->execute()) {
                $updated++;
                $results[] = "✅ تم تحديث صورة العرض: " . substr($title, 0, 50);
            } else {
                $results[] = "❌ فشل في تحديث صورة العرض: " . substr($title, 0, 50);
            }
            
            $updateStmt->close();
        }
        
        $conn->close();
        
        $results[] = "=== النتائج النهائية ===";
        $results[] = "إجمالي العروض: $total";
        $results[] = "الصور المحدثة: $updated";
        
        $message = "✅ تم تحديث $updated صورة بنجاح!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الصور: " . $e->getMessage();
    }
}

// فحص حالة الصور
$image_stats = [];
try {
    $conn = getDBConnection();
    
    // إحصائيات عامة
    $total_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
    $placeholder_images = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1 AND image_url LIKE '%placeholder%'")->fetch_assoc()['count'];
    
    $image_stats = [
        'total' => $total_offers,
        'placeholder' => $placeholder_images,
        'custom' => $total_offers - $placeholder_images
    ];
    
    $conn->close();
} catch (Exception $e) {
    $image_stats['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح صور العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .results {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .result-item {
            background: white;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #667eea;
            font-size: 0.9rem;
        }
        
        .result-item.success {
            border-left-color: #28a745;
        }
        
        .result-item.error {
            border-left-color: #dc3545;
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ إصلاح صور العروض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 حول إصلاح الصور:</h3>
            <ul style="margin-right: 1.5rem;">
                <li><strong>فحص الصور المكسورة:</strong> يتحقق من صحة روابط الصور الحالية</li>
                <li><strong>إصلاح تلقائي:</strong> يستبدل الصور المكسورة بصور افتراضية مخصصة</li>
                <li><strong>صور مخصصة:</strong> تحتوي على عنوان العرض والمبلغ</li>
                <li><strong>ألوان عشوائية:</strong> كل صورة لها ألوان مختلفة</li>
            </ul>
        </div>
        
        <?php if (!isset($image_stats['error'])): ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $image_stats['total']; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $image_stats['placeholder']; ?></div>
                <div class="stat-label">صور افتراضية</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $image_stats['custom']; ?></div>
                <div class="stat-label">صور مخصصة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number">
                    <?php 
                    if ($image_stats['total'] > 0) {
                        echo round(($image_stats['placeholder'] / $image_stats['total']) * 100, 1);
                    } else {
                        echo '0';
                    }
                    ?>%
                </div>
                <div class="stat-label">نسبة الصور الافتراضية</div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="action-section">
            <h3>🛠️ أدوات إصلاح الصور</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="fix_images" class="btn btn-success">
                    🔧 إصلاح الصور المكسورة
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="update_all_images" class="btn btn-warning" 
                        onclick="return confirm('هل أنت متأكد من تحديث جميع الصور؟ هذا سيستبدل جميع الصور الحالية.')">
                    🎨 تحديث جميع الصور
                </button>
            </form>
        </div>
        
        <?php if (!empty($results)): ?>
        <div class="results">
            <h3>📋 نتائج العملية:</h3>
            
            <?php foreach ($results as $result): ?>
                <div class="result-item <?php echo (strpos($result, '✅') !== false) ? 'success' : ((strpos($result, '❌') !== false) ? 'error' : ''); ?>">
                    <?php echo htmlspecialchars($result); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="enhanced_sync.php">✨ المزامنة المحسنة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
