<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// معالجة الإجراءات المتعددة
if (isset($_POST['bulk_action']) && isset($_POST['selected_offers'])) {
    $action = $_POST['bulk_action'];
    $selected_offers = $_POST['selected_offers'];
    
    if (empty($selected_offers)) {
        $message = "❌ يرجى اختيار عرض واحد على الأقل!";
    } else {
        try {
            $conn = getDBConnection();
            $count = 0;
            
            foreach ($selected_offers as $offer_id) {
                $offer_id = intval($offer_id);
                
                switch ($action) {
                    case 'activate':
                        $stmt = $conn->prepare("UPDATE offers SET is_active = 1 WHERE id = ?");
                        $stmt->bind_param("i", $offer_id);
                        if ($stmt->execute()) $count++;
                        $stmt->close();
                        break;
                        
                    case 'deactivate':
                        $stmt = $conn->prepare("UPDATE offers SET is_active = 0 WHERE id = ?");
                        $stmt->bind_param("i", $offer_id);
                        if ($stmt->execute()) $count++;
                        $stmt->close();
                        break;
                        
                    case 'delete':
                        $stmt = $conn->prepare("DELETE FROM offers WHERE id = ?");
                        $stmt->bind_param("i", $offer_id);
                        if ($stmt->execute()) $count++;
                        $stmt->close();
                        break;
                }
            }
            
            $conn->close();
            
            $action_text = [
                'activate' => 'تفعيل',
                'deactivate' => 'إلغاء تفعيل', 
                'delete' => 'حذف'
            ];
            
            $message = "✅ تم {$action_text[$action]} $count عرض بنجاح!";
            $success = true;
            
        } catch (Exception $e) {
            $message = "❌ خطأ في تنفيذ العملية: " . $e->getMessage();
        }
    }
}

// جلب العروض مع الفلترة
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];
$types = "";

if ($filter === 'active') {
    $where_conditions[] = "is_active = 1";
} elseif ($filter === 'inactive') {
    $where_conditions[] = "is_active = 0";
} elseif ($filter === 'manual') {
    $where_conditions[] = "(external_id IS NULL OR external_id = '')";
} elseif ($filter === 'api') {
    $where_conditions[] = "(external_id IS NOT NULL AND external_id != '')";
}

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR offer_url LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ss";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $conn = getDBConnection();
    
    // عدد العروض الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM offers $where_clause";
    if (!empty($params)) {
        $count_stmt = $conn->prepare($count_query);
        if (!empty($types)) {
            $count_stmt->bind_param($types, ...$params);
        }
        $count_stmt->execute();
        $total_offers = $count_stmt->get_result()->fetch_assoc()['total'];
        $count_stmt->close();
    } else {
        $total_offers = $conn->query($count_query)->fetch_assoc()['total'];
    }
    
    $total_pages = ceil($total_offers / $per_page);
    
    // جلب العروض
    $query = "SELECT id, title, image_url, offer_url, amount, countries, device, is_active, created_at, external_id 
              FROM offers $where_clause 
              ORDER BY created_at DESC 
              LIMIT $per_page OFFSET $offset";
    
    if (!empty($params)) {
        $stmt = $conn->prepare($query);
        if (!empty($types)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $offers = $stmt->get_result();
        $stmt->close();
    } else {
        $offers = $conn->query($query);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $message = "❌ خطأ في جلب العروض: " . $e->getMessage();
    $offers = null;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإدارة المتعددة للعروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #333;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .bulk-actions {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .bulk-actions h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .bulk-controls {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-sm {
            padding: 0.3rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .offers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .offer-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .offer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .offer-card.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .offer-checkbox {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            z-index: 10;
        }
        
        .offer-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            position: relative;
        }
        
        .offer-content {
            padding: 1rem;
            position: relative;
        }
        
        .offer-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1rem;
            line-height: 1.3;
            height: 2.6rem;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        
        .offer-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .offer-amount {
            background: #28a745;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.8rem;
        }
        
        .offer-device {
            background: #6c757d;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
        }
        
        .offer-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .type-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .type-manual {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .type-api {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .offer-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .select-all-section {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            text-align: center;
            border-left: 4px solid #ffc107;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .offers-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                justify-content: space-between;
            }
            
            .bulk-controls {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☑️ الإدارة المتعددة للعروض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="controls">
            <div class="filter-group">
                <label>الفلتر:</label>
                <select onchange="updateFilter()" id="filter">
                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع العروض</option>
                    <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>النشطة</option>
                    <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>غير النشطة</option>
                    <option value="manual" <?php echo $filter === 'manual' ? 'selected' : ''; ?>>اليدوية</option>
                    <option value="api" <?php echo $filter === 'api' ? 'selected' : ''; ?>>من API</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>البحث:</label>
                <input type="text" id="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="ابحث في العنوان أو الرابط..." onkeypress="handleSearch(event)">
                <button onclick="performSearch()" class="btn btn-sm">🔍</button>
            </div>
            
            <div style="margin-right: auto;">
                <a href="add_manual_offer.php" class="btn btn-success">➕ إضافة عرض جديد</a>
                <a href="manage_offers.php" class="btn">📋 العرض التقليدي</a>
            </div>
        </div>
        
        <form method="POST" id="bulkForm">
            <div class="bulk-actions">
                <h3>🎯 الإجراءات المتعددة</h3>
                <div class="bulk-controls">
                    <div class="select-all-section">
                        <label>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            تحديد/إلغاء تحديد الكل (<span id="selectedCount">0</span> محدد)
                        </label>
                    </div>
                    
                    <select name="bulk_action" required>
                        <option value="">اختر الإجراء...</option>
                        <option value="activate">✅ تفعيل المحدد</option>
                        <option value="deactivate">⏸️ إلغاء تفعيل المحدد</option>
                        <option value="delete">🗑️ حذف المحدد</option>
                    </select>
                    
                    <button type="submit" class="btn btn-warning" onclick="return confirmBulkAction()">
                        🚀 تنفيذ الإجراء
                    </button>
                </div>
            </div>
            
            <?php if ($offers && $offers->num_rows > 0): ?>
            <div class="offers-grid">
                <?php while ($offer = $offers->fetch_assoc()): ?>
                <div class="offer-card" onclick="toggleOfferSelection(<?php echo $offer['id']; ?>)">
                    <input type="checkbox" name="selected_offers[]" value="<?php echo $offer['id']; ?>" 
                           class="offer-checkbox" id="offer_<?php echo $offer['id']; ?>" 
                           onchange="updateSelectedCount()">
                    
                    <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                         alt="صورة العرض" class="offer-image" 
                         onerror="this.src='https://via.placeholder.com/300x150/667eea/ffffff?text=صورة'">
                    
                    <div class="offer-content">
                        <div class="offer-title" title="<?php echo htmlspecialchars($offer['title']); ?>">
                            <?php echo htmlspecialchars($offer['title']); ?>
                        </div>
                        
                        <div class="offer-details">
                            <?php if ($offer['amount'] > 0): ?>
                                <span class="offer-amount">$<?php echo number_format($offer['amount'], 2); ?></span>
                            <?php else: ?>
                                <span class="offer-amount" style="background: #6c757d;">غير محدد</span>
                            <?php endif; ?>
                            
                            <span class="offer-device"><?php echo htmlspecialchars($offer['device']); ?></span>
                        </div>
                        
                        <div class="offer-status">
                            <span class="status-badge <?php echo $offer['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                <?php echo $offer['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                            
                            <span class="type-badge <?php echo empty($offer['external_id']) ? 'type-manual' : 'type-api'; ?>">
                                <?php echo empty($offer['external_id']) ? 'يدوي' : 'API'; ?>
                            </span>
                        </div>
                        
                        <div class="offer-actions">
                            <a href="edit_offer.php?id=<?php echo $offer['id']; ?>" class="btn btn-sm" 
                               onclick="event.stopPropagation()" title="تحرير العرض">✏️ تحرير</a>
                            
                            <a href="<?php echo htmlspecialchars($offer['offer_url']); ?>" 
                               target="_blank" class="btn btn-sm" 
                               onclick="event.stopPropagation()" title="فتح الرابط">🔗 فتح</a>
                        </div>
                        
                        <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #666; text-align: center;">
                            تاريخ الإنشاء: <?php echo date('Y-m-d', strtotime($offer['created_at'])); ?>
                        </div>
                    </div>
                </div>
                <?php endwhile; ?>
            </div>
            
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">السابق</a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <?php if ($i == $page): ?>
                        <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                        <a href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">التالي</a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php else: ?>
            <div style="text-align: center; padding: 3rem; color: #666;">
                <h3>لا توجد عروض</h3>
                <p>لم يتم العثور على عروض تطابق المعايير المحددة.</p>
                <a href="add_manual_offer.php" class="btn btn-success" style="margin-top: 1rem;">➕ إضافة أول عرض</a>
            </div>
            <?php endif; ?>
        </form>
        
        <div class="nav-links">
            <a href="add_manual_offer.php">➕ إضافة عرض جديد</a>
            <a href="manage_offers.php">📋 العرض التقليدي</a>
            <a href="comprehensive_sync.php">🌟 المزامنة الشاملة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
    
    <script>
        function updateFilter() {
            const filter = document.getElementById('filter').value;
            const search = document.getElementById('search').value;
            window.location.href = `?filter=${filter}&search=${encodeURIComponent(search)}`;
        }
        
        function performSearch() {
            const filter = document.getElementById('filter').value;
            const search = document.getElementById('search').value;
            window.location.href = `?filter=${filter}&search=${encodeURIComponent(search)}`;
        }
        
        function handleSearch(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }
        
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('input[name="selected_offers[]"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const card = checkbox.closest('.offer-card');
                if (selectAll.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
            
            updateSelectedCount();
        }
        
        function toggleOfferSelection(offerId) {
            const checkbox = document.getElementById('offer_' + offerId);
            const card = checkbox.closest('.offer-card');
            
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                card.classList.add('selected');
            } else {
                card.classList.remove('selected');
            }
            
            updateSelectedCount();
        }
        
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('input[name="selected_offers[]"]:checked');
            const count = checkboxes.length;
            document.getElementById('selectedCount').textContent = count;
            
            // تحديث حالة "تحديد الكل"
            const selectAll = document.getElementById('selectAll');
            const totalCheckboxes = document.querySelectorAll('input[name="selected_offers[]"]');
            
            if (count === 0) {
                selectAll.indeterminate = false;
                selectAll.checked = false;
            } else if (count === totalCheckboxes.length) {
                selectAll.indeterminate = false;
                selectAll.checked = true;
            } else {
                selectAll.indeterminate = true;
                selectAll.checked = false;
            }
        }
        
        function confirmBulkAction() {
            const checkboxes = document.querySelectorAll('input[name="selected_offers[]"]:checked');
            const action = document.querySelector('select[name="bulk_action"]').value;
            
            if (checkboxes.length === 0) {
                alert('يرجى اختيار عرض واحد على الأقل!');
                return false;
            }
            
            if (!action) {
                alert('يرجى اختيار الإجراء المطلوب!');
                return false;
            }
            
            const actionText = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'delete': 'حذف'
            };
            
            const confirmText = action === 'delete' 
                ? `هل أنت متأكد من حذف ${checkboxes.length} عرض؟ هذا الإجراء لا يمكن التراجع عنه!`
                : `هل أنت متأكد من ${actionText[action]} ${checkboxes.length} عرض؟`;
            
            return confirm(confirmText);
        }
        
        // تحديث العداد عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();
        });
    </script>
</body>
</html>
