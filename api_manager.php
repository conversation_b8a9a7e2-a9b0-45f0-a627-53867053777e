<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة جلب العروض من CPALead
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fetch_offers'])) {
    $api_id = trim($_POST['api_id']);
    
    if ($api_id) {
        $apiUrl = "https://www.cpalead.com/api/offers?id=" . urlencode($api_id);
        
        try {
            // استخدام cURL بدلاً من file_get_contents لدعم إعادة التوجيه
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // تتبع إعادة التوجيه
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // حد أقصى 5 إعادة توجيه
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPA-Site/1.0)');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: application/json',
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new Exception("خطأ في الاتصال: $error");
            }

            if ($httpCode !== 200) {
                throw new Exception("رمز HTTP غير متوقع: $httpCode");
            }

            if ($response !== false) {
                // عرض البيانات الخام للتشخيص
                $rawData = trim($response);

                // محاولة فك تشفير JSON
                $data = json_decode($rawData, true);
                $jsonError = json_last_error();

                // إذا فشل JSON، قد تكون البيانات في تنسيق مختلف
                if ($jsonError !== JSON_ERROR_NONE) {
                    $message = '<div class="alert alert-error">❌ خطأ في تحليل JSON: ' . json_last_error_msg() . '<br><strong>البيانات المُستقبلة:</strong><br><pre>' . htmlspecialchars(substr($rawData, 0, 500)) . '</pre></div>';
                } elseif ($data && isset($data['status']) && $data['status'] === 'success') {
                    $imported = 0;
                    $errors = [];
                    $debugInfo = [];

                    // التحقق من وجود العروض في الاستجابة
                    if (!isset($data['offers']) || !is_array($data['offers'])) {
                        $message = '<div class="alert alert-error">❌ لا توجد عروض في الاستجابة من API!</div>';
                    } else {
                        $offers = $data['offers'];
                        $debugInfo[] = "عدد العروض المُستقبلة: " . count($offers);
                        $debugInfo[] = "الدولة: " . ($data['country'] ?? 'غير محدد');
                        $debugInfo[] = "إجمالي العروض: " . ($data['number_offers'] ?? count($offers));

                        // عرض هيكل البيانات للتشخيص
                        if (!empty($offers)) {
                            $firstOffer = reset($offers);
                            $debugInfo[] = "هيكل العرض: " . implode(', ', array_keys($firstOffer));
                        }

                        foreach ($offers as $index => $offer) {
                            $debugInfo[] = "معالجة العرض رقم " . ($index + 1);

                            // استخراج البيانات حسب هيكل CPALead API
                            $title = isset($offer['title']) ? trim($offer['title']) : '';
                            $offer_url = isset($offer['preview_link']) ? trim($offer['preview_link']) : '';
                            $tracking_link = isset($offer['link']) ? trim($offer['link']) : '';
                            $image_url = '';

                            // استخراج صورة من creatives
                            if (isset($offer['creatives']['url'])) {
                                $image_url = trim($offer['creatives']['url']);
                            }

                            // استخدام صورة افتراضية إذا لم تكن متوفرة
                            if (empty($image_url)) {
                                $image_url = 'https://via.placeholder.com/300x200/4CAF50/white?text=' . urlencode($title ?: 'عرض');
                            }

                            // معلومات إضافية من API
                            $offer_id_api = isset($offer['id']) ? $offer['id'] : '';
                            $description = isset($offer['description']) ? trim($offer['description']) : '';
                            $conversion = isset($offer['conversion']) ? trim($offer['conversion']) : '';
                            $amount = isset($offer['amount']) ? $offer['amount'] : 0;
                            $device = isset($offer['device']) ? $offer['device'] : '';
                            $payout_type = isset($offer['payout_type']) ? $offer['payout_type'] : '';

                            // استخراج البلدان
                            $countries = '';
                            if (isset($offer['countries']) && is_array($offer['countries'])) {
                                $countries = implode(',', $offer['countries']);
                            }

                            // إنشاء عنوان محسن
                            $enhanced_title = $title;
                            if ($amount > 0) {
                                $enhanced_title .= " - $" . $amount . " " . $payout_type;
                            }
                            if ($device) {
                                $enhanced_title .= " (" . ucfirst($device) . ")";
                            }
                            if ($countries) {
                                $countryList = explode(',', $countries);
                                if (count($countryList) <= 3) {
                                    $enhanced_title .= " [" . implode(',', $countryList) . "]";
                                } else {
                                    $enhanced_title .= " [" . count($countryList) . " بلد]";
                                }
                            }

                            // استخدام رابط التتبع إذا كان متوفراً، وإلا استخدم رابط المعاينة
                            $final_url = !empty($tracking_link) ? $tracking_link : $offer_url;

                            $debugInfo[] = "العنوان: " . ($title ?: 'غير موجود') . " | الرابط: " . ($final_url ?: 'غير موجود') . " | المبلغ: $" . $amount;

                            if (!empty($title) && !empty($final_url)) {
                                // التحقق من عدم وجود العرض مسبقاً (بناءً على العنوان أو معرف API)
                                $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ? OR offer_url = ?");
                                $checkStmt->bind_param("ss", $enhanced_title, $final_url);
                                $checkStmt->execute();
                                $existingOffer = $checkStmt->get_result();
                                $checkStmt->close();

                                if ($existingOffer->num_rows == 0) {
                                    // استخراج الوصف من البيانات
                                    $description = extractDescription($offer, $enhanced_title);

                                    // إضافة العرض الجديد باستخدام الدالة الآمنة
                                    $insert_id = safeInsertOffer($enhanced_title, $description, $image_url, $final_url, $countries, $device, $amount, $payout_type);

                                    if ($insert_id) {
                                        $imported++;
                                        $debugInfo[] = "✅ تم إضافة: " . $enhanced_title;
                                    } else {
                                        $errors[] = "فشل في إضافة العرض: " . htmlspecialchars($enhanced_title);
                                        $debugInfo[] = "❌ فشل إضافة: " . $enhanced_title;
                                    }
                                } else {
                                    $debugInfo[] = "⚠️ موجود مسبقاً: " . $enhanced_title;
                                }
                            } else {
                                $errors[] = "بيانات غير مكتملة في العرض رقم " . ($index + 1) . " - العنوان: " . ($title ?: 'مفقود') . " | الرابط: " . ($final_url ?: 'مفقود');
                                $debugInfo[] = "❌ بيانات ناقصة في العرض رقم " . ($index + 1);
                            }
                        }

                        // إنشاء رسالة النتيجة
                        if ($imported > 0) {
                            $message = '<div class="alert alert-success">✅ تم استيراد ' . $imported . ' عرض بنجاح من CPALead!</div>';
                        } else {
                            $message = '<div class="alert alert-warning">⚠️ لم يتم استيراد أي عروض جديدة. قد تكون موجودة مسبقاً.</div>';
                        }

                        // إضافة معلومات التشخيص
                        $message .= '<div class="alert alert-info"><strong>معلومات التشخيص:</strong><br>' . implode('<br>', $debugInfo) . '</div>';

                        if (!empty($errors)) {
                            $message .= '<div class="alert alert-error"><strong>الأخطاء:</strong><br>' . implode('<br>', $errors) . '</div>';
                        }
                    }
                } else {
                    $message = '<div class="alert alert-error">❌ البيانات المُستقبلة ليست مصفوفة صحيحة!<br><strong>نوع البيانات:</strong> ' . gettype($data) . '<br><strong>المحتوى:</strong><br><pre>' . htmlspecialchars(substr($rawData, 0, 500)) . '</pre></div>';
                }
            } else {
                $message = '<div class="alert alert-error">❌ فشل في الاتصال بـ CPALead API! تحقق من معرف API أو الاتصال بالإنترنت.</div>';
            }
        } catch (Exception $e) {
            $message = '<div class="alert alert-error">❌ خطأ: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال معرف API!</div>';
    }
}

// اختبار API
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_api'])) {
    $test_id = trim($_POST['test_id']);

    if ($test_id) {
        $testUrl = "https://www.cpalead.com/api/offers?id=" . urlencode($test_id);

        // استخدام cURL للاختبار أيضاً
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $testUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPA-Site/1.0)');

        $testResponse = curl_exec($ch);
        $testHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $testError = curl_error($ch);
        curl_close($ch);

        if ($testError) {
            $message = '<div class="alert alert-error">❌ خطأ في الاتصال للاختبار: ' . htmlspecialchars($testError) . '</div>';
        } elseif ($testHttpCode !== 200) {
            $message = '<div class="alert alert-error">❌ رمز HTTP غير متوقع للاختبار: ' . $testHttpCode . '</div>';
        }

        if ($testResponse !== false) {
            $rawTestData = trim($testResponse);
            $testData = json_decode($rawTestData, true);
            $jsonError = json_last_error();

            if ($jsonError !== JSON_ERROR_NONE) {
                $message = '<div class="alert alert-error">❌ خطأ في تحليل JSON: ' . json_last_error_msg() . '<br><strong>البيانات الخام:</strong><br><pre>' . htmlspecialchars(substr($rawTestData, 0, 1000)) . '</pre></div>';
            } elseif ($testData && is_array($testData)) {
                $sampleOffers = array_slice($testData, 0, 3);
                $offerDetails = [];

                foreach ($sampleOffers as $index => $offer) {
                    $offerDetails[] = "العرض " . ($index + 1) . ": " . json_encode($offer, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                }

                $message = '<div class="alert alert-success">✅ API يعمل بشكل صحيح! تم العثور على ' . count($testData) . ' عرض.<br><strong>رابط API:</strong> ' . htmlspecialchars($testUrl) . '<br><strong>معاينة البيانات:</strong><br><pre>' . implode("\n\n", $offerDetails) . '</pre></div>';
            } else {
                $message = '<div class="alert alert-error">❌ البيانات المُستقبلة ليست مصفوفة صحيحة!<br><strong>نوع البيانات:</strong> ' . gettype($testData) . '<br><strong>المحتوى:</strong><br><pre>' . htmlspecialchars(substr($rawTestData, 0, 1000)) . '</pre></div>';
            }
        } else {
            $message = '<div class="alert alert-error">❌ فشل في الاتصال بـ API!<br><strong>رابط API:</strong> ' . htmlspecialchars($testUrl) . '<br>تحقق من:<br>- صحة معرف API<br>- الاتصال بالإنترنت<br>- إعدادات الخادم</div>';
        }
    }
}

// إحصائيات العروض الحالية
$totalOffers = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
$recentOffers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)")->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة API - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }
        
        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
            margin-right: 0.5rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .info-box h4 {
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1rem;
        }
        
        pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔌 إدارة API</h1>
            <p>استيراد العروض من CPALead وإدارة الاتصالات</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 إجمالي العروض</h3>
                <div class="stat-number"><?php echo number_format($totalOffers); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>🆕 عروض اليوم</h3>
                <div class="stat-number"><?php echo number_format($recentOffers); ?></div>
            </div>
        </div>
        
        <div class="info-box">
            <h4>📋 معلومات CPALead API:</h4>
            <ul>
                <li><strong>معرف API:</strong> يمكنك الحصول عليه من لوحة تحكم CPALead</li>
                <li><strong>رابط API:</strong> https://www.cpalead.com/api/offers?id=YOUR_ID</li>
                <li><strong>هيكل البيانات:</strong> يتم استيراد العروض من حقل "offers" في الاستجابة</li>
                <li><strong>البيانات المستخرجة:</strong> العنوان، رابط المعاينة، رابط التتبع، المبلغ، نوع الجهاز</li>
                <li><strong>التحسينات:</strong> يتم إضافة المبلغ ونوع الجهاز والبلدان للعنوان تلقائياً</li>
                <li><strong>فلترة البلدان:</strong> العروض تظهر حسب بلد المستخدم تلقائياً</li>
                <li><strong>منع التكرار:</strong> لا يتم استيراد العروض الموجودة مسبقاً</li>
                <li><strong>اختبار متقدم:</strong> <a href="test_api.php" target="_blank" style="color: #007bff;">🧪 اختبار API المتقدم</a></li>
                <li><strong>إحصائيات البلدان:</strong> <a href="countries_stats.php" target="_blank" style="color: #28a745;">📊 عرض إحصائيات البلدان</a></li>
                <li><strong>ربط CPALead:</strong> <a href="cpalead_integration.php" target="_blank" style="color: #ffc107;">🔗 ربط جميع العروض بـ CPALead</a></li>
                <li><strong>اختبار Postback:</strong> <a href="test_postback.php" target="_blank" style="color: #17a2b8;">🧪 اختبار Postback</a></li>
                <li><strong>المزامنة التلقائية:</strong> <a href="sync_manager.php" target="_blank" style="color: #6f42c1;">🔄 إدارة المزامنة التلقائية</a></li>
                <li><strong>تشغيل المزامنة:</strong> <a href="run_sync.php" target="_blank" style="color: #28a745;">🚀 تشغيل المزامنة العادية</a></li>
                <li><strong>المزامنة المحسنة:</strong> <a href="enhanced_sync.php" target="_blank" style="color: #20c997;">✨ المزامنة المحسنة للعروض النشطة</a></li>
                <li><strong>المزامنة الشاملة:</strong> <a href="comprehensive_sync.php" target="_blank" style="color: #ff6b6b;">🌟 المزامنة الشاملة لجميع الأجهزة</a></li>
                <li><strong>إضافة عرض يدوي:</strong> <a href="add_manual_offer.php" target="_blank" style="color: #28a745;">➕ إضافة عرض يدوي للصفحة الرئيسية</a></li>
                <li><strong>إدارة العروض:</strong> <a href="manage_offers.php" target="_blank" style="color: #17a2b8;">📋 إدارة وتحرير العروض الموجودة</a></li>
                <li><strong>إصلاح صور العروض:</strong> <a href="fix_images.php" target="_blank" style="color: #6f42c1;">🖼️ إصلاح وتحديث صور العروض</a></li>
                <li><strong>اختبار المزامنة:</strong> <a href="test_sync.php" target="_blank" style="color: #17a2b8;">🧪 اختبار متطلبات المزامنة</a></li>
                <li><strong>إصلاح المزامنة:</strong> <a href="fix_run_sync.php" target="_blank" style="color: #dc3545;">🔧 إصلاح مشاكل المزامنة</a></li>
                <li><strong>إصلاح المزامنة:</strong> <a href="fix_sync_issues.php" target="_blank" style="color: #dc3545;">🔧 إصلاح مشاكل المزامنة</a></li>
                <li><strong>اختبار الاتصال:</strong> <a href="test_cpalead_connection.php" target="_blank" style="color: #17a2b8;">🌐 اختبار الاتصال بـ CPALead</a></li>
                <li><strong>إصلاح مشاكل CPALead:</strong> <a href="fix_cpalead_issues.php" target="_blank" style="color: #e91e63;">🔧 إصلاح مشاكل CPALead</a></li>
                <li><strong>إدارة العروض:</strong> <a href="offers_manager.php" target="_blank" style="color: #20c997;">📋 إدارة العروض النشطة والمتوقفة</a></li>
                <li><strong>مولد Postback CPALead:</strong> <a href="cpalead_postback_generator.php" target="_blank" style="color: #e83e8c;">🔗 مولد Postback متقدم</a></li>
                <li><strong>معالج Postback:</strong> <a href="postback_wizard.php" target="_blank" style="color: #6f42c1;">🧙‍♂️ معالج إعداد تفاعلي</a></li>
                <li><strong>محاكي CPALead:</strong> <a href="cpalead_simulator.php" target="_blank" style="color: #4facfe;">🎭 محاكاة Postback</a></li>
                <li><strong>عروض تجريبية:</strong> <a href="add_test_offers.php" target="_blank" style="color: #28a745;">🧪 إضافة عروض للاختبار</a></li>
                <li><strong>إدارة العروض المتقدمة:</strong> <a href="offers_bulk_manager.php" target="_blank" style="color: #6f42c1;">📋 تحديد وحذف جماعي</a></li>
                <li><strong>لوحة الإشعارات:</strong> <a href="notifications_panel.php" target="_blank" style="color: #6f42c1;">🔔 متابعة الأنشطة</a></li>
                <li><strong>تشخيص Postback:</strong> <a href="postback_debug.php" target="_blank" style="color: #ff6b6b;">🔍 تشخيص وحل المشاكل</a></li>
                <li><strong>إضافة حقل الوصف:</strong> <a href="add_description_field.php" target="_blank" style="color: #28a745;">📝 إضافة وصف العروض</a></li>
                <li><strong>إصلاح حقل الوصف:</strong> <a href="fix_description_field.php" target="_blank" style="color: #dc3545;">🔧 إصلاح سريع للوصف</a></li>
                <li><strong>تشخيص قاعدة البيانات:</strong> <a href="database_diagnostic.php" target="_blank" style="color: #6f42c1;">🔍 فحص شامل للمشاكل</a></li>
                <li><strong>اختبار النظام:</strong> <a href="test_system.php" target="_blank" style="color: #17a2b8;">🧪 اختبار سريع للنظام</a></li>
                <li><strong>إصلاح شامل:</strong> <a href="fix_all_issues.php" target="_blank" style="color: #e91e63;">🛠️ إصلاح جميع المشاكل</a></li>
                <li><strong>إصلاح أوقات الإشعارات:</strong> <a href="fix_notification_times.php" target="_blank" style="color: #ff9800;">🕐 إصلاح أوقات الإشعارات</a></li>
                <li><strong>اختبار أوقات الإشعارات:</strong> <a href="test_notification_times.php" target="_blank" style="color: #9c27b0;">🧪 اختبار تنسيق الأوقات</a></li>
                <li><strong>إصلاح نظام الإشعارات:</strong> <a href="fix_notifications_system.php" target="_blank" style="color: #ff5722;">🔔 إصلاح نظام الإشعارات</a></li>
                <li><strong>اختبار IP Quality Score:</strong> <a href="test_ip_quality.php" target="_blank" style="color: #795548;">🔍 اختبار جودة IP</a></li>
                <li><strong>إصلاح درجة IP:</strong> <a href="fix_ip_score.php" target="_blank" style="color: #e91e63;">🔧 إصلاح درجة IP (28→72)</a></li>
                <li><strong>اختبار درجة الاحتيال:</strong> <a href="test_fraud_score.php" target="_blank" style="color: #9c27b0;">🧮 اختبار معادلة درجة الاحتيال</a></li>
                <li><strong>اختبار نظام 12 ساعة:</strong> <a href="test_time_format.php" target="_blank" style="color: #17a2b8;">🕐 اختبار تنسيق الوقت</a></li>
                <li><strong>اختبار الإشعارات:</strong> <a href="test_notification_12hour.php" target="_blank" style="color: #6f42c1;">🔔 اختبار نظام الإشعارات</a></li>
                <li><strong>Postback CPALead:</strong> <a href="postback_cpalead.php" target="_blank" style="color: #fd7e14;">📡 معالج Postback</a></li>
                <li><strong>إصلاح قاعدة البيانات:</strong> <a href="fix_database.php" target="_blank" style="color: #dc3545;">🔧 إصلاح مشاكل الترميز</a></li>
            </ul>
        </div>
        
        <div class="form-section">
            <h2>🧪 اختبار API</h2>
            <p style="color: #666; margin-bottom: 1rem;">اختبر الاتصال بـ CPALead API قبل الاستيراد</p>
            <form method="POST">
                <div class="form-group">
                    <label for="test_id">معرف API للاختبار:</label>
                    <input type="text" id="test_id" name="test_id" placeholder="1941213" value="1941213">
                </div>
                
                <button type="submit" name="test_api" class="btn btn-success">🧪 اختبار API</button>
            </form>
        </div>
        
        <div class="form-section">
            <h2>📥 استيراد العروض من CPALead</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="api_id">معرف API الخاص بك:</label>
                    <input type="text" id="api_id" name="api_id" placeholder="1941213" required>
                </div>
                
                <button type="submit" name="fetch_offers" class="btn">📥 استيراد العروض</button>
            </form>
        </div>
    </div>
</body>
</html>

<?php
$conn->close();
?>
