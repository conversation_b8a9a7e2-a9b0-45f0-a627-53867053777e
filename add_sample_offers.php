<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$added_offers = [];

// دالة إدراج العرض الآمنة
function insertSampleOffer($title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type) {
    try {
        $conn = getDBConnection();
        
        // التحقق من وجود الحقول
        $has_description = false;
        $has_external_id = false;
        
        $desc_check = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
        if ($desc_check->num_rows > 0) $has_description = true;
        
        $ext_check = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        if ($ext_check->num_rows > 0) $has_external_id = true;
        
        $external_id = 'manual_sample_' . time() . '_' . rand(1000, 9999);
        
        if ($has_description && $has_external_id) {
            $stmt = $conn->prepare("
                INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?)
            ");
            $stmt->bind_param("ssssssdss", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $external_id);
        } elseif ($has_description) {
            $stmt = $conn->prepare("
                INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
            ");
            $stmt->bind_param("ssssssds", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type);
        } elseif ($has_external_id) {
            $stmt = $conn->prepare("
                INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?)
            ");
            $stmt->bind_param("sssssds", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $external_id);
        } else {
            $stmt = $conn->prepare("
                INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)
            ");
            $stmt->bind_param("sssssds", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type);
        }
        
        $result = $stmt->execute();
        $insert_id = $conn->insert_id;
        $stmt->close();
        $conn->close();
        
        return $result ? $insert_id : false;
    } catch (Exception $e) {
        return false;
    }
}

// إضافة عروض تجريبية
if (isset($_POST['add_sample_offers'])) {
    $sample_offers = [
        [
            'title' => 'اشترك في Netflix واحصل على $10',
            'description' => 'اشترك في Netflix لمدة شهر واحد واحصل على $10 كمكافأة. العرض متاح للمستخدمين الجدد فقط.',
            'image_url' => 'https://via.placeholder.com/400x250/e50914/ffffff?text=Netflix+Offer',
            'offer_url' => 'https://netflix.com/signup',
            'countries' => 'US,CA,UK,AU',
            'device' => 'all',
            'amount' => 10.00,
            'payout_type' => 'CPA'
        ],
        [
            'title' => 'حمل تطبيق Spotify Premium - $5',
            'description' => 'حمل تطبيق Spotify Premium واستمتع بالموسيقى بدون إعلانات. احصل على $5 عند التسجيل.',
            'image_url' => 'https://via.placeholder.com/400x250/1db954/ffffff?text=Spotify+Premium',
            'offer_url' => 'https://spotify.com/premium',
            'countries' => 'US,UK,DE,FR',
            'device' => 'mobile',
            'amount' => 5.00,
            'payout_type' => 'CPI'
        ],
        [
            'title' => 'سجل في Amazon Prime - $15',
            'description' => 'انضم إلى Amazon Prime واحصل على شحن مجاني وفيديوهات حصرية. مكافأة $15 للأعضاء الجدد.',
            'image_url' => 'https://via.placeholder.com/400x250/ff9900/ffffff?text=Amazon+Prime',
            'offer_url' => 'https://amazon.com/prime',
            'countries' => 'US,UK,CA,DE',
            'device' => 'all',
            'amount' => 15.00,
            'payout_type' => 'CPA'
        ],
        [
            'title' => 'العب Candy Crush Saga - $3',
            'description' => 'حمل لعبة Candy Crush Saga وأكمل المستوى 10 لتحصل على $3. متاح على جميع الأجهزة.',
            'image_url' => 'https://via.placeholder.com/400x250/ff6b6b/ffffff?text=Candy+Crush',
            'offer_url' => 'https://king.com/game/candycrushsaga',
            'countries' => 'all',
            'device' => 'mobile',
            'amount' => 3.00,
            'payout_type' => 'CPI'
        ],
        [
            'title' => 'اشترك في Disney+ - $8',
            'description' => 'استمتع بأفلام Disney وMarvel وStar Wars. اشترك الآن واحصل على $8 كمكافأة ترحيب.',
            'image_url' => 'https://via.placeholder.com/400x250/113ccf/ffffff?text=Disney%2B',
            'offer_url' => 'https://disneyplus.com',
            'countries' => 'US,CA,UK,AU',
            'device' => 'all',
            'amount' => 8.00,
            'payout_type' => 'CPA'
        ],
        [
            'title' => 'حمل تطبيق TikTok - $2',
            'description' => 'حمل تطبيق TikTok وأنشئ حساب جديد. احصل على $2 فور التسجيل والتفعيل.',
            'image_url' => 'https://via.placeholder.com/400x250/000000/ffffff?text=TikTok',
            'offer_url' => 'https://tiktok.com/download',
            'countries' => 'US,UK,CA,AU,EG,SA',
            'device' => 'mobile',
            'amount' => 2.00,
            'payout_type' => 'CPI'
        ],
        [
            'title' => 'سجل في Uber Eats - $12',
            'description' => 'اطلب طعامك المفضل مع Uber Eats. سجل حساب جديد واحصل على $12 كرصيد مجاني.',
            'image_url' => 'https://via.placeholder.com/400x250/00d4aa/ffffff?text=Uber+Eats',
            'offer_url' => 'https://ubereats.com/signup',
            'countries' => 'US,UK,CA,AU,AE',
            'device' => 'all',
            'amount' => 12.00,
            'payout_type' => 'CPA'
        ],
        [
            'title' => 'حمل لعبة PUBG Mobile - $4',
            'description' => 'انضم إلى ملايين اللاعبين في PUBG Mobile. حمل اللعبة وأكمل التدريب لتحصل على $4.',
            'image_url' => 'https://via.placeholder.com/400x250/f39c12/ffffff?text=PUBG+Mobile',
            'offer_url' => 'https://pubgmobile.com',
            'countries' => 'all',
            'device' => 'mobile',
            'amount' => 4.00,
            'payout_type' => 'CPI'
        ]
    ];
    
    $added_count = 0;
    $failed_count = 0;
    
    foreach ($sample_offers as $offer) {
        $result = insertSampleOffer(
            $offer['title'],
            $offer['description'],
            $offer['image_url'],
            $offer['offer_url'],
            $offer['countries'],
            $offer['device'],
            $offer['amount'],
            $offer['payout_type']
        );
        
        if ($result) {
            $added_offers[] = $offer['title'];
            $added_count++;
        } else {
            $failed_count++;
        }
    }
    
    if ($failed_count == 0) {
        $message = "✅ تم إضافة $added_count عرض تجريبي بنجاح!";
    } else {
        $message = "⚠️ تم إضافة $added_count عرض، فشل في إضافة $failed_count عرض";
    }
}

// حذف العروض التجريبية
if (isset($_POST['delete_sample_offers'])) {
    try {
        $conn = getDBConnection();
        $result = $conn->query("DELETE FROM offers WHERE external_id LIKE 'manual_sample_%'");
        $deleted_count = $conn->affected_rows;
        $conn->close();
        
        $message = "✅ تم حذف $deleted_count عرض تجريبي!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في حذف العروض التجريبية: " . $e->getMessage();
    }
}

// إحصائيات العروض
$stats = [];
try {
    $conn = getDBConnection();
    $stats['total'] = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
    $stats['sample'] = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id LIKE 'manual_sample_%'")->fetch_assoc()['count'];
    $stats['active'] = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
    $conn->close();
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عروض تجريبية - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .offers-preview {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .offers-preview h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .offer-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid #2196f3;
        }
        
        .offer-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.3rem;
        }
        
        .offer-details {
            color: #666;
            font-size: 0.9rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 إضافة عروض تجريبية</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php 
                if (strpos($message, '✅') !== false) echo 'success';
                elseif (strpos($message, '⚠️') !== false) echo 'warning';
                else echo 'error';
            ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!isset($stats['error'])): ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['sample']; ?></div>
                <div class="stat-label">العروض التجريبية</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active']; ?></div>
                <div class="stat-label">العروض النشطة</div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="offers-preview">
            <h3>📋 العروض التجريبية المتاحة:</h3>
            <div class="offer-item">
                <div class="offer-title">🎬 اشترك في Netflix واحصل على $10</div>
                <div class="offer-details">للمستخدمين الجدد - جميع الأجهزة - US,CA,UK,AU</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🎵 حمل تطبيق Spotify Premium - $5</div>
                <div class="offer-details">الهاتف المحمول - US,UK,DE,FR</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">📦 سجل في Amazon Prime - $15</div>
                <div class="offer-details">شحن مجاني وفيديوهات - جميع الأجهزة</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🍭 العب Candy Crush Saga - $3</div>
                <div class="offer-details">أكمل المستوى 10 - الهاتف المحمول</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🏰 اشترك في Disney+ - $8</div>
                <div class="offer-details">أفلام Disney وMarvel - جميع الأجهزة</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🎤 حمل تطبيق TikTok - $2</div>
                <div class="offer-details">إنشاء حساب جديد - الهاتف المحمول</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🍔 سجل في Uber Eats - $12</div>
                <div class="offer-details">رصيد مجاني - جميع الأجهزة</div>
            </div>
            <div class="offer-item">
                <div class="offer-title">🎮 حمل لعبة PUBG Mobile - $4</div>
                <div class="offer-details">أكمل التدريب - الهاتف المحمول</div>
            </div>
        </div>
        
        <div class="action-section">
            <h3 style="margin-bottom: 1rem; color: #333;">🛠️ إدارة العروض التجريبية</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="add_sample_offers" class="btn btn-success"
                        onclick="return confirm('هل تريد إضافة 8 عروض تجريبية متنوعة؟')">
                    ➕ إضافة العروض التجريبية
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="delete_sample_offers" class="btn btn-danger"
                        onclick="return confirm('هل أنت متأكد من حذف جميع العروض التجريبية؟')">
                    🗑️ حذف العروض التجريبية
                </button>
            </form>
        </div>
        
        <?php if (!empty($added_offers)): ?>
        <div style="background: #d4edda; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;">
            <h3 style="color: #155724; margin-bottom: 1rem;">✅ العروض المضافة:</h3>
            <?php foreach ($added_offers as $offer): ?>
                <div style="color: #155724; margin: 0.3rem 0;">• <?php echo htmlspecialchars($offer); ?></div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="offers_management.php">📋 إدارة العروض</a>
            <a href="test_offers_system.php">🧪 اختبار النظام</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="api_manager.php">📡 إدارة API</a>
        </div>
    </div>
</body>
</html>
