<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$results = [];
$errors = [];
$success = false;

// دالة لكتابة السجل
function logMessage($message) {
    global $results;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message";
    $results[] = $logEntry;
    
    file_put_contents('comprehensive_sync.log', $logEntry . "\n", FILE_APPEND | LOCK_EX);
}

// دالة لجلب العروض من جميع الأجهزة
function fetchAllDeviceOffers() {
    global $errors;
    
    // قراءة معرفات API
    $apiIds = [];
    if (file_exists('api_ids.txt')) {
        $lines = file('api_ids.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line) && !str_starts_with($line, '//') && !str_starts_with($line, '#')) {
                $apiIds[] = $line;
            }
        }
    }
    
    if (empty($apiIds)) {
        $errors[] = "لا توجد معرفات API صالحة";
        return [];
    }
    
    logMessage("تم العثور على " . count($apiIds) . " معرف API");
    
    // أنواع الأجهزة المختلفة
    $devices = ['all', 'mobile', 'desktop', 'tablet', 'android', 'ios', 'iphone', 'ipad'];
    $countries = ['all', 'US', 'GB', 'CA', 'AU', 'DE', 'FR', 'EG', 'SA', 'AE'];
    
    $allOffers = [];
    
    foreach ($apiIds as $apiId) {
        logMessage("معالجة API ID: $apiId");
        
        foreach ($devices as $device) {
            foreach ($countries as $country) {
                logMessage("جلب عروض $device للدولة $country");
                
                // URLs متعددة مع معاملات مختلفة
                $urls = [
                    "https://cpalead.com/api/offers?id=$apiId&device=$device&country=$country&limit=50",
                    "https://cpalead.com/api/offers?id=$apiId&platform=$device&geo=$country&limit=50",
                    "https://cpalead.com/api/offers?id=$apiId&device=$device&limit=50",
                    "https://www.cpalead.com/api/offers?id=$apiId&device=$device&country=$country&limit=50"
                ];
                
                foreach ($urls as $url) {
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
                    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Accept: application/json',
                        'Cache-Control: no-cache'
                    ]);
                    
                    $response = curl_exec($ch);
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($httpCode == 200 && $response) {
                        $data = json_decode($response, true);
                        
                        if ($data && json_last_error() === JSON_ERROR_NONE) {
                            $offers = [];
                            if (isset($data['offers']) && is_array($data['offers'])) {
                                $offers = $data['offers'];
                            } elseif (isset($data['data']) && is_array($data['data'])) {
                                $offers = $data['data'];
                            } elseif (is_array($data)) {
                                $offers = $data;
                            }
                            
                            if (!empty($offers)) {
                                foreach ($offers as &$offer) {
                                    $offer['source_device'] = $device;
                                    $offer['source_country'] = $country;
                                    $offer['api_id'] = $apiId;
                                }
                                
                                $allOffers = array_merge($allOffers, $offers);
                                logMessage("تم جلب " . count($offers) . " عرض من $device/$country");
                                break; // نجح، انتقل للجهاز التالي
                            }
                        }
                    }
                }
                
                // تأخير قصير لتجنب rate limiting
                usleep(100000); // 0.1 ثانية
            }
        }
    }
    
    // إزالة التكرارات بناءً على العنوان والرابط
    $uniqueOffers = [];
    $seen = [];
    
    foreach ($allOffers as $offer) {
        $title = $offer['title'] ?? $offer['name'] ?? '';
        $link = $offer['link'] ?? $offer['url'] ?? '';
        $key = md5($title . $link);
        
        if (!isset($seen[$key]) && !empty($title) && !empty($link)) {
            $seen[$key] = true;
            $uniqueOffers[] = $offer;
        }
    }
    
    logMessage("إجمالي العروض الفريدة: " . count($uniqueOffers));
    return $uniqueOffers;
}

// دالة لإنشاء صورة عالية الجودة
function createHighQualityImage($offer) {
    $title = $offer['title'] ?? $offer['name'] ?? 'CPA Offer';
    $amount = $offer['amount'] ?? $offer['payout'] ?? 0;
    $device = $offer['source_device'] ?? 'all';
    $country = $offer['source_country'] ?? 'all';
    
    // ألوان متدرجة جذابة
    $gradients = [
        ['667eea', '764ba2'], // أزرق-بنفسجي
        ['f093fb', 'f5576c'], // وردي-أحمر
        ['4facfe', '00f2fe'], // أزرق فاتح
        ['43e97b', '38f9d7'], // أخضر-تركوازي
        ['ffecd2', 'fcb69f'], // برتقالي فاتح
        ['a8edea', 'fed6e3'], // تركوازي-وردي
        ['ff9a9e', 'fecfef'], // وردي فاتح
        ['667eea', '764ba2']  // افتراضي
    ];
    
    $gradient = $gradients[array_rand($gradients)];
    $bgColor = $gradient[0];
    $textColor = 'ffffff';
    
    // تنسيق النص
    $displayText = urlencode(substr($title, 0, 25));
    if ($amount > 0) {
        $displayText .= urlencode(" - $" . number_format($amount, 2));
    }
    
    // إضافة معلومات الجهاز
    if ($device !== 'all') {
        $displayText .= urlencode(" (" . strtoupper($device) . ")");
    }
    
    // إنشاء URL الصورة مع تدرج
    $imageUrl = "https://via.placeholder.com/400x250/{$bgColor}/{$textColor}?text=" . $displayText;
    
    // محاولة استخدام خدمة أخرى للصور إذا فشلت الأولى
    $alternativeServices = [
        "https://dummyimage.com/400x250/{$bgColor}/{$textColor}&text=" . $displayText,
        "https://picsum.photos/400/250?random=" . rand(1, 1000),
        "https://source.unsplash.com/400x250/?business,money"
    ];
    
    // اختبار الصورة الأساسية
    $headers = @get_headers($imageUrl, 1);
    if (!$headers || strpos($headers[0], '200') === false) {
        // جرب الخدمات البديلة
        foreach ($alternativeServices as $altUrl) {
            $headers = @get_headers($altUrl, 1);
            if ($headers && strpos($headers[0], '200') !== false) {
                return $altUrl;
            }
        }
    }
    
    return $imageUrl;
}

// دالة لمعالجة العروض الشاملة
function processComprehensiveOffers($offers) {
    global $errors;
    
    if (empty($offers)) {
        logMessage("لا توجد عروض للمعالجة");
        return ['imported' => 0, 'updated' => 0, 'skipped' => 0];
    }
    
    $imported = 0;
    $updated = 0;
    $skipped = 0;
    
    $conn = getDBConnection();
    
    foreach ($offers as $index => $offer) {
        try {
            logMessage("معالجة العرض " . ($index + 1) . " من " . count($offers));
            
            // استخراج البيانات
            $title = $offer['title'] ?? $offer['name'] ?? '';
            $link = $offer['link'] ?? $offer['url'] ?? '';
            $amount = floatval($offer['amount'] ?? $offer['payout'] ?? 0);
            $device = $offer['source_device'] ?? 'all';
            $countries = $offer['source_country'] ?? 'all';
            $offerId = $offer['id'] ?? $offer['offer_id'] ?? '';
            
            if (empty($title) || empty($link)) {
                $skipped++;
                continue;
            }
            
            // تحسين العنوان
            $enhanced_title = $title;
            if ($amount > 0) {
                $enhanced_title .= " - $" . number_format($amount, 2);
            }
            if ($device !== 'all') {
                $enhanced_title .= " (" . strtoupper($device) . ")";
            }
            
            // إنشاء صورة عالية الجودة
            $image_url = createHighQualityImage($offer);
            
            // التحقق من وجود العرض
            $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ? OR offer_url = ?");
            $checkStmt->bind_param("ss", $enhanced_title, $link);
            $checkStmt->execute();
            $existing = $checkStmt->get_result();
            
            if ($existing->num_rows == 0) {
                // إضافة عرض جديد
                $description = extractDescription($offer, $enhanced_title);
                
                $insert_id = safeInsertOffer(
                    $enhanced_title,
                    $description,
                    $image_url,
                    $link,
                    $countries,
                    $device,
                    $amount,
                    'CPI',
                    1,
                    $offerId
                );
                
                if ($insert_id) {
                    $imported++;
                    logMessage("✅ تم إضافة عرض جديد: " . substr($enhanced_title, 0, 50));
                }
            } else {
                // تحديث العرض الموجود
                $existingOffer = $existing->fetch_assoc();
                $updateStmt = $conn->prepare("UPDATE offers SET is_active = 1, image_url = ?, amount = ?, updated_at = NOW() WHERE id = ?");
                $updateStmt->bind_param("sdi", $image_url, $amount, $existingOffer['id']);
                
                if ($updateStmt->execute()) {
                    $updated++;
                    logMessage("🔄 تم تحديث العرض: " . substr($enhanced_title, 0, 50));
                }
                $updateStmt->close();
            }
            
            $checkStmt->close();
            
        } catch (Exception $e) {
            $error_msg = "خطأ في معالجة العرض: " . $e->getMessage();
            $errors[] = $error_msg;
            logMessage("❌ " . $error_msg);
        }
    }
    
    $conn->close();
    
    return [
        'imported' => $imported,
        'updated' => $updated,
        'skipped' => $skipped
    ];
}

// تشغيل المزامنة الشاملة
if (isset($_POST['run_comprehensive_sync'])) {
    logMessage("=== بدء المزامنة الشاملة ===");
    
    try {
        // التأكد من وجود الحقول المطلوبة
        if (!hasDescriptionField()) {
            ensureDescriptionField();
            logMessage("تم إضافة حقل الوصف");
        }
        
        // إضافة حقل external_id إذا لم يكن موجود
        $conn = getDBConnection();
        $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
        if ($result->num_rows == 0) {
            $conn->query("ALTER TABLE offers ADD COLUMN external_id VARCHAR(50) DEFAULT NULL AFTER id");
            logMessage("تم إضافة حقل external_id");
        }
        $conn->close();
        
        // جلب العروض من جميع الأجهزة والدول
        logMessage("بدء جلب العروض من جميع الأجهزة والدول");
        $offers = fetchAllDeviceOffers();
        
        if (empty($offers)) {
            $errors[] = "لم يتم العثور على عروض";
            logMessage("لم يتم جلب أي عروض");
        } else {
            logMessage("تم جلب " . count($offers) . " عرض فريد - بدء المعالجة");
            
            // معالجة العروض
            $stats = processComprehensiveOffers($offers);
            
            $result_msg = "النتائج النهائية: " . $stats['imported'] . " جديد، " . $stats['updated'] . " محدث، " . $stats['skipped'] . " متجاهل";
            logMessage($result_msg);
            
            if ($stats['imported'] > 0 || $stats['updated'] > 0) {
                $success = true;
            }
        }
        
        logMessage("=== انتهاء المزامنة الشاملة ===");
        
    } catch (Exception $e) {
        $error_msg = "خطأ عام في المزامنة الشاملة: " . $e->getMessage();
        $errors[] = $error_msg;
        logMessage("❌ " . $error_msg);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المزامنة الشاملة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #2196f3;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-title {
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            color: #1565c0;
            font-size: 0.9rem;
        }
        
        .sync-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 2rem 4rem;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1.3rem;
            font-weight: bold;
            display: block;
            margin: 2rem auto;
            transition: all 0.3s;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }
        
        .sync-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }
        
        .results {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .log-entry {
            background: white;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            border-left: 3px solid #667eea;
        }
        
        .log-entry.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .log-entry.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 المزامنة الشاملة لجميع الأجهزة</h1>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">جميع الأجهزة</div>
                <div class="feature-desc">Mobile, Desktop, Tablet, Android, iOS</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🌍</div>
                <div class="feature-title">جميع الدول</div>
                <div class="feature-desc">US, UK, Canada, Australia, Egypt, Saudi</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🖼️</div>
                <div class="feature-title">صور عالية الجودة</div>
                <div class="feature-desc">صور مخصصة بألوان متدرجة جذابة</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <div class="feature-title">إزالة التكرار</div>
                <div class="feature-desc">فلترة العروض المكررة تلقائياً</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">سرعة محسنة</div>
                <div class="feature-desc">معالجة متوازية وذكية</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">تسجيل مفصل</div>
                <div class="feature-desc">مراقبة شاملة لجميع العمليات</div>
            </div>
        </div>
        
        <form method="POST">
            <button type="submit" name="run_comprehensive_sync" class="sync-button">
                🚀 تشغيل المزامنة الشاملة
            </button>
        </form>
        
        <?php if (!empty($results) || !empty($errors)): ?>
        <div class="results">
            <h3>📋 سجل المزامنة الشاملة:</h3>
            
            <?php foreach ($errors as $error): ?>
                <div class="log-entry error">❌ <?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
            
            <?php foreach ($results as $result): ?>
                <div class="log-entry <?php echo (strpos($result, '✅') !== false) ? 'success' : ''; ?>">
                    <?php echo htmlspecialchars($result); ?>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="enhanced_sync.php">✨ المزامنة المحسنة</a>
            <a href="fix_images.php">🖼️ إصلاح الصور</a>
            <a href="test_ip_quality.php">🔍 اختبار IP</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
