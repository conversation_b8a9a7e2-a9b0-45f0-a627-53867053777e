<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$test_results = [];

// اختبار درجة الاحتيال
if (isset($_POST['test_fraud_score'])) {
    $test_ip = trim($_POST['test_ip']);
    
    if (empty($test_ip)) {
        $test_ip = $_SERVER['REMOTE_ADDR'];
    }
    
    if (filter_var($test_ip, FILTER_VALIDATE_IP)) {
        $start_time = microtime(true);
        $ipInfo = checkIPQuality($test_ip);
        $end_time = microtime(true);
        
        $response_time = round(($end_time - $start_time) * 1000, 2);
        
        // حساب النتيجة المتوقعة يدوياً
        $fraud_score = $ipInfo['fraud_score'] ?? 0;
        $expected_quality = 100 - $fraud_score;
        
        // تطبيق خصومات المخاطر
        if ($ipInfo['is_proxy'] || $ipInfo['is_vpn']) {
            $expected_quality -= 20;
        }
        if (isset($ipInfo['is_tor']) && $ipInfo['is_tor']) {
            $expected_quality -= 30;
        }
        if (isset($ipInfo['is_crawler']) && $ipInfo['is_crawler']) {
            $expected_quality -= 15;
        }
        
        $expected_quality = max(0, min(100, $expected_quality));
        
        $test_results = [
            'ip' => $test_ip,
            'response_time' => $response_time,
            'fraud_score' => $fraud_score,
            'expected_quality' => $expected_quality,
            'actual_quality' => $ipInfo['quality_score'],
            'is_correct' => abs($expected_quality - $ipInfo['quality_score']) <= 2,
            'country' => $ipInfo['country'],
            'city' => $ipInfo['city'],
            'isp' => $ipInfo['isp'],
            'is_proxy' => $ipInfo['is_proxy'],
            'is_vpn' => $ipInfo['is_vpn'],
            'is_tor' => $ipInfo['is_tor'] ?? false,
            'is_crawler' => $ipInfo['is_crawler'] ?? false,
            'raw_data' => $ipInfo
        ];
        
        $message = $test_results['is_correct'] ? 
            "✅ درجة الاحتيال تعمل بشكل صحيح!" : 
            "❌ هناك خطأ في حساب درجة الاحتيال!";
            
    } else {
        $message = "❌ عنوان IP غير صحيح!";
    }
}

// اختبار عدة IPs معروفة
$known_ips = [
    '*******' => ['name' => 'Google DNS', 'expected_fraud' => 'منخفض'],
    '*******' => ['name' => 'Cloudflare DNS', 'expected_fraud' => 'منخفض'],
    '**************' => ['name' => 'OpenDNS', 'expected_fraud' => 'منخفض'],
    '***************' => ['name' => 'GitHub', 'expected_fraud' => 'منخفض'],
    $_SERVER['REMOTE_ADDR'] => ['name' => 'عنوان IP الخاص بك', 'expected_fraud' => 'متغير']
];

if (isset($_POST['test_multiple_fraud'])) {
    $test_results = ['multiple' => []];
    
    foreach ($known_ips as $ip => $info) {
        if (filter_var($ip, FILTER_VALIDATE_IP)) {
            $start_time = microtime(true);
            $ipInfo = checkIPQuality($ip);
            $end_time = microtime(true);
            
            $response_time = round(($end_time - $start_time) * 1000, 2);
            $fraud_score = $ipInfo['fraud_score'] ?? 0;
            $expected_quality = 100 - $fraud_score;
            
            // تطبيق خصومات المخاطر
            if ($ipInfo['is_proxy'] || $ipInfo['is_vpn']) {
                $expected_quality -= 20;
            }
            if (isset($ipInfo['is_tor']) && $ipInfo['is_tor']) {
                $expected_quality -= 30;
            }
            if (isset($ipInfo['is_crawler']) && $ipInfo['is_crawler']) {
                $expected_quality -= 15;
            }
            
            $expected_quality = max(0, min(100, $expected_quality));
            
            $test_results['multiple'][] = [
                'ip' => $ip,
                'name' => $info['name'],
                'expected_fraud_level' => $info['expected_fraud'],
                'response_time' => $response_time,
                'fraud_score' => $fraud_score,
                'expected_quality' => $expected_quality,
                'actual_quality' => $ipInfo['quality_score'],
                'is_correct' => abs($expected_quality - $ipInfo['quality_score']) <= 2,
                'country' => $ipInfo['country'],
                'city' => $ipInfo['city'],
                'isp' => $ipInfo['isp'],
                'is_proxy' => $ipInfo['is_proxy'],
                'is_vpn' => $ipInfo['is_vpn'],
                'is_tor' => $ipInfo['is_tor'] ?? false,
                'is_crawler' => $ipInfo['is_crawler'] ?? false
            ];
        }
    }
    
    $correct_count = 0;
    foreach ($test_results['multiple'] as $result) {
        if ($result['is_correct']) $correct_count++;
    }
    
    $total_count = count($test_results['multiple']);
    $message = "✅ تم اختبار $total_count عنوان IP - $correct_count منها صحيح!";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار درجة الاحتيال - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .formula-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .formula-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .formula {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 1.1rem;
            color: #333;
            margin: 0.5rem 0;
        }
        
        .test-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .results-section {
            margin-top: 2rem;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .result-card.correct {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .result-card.incorrect {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }
        
        .ip-info {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .status-correct {
            background: #28a745;
            color: white;
        }
        
        .status-incorrect {
            background: #dc3545;
            color: white;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .detail-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .detail-label {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .detail-value {
            font-weight: bold;
            color: #333;
            font-size: 1.1rem;
        }
        
        .calculation-box {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            border-left: 3px solid #ffc107;
        }
        
        .calculation-step {
            font-family: monospace;
            margin: 0.3rem 0;
            color: #856404;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 اختبار درجة الاحتيال</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="formula-box">
            <h3>📐 معادلة حساب درجة الجودة:</h3>
            <div class="formula">quality_score = 100 - fraud_score</div>
            <div class="formula">إذا كان proxy أو vpn: quality_score -= 20</div>
            <div class="formula">إذا كان tor: quality_score -= 30</div>
            <div class="formula">إذا كان crawler: quality_score -= 15</div>
            <div class="formula">النتيجة النهائية = max(0, min(100, quality_score))</div>
        </div>
        
        <div class="test-form">
            <h3 style="margin-bottom: 1rem;">🧪 اختبار درجة الاحتيال</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="test_ip">عنوان IP للاختبار:</label>
                    <input type="text" id="test_ip" name="test_ip" 
                           placeholder="اتركه فارغاً لاختبار IP الخاص بك (<?php echo $_SERVER['REMOTE_ADDR']; ?>)" 
                           value="<?php echo isset($_POST['test_ip']) ? htmlspecialchars($_POST['test_ip']) : ''; ?>">
                </div>
                
                <button type="submit" name="test_fraud_score" class="btn">
                    🔍 اختبار درجة الاحتيال
                </button>
                
                <button type="submit" name="test_multiple_fraud" class="btn btn-success">
                    🌐 اختبار عدة IPs
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_results)): ?>
        <div class="results-section">
            <h3 style="color: #333; margin-bottom: 1rem;">📊 نتائج الاختبار</h3>
            
            <?php if (isset($test_results['multiple'])): ?>
                <?php foreach ($test_results['multiple'] as $result): ?>
                    <div class="result-card <?php echo $result['is_correct'] ? 'correct' : 'incorrect'; ?>">
                        <div class="result-header">
                            <div>
                                <div class="ip-info"><?php echo htmlspecialchars($result['ip']); ?></div>
                                <div style="color: #666; font-size: 0.9rem;"><?php echo htmlspecialchars($result['name']); ?></div>
                            </div>
                            <div class="status-badge <?php echo $result['is_correct'] ? 'status-correct' : 'status-incorrect'; ?>">
                                <?php echo $result['is_correct'] ? '✅ صحيح' : '❌ خطأ'; ?>
                            </div>
                        </div>
                        
                        <div class="details-grid">
                            <div class="detail-item">
                                <div class="detail-label">نقاط الاحتيال</div>
                                <div class="detail-value"><?php echo $result['fraud_score']; ?>%</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">الجودة المتوقعة</div>
                                <div class="detail-value" style="color: #28a745;"><?php echo $result['expected_quality']; ?>%</div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">الجودة الفعلية</div>
                                <div class="detail-value" style="color: <?php echo $result['is_correct'] ? '#28a745' : '#dc3545'; ?>;">
                                    <?php echo $result['actual_quality']; ?>%
                                </div>
                            </div>
                            
                            <div class="detail-item">
                                <div class="detail-label">وقت الاستجابة</div>
                                <div class="detail-value"><?php echo $result['response_time']; ?> مللي ثانية</div>
                            </div>
                        </div>
                        
                        <div class="calculation-box">
                            <strong>خطوات الحساب:</strong>
                            <div class="calculation-step">1. البداية: 100 - <?php echo $result['fraud_score']; ?> = <?php echo 100 - $result['fraud_score']; ?></div>
                            <?php if ($result['is_proxy'] || $result['is_vpn']): ?>
                                <div class="calculation-step">2. خصم البروكسي/VPN: <?php echo 100 - $result['fraud_score']; ?> - 20 = <?php echo 100 - $result['fraud_score'] - 20; ?></div>
                            <?php endif; ?>
                            <?php if ($result['is_tor']): ?>
                                <div class="calculation-step">3. خصم Tor: - 30</div>
                            <?php endif; ?>
                            <?php if ($result['is_crawler']): ?>
                                <div class="calculation-step">4. خصم Crawler: - 15</div>
                            <?php endif; ?>
                            <div class="calculation-step"><strong>النتيجة النهائية: <?php echo $result['expected_quality']; ?>%</strong></div>
                        </div>
                    </div>
                <?php endforeach; ?>
                
            <?php else: ?>
                <div class="result-card <?php echo $test_results['is_correct'] ? 'correct' : 'incorrect'; ?>">
                    <div class="result-header">
                        <div class="ip-info"><?php echo htmlspecialchars($test_results['ip']); ?></div>
                        <div class="status-badge <?php echo $test_results['is_correct'] ? 'status-correct' : 'status-incorrect'; ?>">
                            <?php echo $test_results['is_correct'] ? '✅ صحيح' : '❌ خطأ'; ?>
                        </div>
                    </div>
                    
                    <div class="details-grid">
                        <div class="detail-item">
                            <div class="detail-label">نقاط الاحتيال</div>
                            <div class="detail-value"><?php echo $test_results['fraud_score']; ?>%</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">الجودة المتوقعة</div>
                            <div class="detail-value" style="color: #28a745;"><?php echo $test_results['expected_quality']; ?>%</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">الجودة الفعلية</div>
                            <div class="detail-value" style="color: <?php echo $test_results['is_correct'] ? '#28a745' : '#dc3545'; ?>;">
                                <?php echo $test_results['actual_quality']; ?>%
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">وقت الاستجابة</div>
                            <div class="detail-value"><?php echo $test_results['response_time']; ?> مللي ثانية</div>
                        </div>
                    </div>
                    
                    <div class="calculation-box">
                        <strong>خطوات الحساب:</strong>
                        <div class="calculation-step">1. البداية: 100 - <?php echo $test_results['fraud_score']; ?> = <?php echo 100 - $test_results['fraud_score']; ?></div>
                        <?php if ($test_results['is_proxy'] || $test_results['is_vpn']): ?>
                            <div class="calculation-step">2. خصم البروكسي/VPN: <?php echo 100 - $test_results['fraud_score']; ?> - 20 = <?php echo 100 - $test_results['fraud_score'] - 20; ?></div>
                        <?php endif; ?>
                        <?php if ($test_results['is_tor']): ?>
                            <div class="calculation-step">3. خصم Tor: - 30</div>
                        <?php endif; ?>
                        <?php if ($test_results['is_crawler']): ?>
                            <div class="calculation-step">4. خصم Crawler: - 15</div>
                        <?php endif; ?>
                        <div class="calculation-step"><strong>النتيجة النهائية: <?php echo $test_results['expected_quality']; ?>%</strong></div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="comprehensive_sync.php">🌟 المزامنة الشاملة</a>
            <a href="fix_ip_score.php">🔧 إصلاح درجة IP</a>
            <a href="test_ip_quality.php">🔍 اختبار IP Quality</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
