<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();

// معالجة إنشاء اسم مستخدم جديد
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_username'])) {
    $newUsername = generateRandomUsername();
    $message = '<div class="alert alert-success">✅ تم إنشاء اسم مستخدم جديد: <strong>' . htmlspecialchars($newUsername) . '</strong></div>';
}

// الحصول على جميع أسماء المستخدمين
$usernames = $conn->query("SELECT username, ip_address, country, city, quality_score, saved_at FROM saved_ips ORDER BY saved_at DESC");

// إحصائيات أسماء المستخدمين
$stats_stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_usernames,
        COUNT(CASE WHEN quality_score >= 70 THEN 1 END) as high_quality_users,
        COUNT(CASE WHEN is_proxy = 1 THEN 1 END) as proxy_users,
        AVG(quality_score) as avg_quality
    FROM saved_ips
");
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أسماء المستخدمين - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .generator-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .generator-section h2 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .generator-section p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }
        
        .stat-card .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .usernames-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .username-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .username-card:hover {
            transform: translateY(-3px);
        }
        
        .username-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
            text-align: center;
        }
        
        .username-details {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }
        
        .username-details div {
            margin-bottom: 0.3rem;
        }
        
        .quality-score {
            font-weight: bold;
        }
        
        .quality-high { color: #28a745; }
        .quality-medium { color: #ffc107; }
        .quality-low { color: #dc3545; }
        
        .copy-username {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 0.3rem 0.6rem;
            border-radius: 3px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-top: 0.5rem;
            width: 100%;
        }
        
        .copy-username:hover {
            background: #138496;
        }
        
        .no-usernames {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            color: #666;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .usernames-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 أسماء المستخدمين</h1>
            <p>عرض وإدارة أسماء المستخدمين المُنشأة تلقائياً</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="username_manager.php">👤 إدارة أسماء المستخدمين</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="username_guide.php">📚 دليل الاستخدام</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="generator-section">
            <h2>🎲 مولد أسماء المستخدمين الأجنبية</h2>
            <p>اضغط الزر أدناه لإنشاء اسم مستخدم أجنبي عشوائي جديد (بدون أرقام)</p>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1rem; font-size: 0.9rem; color: #666;">
                <strong>أمثلة على الأنماط الجديدة:</strong><br>
                • <strong>صفة + اسم:</strong> FastTiger, CoolDragon, SmartWolf<br>
                • <strong>اسم أول + اسم:</strong> AlexEagle, MikePhoenix, JohnLion<br>
                • <strong>اسم + صفة:</strong> TigerFast, DragonCool, WolfSmart<br>
                • <strong>صفتان:</strong> FastCool, SmartBrave, EpicWild<br>
                • <strong>اسمان:</strong> TigerWolf, DragonEagle, LionPhoenix<br>
                • <strong>اسم واحد:</strong> Dragon, Phoenix, Tiger, Alex, Mike
            </div>
            <form method="POST">
                <button type="submit" name="generate_username" class="btn">🎯 إنشاء اسم مستخدم عشوائي</button>
            </form>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>👥 إجمالي المستخدمين</h3>
                <div class="stat-number"><?php echo number_format($stats['total_usernames']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>⭐ مستخدمين عالي الجودة</h3>
                <div class="stat-number"><?php echo number_format($stats['high_quality_users']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>⚠️ مستخدمين بروكسي</h3>
                <div class="stat-number"><?php echo number_format($stats['proxy_users']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>📊 متوسط الجودة</h3>
                <div class="stat-number"><?php echo number_format($stats['avg_quality'], 1); ?>%</div>
            </div>
        </div>
        
        <?php if ($usernames->num_rows > 0): ?>
            <div class="usernames-grid">
                <?php while ($user = $usernames->fetch_assoc()): ?>
                    <?php
                    $qualityClass = $user['quality_score'] >= 70 ? 'quality-high' : ($user['quality_score'] >= 40 ? 'quality-medium' : 'quality-low');
                    ?>
                    <div class="username-card">
                        <div class="username-title">👤 <?php echo htmlspecialchars($user['username']); ?></div>
                        <div class="username-details">
                            <div><strong>IP:</strong> <?php echo htmlspecialchars($user['ip_address']); ?></div>
                            <div><strong>الدولة:</strong> <?php echo htmlspecialchars($user['country']); ?></div>
                            <div><strong>المدينة:</strong> <?php echo htmlspecialchars($user['city']); ?></div>
                            <div><strong>الجودة:</strong> 
                                <span class="quality-score <?php echo $qualityClass; ?>">
                                    <?php echo $user['quality_score']; ?>/100
                                </span>
                            </div>
                            <div><strong>تاريخ الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($user['saved_at'])); ?></div>
                        </div>
                        <button class="copy-username" onclick="copyUsername('<?php echo addslashes($user['username']); ?>')">
                            📋 نسخ اسم المستخدم
                        </button>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="no-usernames">
                <h3>😔 لا توجد أسماء مستخدمين</h3>
                <p>لم يتم إنشاء أي أسماء مستخدمين بعد.</p>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
        function copyUsername(username) {
            navigator.clipboard.writeText(username).then(function() {
                alert('تم نسخ اسم المستخدم: ' + username + ' 📋');
            }, function(err) {
                console.error('فشل في نسخ اسم المستخدم: ', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement("textarea");
                textArea.value = username;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('تم نسخ اسم المستخدم: ' + username + ' 📋');
                } catch (err) {
                    alert('فشل في نسخ اسم المستخدم. يرجى النسخ يدوياً.');
                }
                document.body.removeChild(textArea);
            });
        }
    </script>
</body>
</html>

<?php
$conn->close();
?>
