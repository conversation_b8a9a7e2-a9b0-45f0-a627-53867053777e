/**
 * تحديث الأوقات النسبية في الصفحة تلقائياً
 * يعمل مع الإشعارات والعروض والتحويلات
 */

// دالة لتنسيق الوقت النسبي بالعربية
function formatRelativeTime(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffSeconds = Math.floor((now - time) / 1000);
    
    // إذا كان الوقت في المستقبل
    if (diffSeconds < 0) {
        return time.toLocaleString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    }
    
    // أقل من دقيقة
    if (diffSeconds < 60) {
        return "الآن";
    }
    
    // أقل من ساعة
    if (diffSeconds < 3600) {
        const minutes = Math.floor(diffSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    }
    
    // أقل من يوم
    if (diffSeconds < 86400) {
        const hours = Math.floor(diffSeconds / 3600);
        return `منذ ${hours} ساعة`;
    }
    
    // أقل من أسبوع
    if (diffSeconds < 604800) {
        const days = Math.floor(diffSeconds / 86400);
        if (days === 1) {
            return `أمس في ${time.toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit', hour12: true })}`;
        } else {
            return `منذ ${days} يوم`;
        }
    }
    
    // أكثر من أسبوع - اعرض التاريخ الكامل
    return time.toLocaleString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

// دالة لتحديث جميع الأوقات في الصفحة
function updateAllTimes() {
    // تحديث أوقات الإشعارات
    const notificationTimes = document.querySelectorAll('[data-timestamp]');
    notificationTimes.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });
    
    // تحديث أوقات العروض
    const offerTimes = document.querySelectorAll('.offer-time[data-timestamp]');
    offerTimes.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });
    
    // تحديث أوقات التحويلات
    const conversionTimes = document.querySelectorAll('.conversion-time[data-timestamp]');
    conversionTimes.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            element.textContent = formatRelativeTime(timestamp);
        }
    });
}

// تحديث الأوقات كل دقيقة
function startTimeUpdater() {
    // تحديث فوري
    updateAllTimes();
    
    // تحديث كل دقيقة
    setInterval(updateAllTimes, 60000);
}

// بدء التحديث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', startTimeUpdater);

// دالة لإضافة timestamp لعنصر جديد
function addTimestamp(element, timestamp) {
    element.setAttribute('data-timestamp', timestamp);
    element.textContent = formatRelativeTime(timestamp);
}

// دالة لتحديث وقت واحد فقط
function updateSingleTime(element, timestamp) {
    if (element && timestamp) {
        element.setAttribute('data-timestamp', timestamp);
        element.textContent = formatRelativeTime(timestamp);
    }
}

// تصدير الدوال للاستخدام الخارجي
window.TimeUpdater = {
    formatRelativeTime,
    updateAllTimes,
    addTimestamp,
    updateSingleTime,
    startTimeUpdater
};
