<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();

// معالجة حذف IP
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $ip_id = (int)$_GET['delete'];
    $stmt = $conn->prepare("DELETE FROM saved_ips WHERE id = ?");
    $stmt->bind_param("i", $ip_id);
    
    if ($stmt->execute()) {
        $message = '<div class="alert alert-success">✅ تم حذف عنوان IP بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في حذف عنوان IP!</div>';
    }
    $stmt->close();
}

// الحصول على جميع الـ IPs المحفوظة
$saved_ips = $conn->query("SELECT * FROM saved_ips ORDER BY saved_at DESC");

// إحصائيات سريعة
$stats_stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_saved_ips,
        COUNT(CASE WHEN is_proxy = 1 THEN 1 END) as proxy_count,
        COUNT(CASE WHEN quality_score >= 70 THEN 1 END) as high_quality,
        COUNT(CASE WHEN quality_score < 40 THEN 1 END) as low_quality,
        AVG(quality_score) as avg_quality
    FROM saved_ips
");
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الـ IPs المحفوظة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }
        
        .stat-card .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .ips-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .ips-table h2 {
            padding: 1.5rem;
            background: #f8f9fa;
            color: #333;
            margin: 0;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid #eee;
            font-size: 0.9rem;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .ip-address {
            font-family: monospace;
            background: #f1f3f4;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
        }
        
        .quality-score {
            font-weight: bold;
        }
        
        .quality-high { color: #28a745; }
        .quality-medium { color: #ffc107; }
        .quality-low { color: #dc3545; }
        
        .proxy-badge {
            background: #dc3545;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
        }

        .username-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: bold;
            display: inline-block;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 0.4rem 0.8rem;
            text-decoration: none;
            border-radius: 3px;
            font-size: 0.8rem;
            transition: transform 0.2s;
        }
        
        .btn-danger:hover {
            transform: translateY(-1px);
        }
        
        .no-ips {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            th, td {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💾 الـ IPs المحفوظة</h1>
            <p>عرض وإدارة عناوين IP المحفوظة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
                <a href="api_manager.php">🔌 إدارة API</a>
            </div>
        </div>
        
        <?php if (isset($message)) echo $message; ?>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📊 إجمالي الـ IPs</h3>
                <div class="stat-number"><?php echo number_format($stats['total_saved_ips']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>⚠️ بروكسي</h3>
                <div class="stat-number"><?php echo number_format($stats['proxy_count']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>✅ جودة عالية</h3>
                <div class="stat-number"><?php echo number_format($stats['high_quality']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>❌ جودة منخفضة</h3>
                <div class="stat-number"><?php echo number_format($stats['low_quality']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>📈 متوسط الجودة</h3>
                <div class="stat-number"><?php echo number_format($stats['avg_quality'], 1); ?>%</div>
            </div>
        </div>
        
        <div class="ips-table">
            <h2>📋 قائمة الـ IPs المحفوظة</h2>
            <div class="table-responsive">
                <?php if ($saved_ips->num_rows > 0): ?>
                    <table>
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>عنوان IP</th>
                                <th>اسم المستخدم</th>
                                <th>الدولة</th>
                                <th>المدينة</th>
                                <th>مزود الخدمة</th>
                                <th>بروكسي</th>
                                <th>نقاط الجودة</th>
                                <th>تاريخ الحفظ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($ip = $saved_ips->fetch_assoc()): ?>
                                <?php
                                $qualityClass = $ip['quality_score'] >= 70 ? 'quality-high' : ($ip['quality_score'] >= 40 ? 'quality-medium' : 'quality-low');
                                ?>
                                <tr>
                                    <td><?php echo $ip['id']; ?></td>
                                    <td>
                                        <span class="ip-address"><?php echo htmlspecialchars($ip['ip_address']); ?></span>
                                    </td>
                                    <td>
                                        <span class="username-badge"><?php echo htmlspecialchars($ip['username']); ?></span>
                                    </td>
                                    <td><?php echo htmlspecialchars($ip['country']); ?></td>
                                    <td><?php echo htmlspecialchars($ip['city']); ?></td>
                                    <td><?php echo htmlspecialchars($ip['isp']); ?></td>
                                    <td>
                                        <?php if ($ip['is_proxy']): ?>
                                            <span class="proxy-badge">بروكسي</span>
                                        <?php else: ?>
                                            ✅ عادي
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="quality-score <?php echo $qualityClass; ?>">
                                            <?php echo $ip['quality_score']; ?>/100
                                        </span>
                                    </td>
                                    <td><?php echo formatTime12Hour($ip['saved_at']); ?></td>
                                    <td>
                                        <a href="?delete=<?php echo $ip['id']; ?>"
                                           class="btn-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا IP؟')">
                                            🗑️ حذف
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="no-ips">
                        <h3>😔 لا توجد عناوين IP محفوظة</h3>
                        <p>لم يتم حفظ أي عناوين IP بعد.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>

<?php
$conn->close();
?>
