<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// معالجة إضافة عرض مميز
if (isset($_POST['add_featured'])) {
    $offer_id = intval($_POST['offer_id']);
    $position = intval($_POST['position']);
    
    if ($offer_id > 0) {
        try {
            $conn = getDBConnection();
            
            // التحقق من وجود جدول العروض المميزة
            $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
            if ($table_check->num_rows == 0) {
                // إنشاء جدول العروض المميزة
                $create_table = "
                    CREATE TABLE featured_offers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        offer_id INT NOT NULL,
                        position INT NOT NULL DEFAULT 1,
                        is_active TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_offer (offer_id),
                        INDEX idx_position_active (position, is_active)
                    )
                ";
                $conn->query($create_table);
            }
            
            // التحقق من وجود العرض
            $check_offer = $conn->prepare("SELECT id, title FROM offers WHERE id = ? AND is_active = 1");
            $check_offer->bind_param("i", $offer_id);
            $check_offer->execute();
            $offer_result = $check_offer->get_result();
            
            if ($offer_result->num_rows > 0) {
                $offer_data = $offer_result->fetch_assoc();
                
                // التحقق من عدم وجود العرض في القائمة المميزة
                $check_featured = $conn->prepare("SELECT id FROM featured_offers WHERE offer_id = ?");
                $check_featured->bind_param("i", $offer_id);
                $check_featured->execute();
                
                if ($check_featured->get_result()->num_rows == 0) {
                    // إضافة العرض للقائمة المميزة
                    $insert_stmt = $conn->prepare("INSERT INTO featured_offers (offer_id, position) VALUES (?, ?)");
                    $insert_stmt->bind_param("ii", $offer_id, $position);
                    
                    if ($insert_stmt->execute()) {
                        $success = true;
                        $message = "✅ تم إضافة العرض '{$offer_data['title']}' للعروض المميزة!";
                    } else {
                        $message = "❌ فشل في إضافة العرض للقائمة المميزة!";
                    }
                    $insert_stmt->close();
                } else {
                    $message = "⚠️ هذا العرض موجود بالفعل في القائمة المميزة!";
                }
                $check_featured->close();
            } else {
                $message = "❌ العرض غير موجود أو غير نشط!";
            }
            $check_offer->close();
            $conn->close();
            
        } catch (Exception $e) {
            $message = "❌ خطأ في إضافة العرض: " . $e->getMessage();
        }
    } else {
        $message = "❌ يرجى اختيار عرض صحيح!";
    }
}

// معالجة إزالة عرض مميز
if (isset($_POST['remove_featured'])) {
    $featured_id = intval($_POST['featured_id']);
    
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("DELETE FROM featured_offers WHERE id = ?");
        $stmt->bind_param("i", $featured_id);
        
        if ($stmt->execute()) {
            $success = true;
            $message = "✅ تم إزالة العرض من القائمة المميزة!";
        } else {
            $message = "❌ فشل في إزالة العرض!";
        }
        $stmt->close();
        $conn->close();
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إزالة العرض: " . $e->getMessage();
    }
}

// معالجة تحديث ترتيب العروض
if (isset($_POST['update_positions'])) {
    $positions = $_POST['positions'] ?? [];
    
    try {
        $conn = getDBConnection();
        
        foreach ($positions as $featured_id => $position) {
            $stmt = $conn->prepare("UPDATE featured_offers SET position = ? WHERE id = ?");
            $stmt->bind_param("ii", $position, $featured_id);
            $stmt->execute();
            $stmt->close();
        }
        
        $success = true;
        $message = "✅ تم تحديث ترتيب العروض المميزة!";
        $conn->close();
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الترتيب: " . $e->getMessage();
    }
}

// جلب العروض المتاحة للإضافة
$available_offers = [];
try {
    $conn = getDBConnection();

    // التحقق من وجود جدول العروض المميزة
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");

    if ($table_check->num_rows > 0) {
        // جلب العروض النشطة التي ليست في القائمة المميزة
        $query = "
            SELECT o.id, o.title, o.amount, o.image_url
            FROM offers o
            LEFT JOIN featured_offers f ON o.id = f.offer_id
            WHERE o.is_active = 1 AND f.offer_id IS NULL
            ORDER BY o.created_at DESC
            LIMIT 50
        ";
    } else {
        // إذا لم يكن الجدول موجود، جلب جميع العروض النشطة
        $query = "
            SELECT o.id, o.title, o.amount, o.image_url
            FROM offers o
            WHERE o.is_active = 1
            ORDER BY o.created_at DESC
            LIMIT 50
        ";
    }

    $available_offers = $conn->query($query);
    $conn->close();

} catch (Exception $e) {
    if (empty($message)) {
        $message = "❌ خطأ في جلب العروض: " . $e->getMessage();
    }
}

// جلب العروض المميزة الحالية
$featured_offers = [];
$table_exists = false;
try {
    $conn = getDBConnection();

    // التحقق من وجود جدول العروض المميزة
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
    $table_exists = $table_check->num_rows > 0;

    if (!$table_exists) {
        // محاولة إنشاء الجدول تلقائياً
        try {
            $create_table = "
                CREATE TABLE featured_offers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    offer_id INT NOT NULL,
                    position INT NOT NULL DEFAULT 1,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_offer (offer_id),
                    INDEX idx_position_active (position, is_active)
                )
            ";
            $conn->query($create_table);
            $table_exists = true;
            $message = "✅ تم إنشاء جدول العروض المميزة تلقائياً!";
            $success = true;
        } catch (Exception $create_error) {
            $message = "❌ لم يتم العثور على جدول العروض المميزة. <a href='create_featured_table.php' style='color: #fff; text-decoration: underline;'>اضغط هنا لإنشاؤه</a>";
        }
    }

    if ($table_exists) {
        $query = "
            SELECT f.id as featured_id, f.position, o.id, o.title, o.amount, o.image_url, o.offer_url
            FROM featured_offers f 
            JOIN offers o ON f.offer_id = o.id 
            WHERE f.is_active = 1 AND o.is_active = 1 
            ORDER BY f.position ASC, f.created_at ASC
        ";
        
        $featured_offers = $conn->query($query);
    }
    $conn->close();
    
} catch (Exception $e) {
    $message = "❌ خطأ في جلب العروض المميزة: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض المميزة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .add-form {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            align-items: end;
        }
        
        @media (max-width: 768px) {
            .add-form {
                grid-template-columns: 1fr;
            }
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .featured-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            position: relative;
        }
        
        .featured-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .featured-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .featured-card .amount {
            background: #28a745;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .position-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: space-between;
            align-items: center;
        }
        
        .position-input {
            width: 60px;
            padding: 0.3rem;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        
        .available-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .available-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #eee;
        }
        
        .available-card img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        
        .available-card h5 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .available-card .amount {
            background: #17a2b8;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
            color: #1565c0;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⭐ إدارة العروض المميزة</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if (!$table_exists): ?>
            <div class="message error">
                ⚠️ <strong>تحذير:</strong> جدول العروض المميزة غير موجود!<br>
                <a href="create_featured_table.php" style="color: #fff; text-decoration: underline; font-weight: bold;">
                    🗃️ اضغط هنا لإنشاء الجدول
                </a>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 حول العروض المميزة:</h3>
            <ul>
                <li><strong>المكان:</strong> تظهر العروض المميزة في أعلى الصفحة الرئيسية</li>
                <li><strong>الترتيب:</strong> يمكن ترتيب العروض حسب الأولوية (1 = الأول)</li>
                <li><strong>العدد:</strong> يُنصح بعدم تجاوز 6 عروض مميزة</li>
                <li><strong>التحديث:</strong> يمكن إضافة وإزالة العروض في أي وقت</li>
            </ul>
        </div>
        
        <!-- العروض المميزة الحالية -->
        <div class="section">
            <h3>⭐ العروض المميزة الحالية</h3>
            
            <?php if ($featured_offers && $featured_offers->num_rows > 0): ?>
                <form method="POST">
                    <div class="featured-grid">
                        <?php while ($offer = $featured_offers->fetch_assoc()): ?>
                            <div class="featured-card">
                                <div class="position-badge"><?php echo $offer['position']; ?></div>
                                
                                <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                                     alt="<?php echo htmlspecialchars($offer['title']); ?>"
                                     onerror="this.src='https://via.placeholder.com/300x150/667eea/ffffff?text=صورة'">
                                
                                <h4><?php echo htmlspecialchars($offer['title']); ?></h4>
                                
                                <?php if ($offer['amount'] > 0): ?>
                                    <div class="amount">$<?php echo number_format($offer['amount'], 2); ?></div>
                                <?php endif; ?>
                                
                                <div class="card-actions">
                                    <div>
                                        <label style="font-size: 0.8rem; color: #666;">الترتيب:</label>
                                        <input type="number" name="positions[<?php echo $offer['featured_id']; ?>]" 
                                               value="<?php echo $offer['position']; ?>" 
                                               min="1" max="99" class="position-input">
                                    </div>
                                    
                                    <div>
                                        <button type="submit" name="remove_featured" value="<?php echo $offer['featured_id']; ?>" 
                                                class="btn btn-danger btn-sm"
                                                onclick="return confirm('هل أنت متأكد من إزالة هذا العرض من القائمة المميزة؟')">
                                            🗑️ إزالة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    
                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" name="update_positions" class="btn btn-success">
                            🔄 تحديث الترتيب
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #666;">
                    <h4>لا توجد عروض مميزة حالياً</h4>
                    <p>ابدأ بإضافة عروض مميزة من القائمة أدناه</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- إضافة عرض مميز جديد -->
        <div class="section">
            <h3>➕ إضافة عرض مميز جديد</h3>
            
            <form method="POST" class="add-form">
                <div class="form-group">
                    <label for="offer_id">اختر العرض:</label>
                    <select id="offer_id" name="offer_id" required>
                        <option value="">-- اختر عرض --</option>
                        <?php if ($available_offers && $available_offers->num_rows > 0): ?>
                            <?php while ($offer = $available_offers->fetch_assoc()): ?>
                                <option value="<?php echo $offer['id']; ?>">
                                    <?php echo htmlspecialchars($offer['title']); ?>
                                    <?php if ($offer['amount'] > 0): ?>
                                        - $<?php echo number_format($offer['amount'], 2); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="position">الترتيب:</label>
                    <input type="number" id="position" name="position" value="1" min="1" max="99" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" name="add_featured" class="btn btn-success">
                        ⭐ إضافة للمميزة
                    </button>
                </div>
            </form>
        </div>
        
        <!-- العروض المتاحة -->
        <div class="section">
            <h3>📋 العروض المتاحة للإضافة</h3>
            
            <?php if ($available_offers && $available_offers->num_rows > 0): ?>
                <?php 
                // إعادة تعيين المؤشر لعرض العروض مرة أخرى
                $available_offers->data_seek(0);
                ?>
                <div class="available-grid">
                    <?php while ($offer = $available_offers->fetch_assoc()): ?>
                        <div class="available-card">
                            <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                                 alt="<?php echo htmlspecialchars($offer['title']); ?>"
                                 onerror="this.src='https://via.placeholder.com/250x100/667eea/ffffff?text=صورة'">
                            
                            <h5 title="<?php echo htmlspecialchars($offer['title']); ?>">
                                <?php echo htmlspecialchars($offer['title']); ?>
                            </h5>
                            
                            <?php if ($offer['amount'] > 0): ?>
                                <div class="amount">$<?php echo number_format($offer['amount'], 2); ?></div>
                            <?php endif; ?>
                            
                            <button onclick="selectOffer(<?php echo $offer['id']; ?>, '<?php echo htmlspecialchars($offer['title'], ENT_QUOTES); ?>')" 
                                    class="btn btn-sm" style="width: 100%;">
                                ⭐ اختيار
                            </button>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #666;">
                    <h4>لا توجد عروض متاحة</h4>
                    <p>جميع العروض النشطة موجودة بالفعل في القائمة المميزة أو لا توجد عروض نشطة.</p>
                    <a href="add_manual_offer.php" class="btn btn-success" style="margin-top: 1rem;">
                        ➕ إضافة عرض جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="manage_offers.php">📋 إدارة العروض</a>
            <a href="add_manual_offer.php">➕ إضافة عرض جديد</a>
            <a href="api_manager.php">📡 لوحة التحكم</a>
        </div>
    </div>
    
    <script>
        function selectOffer(offerId, offerTitle) {
            document.getElementById('offer_id').value = offerId;
            
            // تمييز العرض المختار
            const cards = document.querySelectorAll('.available-card');
            cards.forEach(card => card.style.border = '1px solid #eee');
            
            event.target.closest('.available-card').style.border = '2px solid #667eea';
            
            // التمرير لنموذج الإضافة
            document.querySelector('.add-form').scrollIntoView({ behavior: 'smooth' });
            
            // تركيز على حقل الترتيب
            setTimeout(() => {
                document.getElementById('position').focus();
            }, 500);
        }
    </script>
</body>
</html>
