<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// معالجة إضافة عرض مميز
if (isset($_POST['add_featured'])) {
    $offer_id = intval($_POST['offer_id']);
    $position = intval($_POST['position']);
    
    if ($offer_id > 0) {
        try {
            $conn = getDBConnection();
            
            // التحقق من وجود جدول العروض المميزة
            $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
            if ($table_check->num_rows == 0) {
                // إنشاء جدول العروض المميزة
                $create_table = "
                    CREATE TABLE featured_offers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        offer_id INT NOT NULL,
                        position INT NOT NULL DEFAULT 1,
                        is_active TINYINT(1) DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_offer (offer_id),
                        INDEX idx_position_active (position, is_active)
                    )
                ";
                $conn->query($create_table);
            }
            
            // التحقق من وجود العرض
            $check_offer = $conn->prepare("SELECT id, title FROM offers WHERE id = ? AND is_active = 1");
            $check_offer->bind_param("i", $offer_id);
            $check_offer->execute();
            $offer_result = $check_offer->get_result();
            
            if ($offer_result->num_rows > 0) {
                $offer_data = $offer_result->fetch_assoc();
                
                // التحقق من عدم وجود العرض في القائمة المميزة
                $check_featured = $conn->prepare("SELECT id FROM featured_offers WHERE offer_id = ?");
                $check_featured->bind_param("i", $offer_id);
                $check_featured->execute();
                
                if ($check_featured->get_result()->num_rows == 0) {
                    // إضافة العرض للقائمة المميزة
                    $insert_stmt = $conn->prepare("INSERT INTO featured_offers (offer_id, position) VALUES (?, ?)");
                    $insert_stmt->bind_param("ii", $offer_id, $position);
                    
                    if ($insert_stmt->execute()) {
                        $success = true;
                        $message = "✅ تم إضافة العرض '{$offer_data['title']}' للعروض المميزة!";
                    } else {
                        $message = "❌ فشل في إضافة العرض للقائمة المميزة!";
                    }
                    $insert_stmt->close();
                } else {
                    $message = "⚠️ هذا العرض موجود بالفعل في القائمة المميزة!";
                }
                $check_featured->close();
            } else {
                $message = "❌ العرض غير موجود أو غير نشط!";
            }
            $check_offer->close();
            $conn->close();
            
        } catch (Exception $e) {
            $message = "❌ خطأ في إضافة العرض: " . $e->getMessage();
        }
    } else {
        $message = "❌ يرجى اختيار عرض صحيح!";
    }
}

// معالجة إزالة عرض مميز
if (isset($_POST['remove_featured'])) {
    $featured_id = intval($_POST['remove_featured']);

    if ($featured_id > 0) {
        try {
            $conn = getDBConnection();

            // جلب معلومات العرض قبل الحذف
            $get_offer = $conn->prepare("
                SELECT o.title
                FROM featured_offers f
                JOIN offers o ON f.offer_id = o.id
                WHERE f.id = ?
            ");
            $get_offer->bind_param("i", $featured_id);
            $get_offer->execute();
            $offer_result = $get_offer->get_result();

            if ($offer_result->num_rows > 0) {
                $offer_data = $offer_result->fetch_assoc();
                $offer_title = $offer_data['title'];

                // حذف العرض من القائمة المميزة
                $stmt = $conn->prepare("DELETE FROM featured_offers WHERE id = ?");
                $stmt->bind_param("i", $featured_id);

                if ($stmt->execute() && $stmt->affected_rows > 0) {
                    $success = true;
                    $message = "✅ تم إزالة العرض '{$offer_title}' من القائمة المميزة بنجاح!";

                    // إضافة إشعار
                    if (function_exists('addNotification')) {
                        addNotification(
                            'featured_removed',
                            'تم إزالة عرض مميز',
                            "تم إزالة العرض: {$offer_title} من القائمة المميزة",
                            ['featured_id' => $featured_id, 'title' => $offer_title]
                        );
                    }
                } else {
                    $message = "❌ فشل في إزالة العرض أو العرض غير موجود!";
                }
                $stmt->close();
            } else {
                $message = "❌ العرض المميز غير موجود!";
            }
            $get_offer->close();
            $conn->close();

        } catch (Exception $e) {
            $message = "❌ خطأ في إزالة العرض: " . $e->getMessage();
        }
    } else {
        $message = "❌ معرف العرض غير صحيح!";
    }
}

// معالجة تحديث ترتيب العروض
if (isset($_POST['update_positions'])) {
    $positions = $_POST['positions'] ?? [];
    
    try {
        $conn = getDBConnection();
        
        foreach ($positions as $featured_id => $position) {
            $stmt = $conn->prepare("UPDATE featured_offers SET position = ? WHERE id = ?");
            $stmt->bind_param("ii", $position, $featured_id);
            $stmt->execute();
            $stmt->close();
        }
        
        $success = true;
        $message = "✅ تم تحديث ترتيب العروض المميزة!";
        $conn->close();
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الترتيب: " . $e->getMessage();
    }
}

// جلب العروض المتاحة للإضافة
$available_offers = [];
try {
    $conn = getDBConnection();

    // التحقق من وجود جدول العروض المميزة
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");

    // جلب جميع العروض النشطة مع تمييز المميزة منها
    if ($table_check->num_rows > 0) {
        $query = "
            SELECT o.id, o.title, o.amount, o.image_url,
                   CASE WHEN f.offer_id IS NOT NULL THEN 1 ELSE 0 END as is_featured,
                   f.position as featured_position
            FROM offers o
            LEFT JOIN featured_offers f ON o.id = f.offer_id AND f.is_active = 1
            WHERE o.is_active = 1
            ORDER BY is_featured DESC, o.created_at DESC
            LIMIT 100
        ";
    } else {
        // إذا لم يكن الجدول موجود، جلب جميع العروض النشطة
        $query = "
            SELECT o.id, o.title, o.amount, o.image_url,
                   0 as is_featured, NULL as featured_position
            FROM offers o
            WHERE o.is_active = 1
            ORDER BY o.created_at DESC
            LIMIT 100
        ";
    }

    $available_offers = $conn->query($query);
    $conn->close();

} catch (Exception $e) {
    if (empty($message)) {
        $message = "❌ خطأ في جلب العروض: " . $e->getMessage();
    }
}

// جلب العروض المميزة الحالية
$featured_offers = [];
$table_exists = false;
try {
    $conn = getDBConnection();

    // التحقق من وجود جدول العروض المميزة
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
    $table_exists = $table_check->num_rows > 0;

    if (!$table_exists) {
        // محاولة إنشاء الجدول تلقائياً
        try {
            $create_table = "
                CREATE TABLE featured_offers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    offer_id INT NOT NULL,
                    position INT NOT NULL DEFAULT 1,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_offer (offer_id),
                    INDEX idx_position_active (position, is_active)
                )
            ";
            $conn->query($create_table);
            $table_exists = true;
            $message = "✅ تم إنشاء جدول العروض المميزة تلقائياً!";
            $success = true;
        } catch (Exception $create_error) {
            $message = "❌ لم يتم العثور على جدول العروض المميزة. <a href='create_featured_table.php' style='color: #fff; text-decoration: underline;'>اضغط هنا لإنشاؤه</a>";
        }
    }

    if ($table_exists) {
        $query = "
            SELECT f.id as featured_id, f.position, o.id, o.title, o.amount, o.image_url, o.offer_url
            FROM featured_offers f 
            JOIN offers o ON f.offer_id = o.id 
            WHERE f.is_active = 1 AND o.is_active = 1 
            ORDER BY f.position ASC, f.created_at ASC
        ";
        
        $featured_offers = $conn->query($query);
    }
    $conn->close();
    
} catch (Exception $e) {
    $message = "❌ خطأ في جلب العروض المميزة: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض المميزة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .add-form {
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 1rem;
            align-items: end;
        }
        
        @media (max-width: 768px) {
            .add-form {
                grid-template-columns: 1fr;
            }
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .featured-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1rem;
        }
        
        .featured-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            position: relative;
        }
        
        .featured-card img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .featured-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .featured-card .amount {
            background: #28a745;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .position-badge {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
        }
        
        .card-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: space-between;
            align-items: center;
        }
        
        .position-input {
            width: 60px;
            padding: 0.3rem;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        
        .available-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .available-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #eee;
        }
        
        .available-card img {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 0.5rem;
        }
        
        .available-card h5 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .available-card .amount {
            background: #17a2b8;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }

        .featured-card-highlight {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%) !important;
            border: 2px solid #ff9800 !important;
            position: relative;
        }

        .featured-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
            z-index: 10;
        }

        .featured-card-highlight:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
            color: #1565c0;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⭐ إدارة العروض المميزة</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if (!$table_exists): ?>
            <div class="message error">
                ⚠️ <strong>تحذير:</strong> جدول العروض المميزة غير موجود!<br>
                <a href="create_featured_table.php" style="color: #fff; text-decoration: underline; font-weight: bold;">
                    🗃️ اضغط هنا لإنشاء الجدول
                </a>
            </div>
        <?php endif; ?>
        
        <!-- إحصائيات سريعة -->
        <?php
        $stats = ['total' => 0, 'featured' => 0, 'available' => 0];
        if ($available_offers) {
            $available_offers->data_seek(0);
            while ($offer = $available_offers->fetch_assoc()) {
                $stats['total']++;
                if ($offer['is_featured']) {
                    $stats['featured']++;
                } else {
                    $stats['available']++;
                }
            }
        }
        ?>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
            <div style="background: #e3f2fd; padding: 1.5rem; border-radius: 10px; text-align: center; border-left: 4px solid #2196f3;">
                <div style="font-size: 2rem; font-weight: bold; color: #1976d2;"><?php echo $stats['total']; ?></div>
                <div style="color: #666; font-size: 0.9rem;">إجمالي العروض</div>
            </div>

            <div style="background: #fff3e0; padding: 1.5rem; border-radius: 10px; text-align: center; border-left: 4px solid #ff9800;">
                <div style="font-size: 2rem; font-weight: bold; color: #f57c00;"><?php echo $stats['featured']; ?></div>
                <div style="color: #666; font-size: 0.9rem;">العروض المميزة</div>
            </div>

            <div style="background: #e8f5e8; padding: 1.5rem; border-radius: 10px; text-align: center; border-left: 4px solid #4caf50;">
                <div style="font-size: 2rem; font-weight: bold; color: #388e3c;"><?php echo $stats['available']; ?></div>
                <div style="color: #666; font-size: 0.9rem;">العروض العادية</div>
            </div>
        </div>

        <div class="info-box">
            <h3>📋 حول العروض المميزة:</h3>
            <ul>
                <li><strong>المكان:</strong> تظهر العروض المميزة في أعلى الصفحة الرئيسية</li>
                <li><strong>الترتيب:</strong> يمكن ترتيب العروض حسب الأولوية (1 = الأول)</li>
                <li><strong>العدد:</strong> يُنصح بعدم تجاوز 6 عروض مميزة</li>
                <li><strong>التحديث:</strong> يمكن إضافة وإزالة العروض في أي وقت</li>
                <li><strong>الاختيار:</strong> يمكن اختيار أي عرض من جميع العروض المتاحة</li>
            </ul>
        </div>
        
        <!-- العروض المميزة الحالية -->
        <div class="section">
            <h3>⭐ العروض المميزة الحالية</h3>
            
            <?php if ($featured_offers && $featured_offers->num_rows > 0): ?>
                <form method="POST">
                    <div class="featured-grid">
                        <?php while ($offer = $featured_offers->fetch_assoc()): ?>
                            <div class="featured-card">
                                <div class="position-badge"><?php echo $offer['position']; ?></div>
                                
                                <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                                     alt="<?php echo htmlspecialchars($offer['title']); ?>"
                                     onerror="this.src='https://via.placeholder.com/300x150/667eea/ffffff?text=صورة'">
                                
                                <h4><?php echo htmlspecialchars($offer['title']); ?></h4>
                                
                                <?php if ($offer['amount'] > 0): ?>
                                    <div class="amount">$<?php echo number_format($offer['amount'], 2); ?></div>
                                <?php endif; ?>
                                
                                <div class="card-actions">
                                    <div>
                                        <label style="font-size: 0.8rem; color: #666;">الترتيب:</label>
                                        <input type="number" name="positions[<?php echo $offer['featured_id']; ?>]"
                                               value="<?php echo $offer['position']; ?>"
                                               min="1" max="99" class="position-input">
                                    </div>

                                    <div>
                                        <!-- نموذج منفصل لحذف العرض -->
                                        <form method="POST" style="display: inline-block;">
                                            <button type="submit" name="remove_featured" value="<?php echo $offer['featured_id']; ?>"
                                                    class="btn btn-danger btn-sm"
                                                    onclick="return confirm('هل أنت متأكد من إزالة هذا العرض من القائمة المميزة؟')">
                                                🗑️ إزالة
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                    
                    <div style="text-align: center; margin-top: 2rem;">
                        <button type="submit" name="update_positions" class="btn btn-success">
                            🔄 تحديث الترتيب
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #666;">
                    <h4>لا توجد عروض مميزة حالياً</h4>
                    <p>ابدأ بإضافة عروض مميزة من القائمة أدناه</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- إضافة عرض مميز جديد -->
        <div class="section">
            <h3>➕ إضافة عرض مميز جديد</h3>
            
            <form method="POST" class="add-form">
                <div class="form-group">
                    <label for="offer_id">اختر العرض:</label>
                    <select id="offer_id" name="offer_id" required>
                        <option value="">-- اختر عرض --</option>
                        <?php if ($available_offers && $available_offers->num_rows > 0): ?>
                            <?php while ($offer = $available_offers->fetch_assoc()): ?>
                                <option value="<?php echo $offer['id']; ?>">
                                    <?php echo htmlspecialchars($offer['title']); ?>
                                    <?php if ($offer['amount'] > 0): ?>
                                        - $<?php echo number_format($offer['amount'], 2); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endwhile; ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="position">الترتيب:</label>
                    <input type="number" id="position" name="position" value="1" min="1" max="99" required>
                </div>
                
                <div class="form-group">
                    <button type="submit" name="add_featured" class="btn btn-success">
                        ⭐ إضافة للمميزة
                    </button>
                </div>
            </form>
        </div>
        
        <!-- جميع العروض -->
        <div class="section">
            <h3>📋 جميع العروض المتاحة</h3>
            <p style="color: #666; margin-bottom: 1rem;">
                العروض المميزة تظهر بخلفية ذهبية، والعروض العادية بخلفية بيضاء
            </p>

            <?php if ($available_offers && $available_offers->num_rows > 0): ?>
                <?php
                // إعادة تعيين المؤشر لعرض العروض مرة أخرى
                $available_offers->data_seek(0);
                ?>
                <div class="available-grid">
                    <?php while ($offer = $available_offers->fetch_assoc()): ?>
                        <div class="available-card <?php echo $offer['is_featured'] ? 'featured-card-highlight' : ''; ?>">
                            <?php if ($offer['is_featured']): ?>
                                <div class="featured-badge">⭐ مميز #<?php echo $offer['featured_position']; ?></div>
                            <?php endif; ?>

                            <img src="<?php echo htmlspecialchars($offer['image_url']); ?>"
                                 alt="<?php echo htmlspecialchars($offer['title']); ?>"
                                 onerror="this.src='https://via.placeholder.com/250x100/667eea/ffffff?text=صورة'">

                            <h5 title="<?php echo htmlspecialchars($offer['title']); ?>">
                                <?php echo htmlspecialchars($offer['title']); ?>
                            </h5>

                            <?php if ($offer['amount'] > 0): ?>
                                <div class="amount">$<?php echo number_format($offer['amount'], 2); ?></div>
                            <?php endif; ?>

                            <?php if ($offer['is_featured']): ?>
                                <button onclick="selectOffer(<?php echo $offer['id']; ?>, '<?php echo htmlspecialchars($offer['title'], ENT_QUOTES); ?>')"
                                        class="btn btn-sm" style="width: 100%; background: #ffc107; color: #333;">
                                    🔄 تحديث الترتيب
                                </button>
                            <?php else: ?>
                                <button onclick="selectOffer(<?php echo $offer['id']; ?>, '<?php echo htmlspecialchars($offer['title'], ENT_QUOTES); ?>')"
                                        class="btn btn-sm" style="width: 100%;">
                                    ⭐ إضافة للمميزة
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endwhile; ?>
                </div>
            <?php else: ?>
                <div style="text-align: center; padding: 2rem; color: #666;">
                    <h4>لا توجد عروض</h4>
                    <p>لا توجد عروض نشطة في النظام.</p>
                    <a href="add_manual_offer.php" class="btn btn-success" style="margin-top: 1rem;">
                        ➕ إضافة عرض جديد
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="manage_offers.php">📋 إدارة العروض</a>
            <a href="add_manual_offer.php">➕ إضافة عرض جديد</a>
            <a href="api_manager.php">📡 لوحة التحكم</a>
        </div>
    </div>
    
    <script>
        function selectOffer(offerId, offerTitle) {
            document.getElementById('offer_id').value = offerId;

            // تمييز العرض المختار
            const cards = document.querySelectorAll('.available-card');
            cards.forEach(card => {
                if (card.classList.contains('featured-card-highlight')) {
                    card.style.border = '2px solid #ff9800';
                } else {
                    card.style.border = '1px solid #eee';
                }
            });

            const selectedCard = event.target.closest('.available-card');
            selectedCard.style.border = '3px solid #667eea';
            selectedCard.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';

            // التمرير لنموذج الإضافة
            document.querySelector('.add-form').scrollIntoView({ behavior: 'smooth' });

            // تركيز على حقل الترتيب
            setTimeout(() => {
                document.getElementById('position').focus();

                // إظهار رسالة تأكيد
                const confirmMsg = document.createElement('div');
                confirmMsg.innerHTML = `✅ تم اختيار العرض: ${offerTitle}`;
                confirmMsg.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 1000;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                `;
                document.body.appendChild(confirmMsg);

                setTimeout(() => {
                    confirmMsg.remove();
                }, 3000);
            }, 500);
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات hover للبطاقات
            const cards = document.querySelectorAll('.available-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('featured-card-highlight')) {
                        this.style.transform = 'translateY(-3px)';
                        this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.15)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('featured-card-highlight')) {
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                    }
                });
            });
        });
    </script>
</body>
</html>
