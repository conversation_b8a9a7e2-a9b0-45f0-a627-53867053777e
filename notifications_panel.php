<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';

// معالجة تعليم الإشعارات كمقروءة
if (isset($_POST['mark_read'])) {
    $notification_id = (int)$_POST['notification_id'];
    if (markNotificationAsRead($notification_id)) {
        $message = '<div class="alert alert-success">✅ تم تعليم الإشعار كمقروء!</div>';
    }
}

// معالجة تعليم جميع الإشعارات كمقروءة
if (isset($_POST['mark_all_read'])) {
    if (markAllNotificationsAsRead()) {
        $message = '<div class="alert alert-success">✅ تم تعليم جميع الإشعارات كمقروءة!</div>';
    }
}

// معالجة تنظيف الإشعارات القديمة
if (isset($_POST['clean_old'])) {
    $deleted_count = cleanOldNotifications();
    $message = '<div class="alert alert-success">✅ تم حذف ' . $deleted_count . ' إشعار قديم!</div>';
}

// الحصول على الإشعارات والإحصائيات
$notifications = getNotifications(50);
$stats = getNotificationsStats();
$unread_count = getUnreadNotificationsCount();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإشعارات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .cairo-time {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 1rem 0;
            display: inline-block;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .controls-section {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin: 0.25rem;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .notifications-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .notifications-header {
            background: #667eea;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .notification-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s;
            position: relative;
        }
        
        .notification-item:hover {
            background: #f8f9fa;
        }
        
        .notification-item.unread {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .notification-icon {
            font-size: 1.5rem;
            margin-left: 0.5rem;
        }
        
        .notification-title {
            font-weight: bold;
            color: #333;
            flex-grow: 1;
        }
        
        .notification-time {
            font-size: 0.8rem;
            color: #666;
        }
        
        .notification-message {
            color: #555;
            margin-bottom: 0.5rem;
        }
        
        .notification-data {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 3px;
            font-size: 0.9rem;
            color: #666;
        }
        
        .notification-actions {
            margin-top: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .empty-state img {
            width: 100px;
            opacity: 0.5;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .notification-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 لوحة الإشعارات</h1>
            <p>متابعة جميع الأنشطة والتحويلات في الوقت الفعلي</p>
            <div class="cairo-time">
                🕐 التوقيت المصري: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="offers_manager.php">📋 إدارة العروض</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات الإشعارات -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total']; ?></div>
                <div class="stats-label">📊 إجمالي الإشعارات</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" style="color: #dc3545;"><?php echo $stats['unread']; ?></div>
                <div class="stats-label">🔴 غير مقروءة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" style="color: #28a745;"><?php echo $stats['conversions']; ?></div>
                <div class="stats-label">💰 تحويلات</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" style="color: #007bff;"><?php echo $stats['clicks']; ?></div>
                <div class="stats-label">🎯 ضغطات</div>
            </div>
            <div class="stats-card">
                <div class="stats-number" style="color: #ffc107;"><?php echo $stats['today']; ?></div>
                <div class="stats-label">📅 اليوم</div>
            </div>
        </div>
        
        <!-- أدوات التحكم -->
        <div class="controls-section">
            <h3 style="margin-bottom: 1rem;">🛠️ أدوات التحكم</h3>
            
            <?php if ($unread_count > 0): ?>
            <form method="POST" style="display: inline;">
                <button type="submit" name="mark_all_read" class="btn btn-success">
                    ✅ تعليم الكل كمقروء (<?php echo $unread_count; ?>)
                </button>
            </form>
            <?php endif; ?>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف الإشعارات القديمة؟')">
                <button type="submit" name="clean_old" class="btn btn-danger">
                    🗑️ حذف الإشعارات القديمة (+30 يوم)
                </button>
            </form>
            
            <button onclick="location.reload()" class="btn">
                🔄 تحديث الصفحة
            </button>
        </div>
        
        <!-- قائمة الإشعارات -->
        <div class="notifications-container">
            <div class="notifications-header">
                <h3>🔔 الإشعارات (<?php echo count($notifications); ?>)</h3>
                <span>آخر تحديث: <?php echo date('H:i:s'); ?></span>
            </div>
            
            <?php if (empty($notifications)): ?>
                <div class="empty-state">
                    <div style="font-size: 3rem;">🔕</div>
                    <h3>لا توجد إشعارات</h3>
                    <p>ستظهر الإشعارات هنا عند حدوث أنشطة جديدة</p>
                </div>
            <?php else: ?>
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item <?php echo !$notification['is_read'] ? 'unread' : ''; ?>">
                        <div class="notification-header">
                            <div style="display: flex; align-items: center;">
                                <span class="notification-icon"><?php echo getNotificationIcon($notification['type']); ?></span>
                                <span class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></span>
                            </div>
                            <span class="notification-time"><?php echo formatCairoTime($notification['created_at']); ?></span>
                        </div>
                        
                        <div class="notification-message">
                            <?php echo htmlspecialchars($notification['message']); ?>
                        </div>
                        
                        <?php if ($notification['data']): ?>
                            <div class="notification-data">
                                <?php
                                $data = $notification['data'];
                                if (isset($data['offer_id'])) echo "🎯 العرض: #" . $data['offer_id'] . " ";
                                if (isset($data['username'])) echo "👤 المستخدم: " . htmlspecialchars($data['username']) . " ";
                                if (isset($data['ip_address'])) echo "🌐 IP: " . htmlspecialchars($data['ip_address']) . " ";
                                if (isset($data['payout']) && $data['payout'] > 0) echo "💰 العمولة: $" . number_format($data['payout'], 2) . " ";
                                if (isset($data['cairo_time'])) echo "🕐 " . $data['cairo_time'];
                                ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$notification['is_read']): ?>
                            <div class="notification-actions">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" name="mark_read" class="btn btn-sm btn-success">
                                        ✅ تعليم كمقروء
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            // تحديث الوقت فقط بدون إعادة تحميل الصفحة
            const timeElement = document.querySelector('.cairo-time');
            if (timeElement) {
                const now = new Date();
                const cairoTime = new Date(now.toLocaleString("en-US", {timeZone: "Africa/Cairo"}));
                const formatted = cairoTime.getFullYear() + '-' + 
                    String(cairoTime.getMonth() + 1).padStart(2, '0') + '-' + 
                    String(cairoTime.getDate()).padStart(2, '0') + ' ' +
                    String(cairoTime.getHours()).padStart(2, '0') + ':' + 
                    String(cairoTime.getMinutes()).padStart(2, '0') + ':' + 
                    String(cairoTime.getSeconds()).padStart(2, '0');
                timeElement.innerHTML = '🕐 التوقيت المصري: ' + formatted;
            }
        }, 1000);
        
        // تحديث الصفحة كل 5 دقائق للحصول على إشعارات جديدة
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
