<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? '';
    
    switch ($type) {
        case 'errors':
            $file = 'cpalead_errors.log';
            break;
        case 'conversions':
            $file = 'cpalead_conversions.log';
            break;
        default:
            echo 'نوع السجل غير صحيح';
            exit;
    }
    
    if (file_exists($file)) {
        if (unlink($file)) {
            echo 'تم مسح السجل بنجاح';
        } else {
            echo 'فشل في مسح السجل';
        }
    } else {
        echo 'السجل غير موجود';
    }
} else {
    echo 'طريقة الطلب غير صحيحة';
}
?>
