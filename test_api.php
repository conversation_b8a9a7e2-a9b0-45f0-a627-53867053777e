<?php
// ملف اختبار مبسط لـ CPALead API
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$api_id = isset($_GET['id']) ? $_GET['id'] : '1941213';
$apiUrl = "https://www.cpalead.com/api/offers?id=" . urlencode($api_id);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>اختبار CPALead API</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; padding: 2rem; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 1rem; border-radius: 5px; margin: 1rem 0; }";
echo "pre { background: #f8f9fa; padding: 1rem; border-radius: 5px; overflow-x: auto; max-height: 400px; overflow-y: auto; }";
echo ".form-group { margin: 1rem 0; }";
echo ".form-group input { padding: 0.5rem; border: 1px solid #ddd; border-radius: 3px; }";
echo ".btn { background: #007bff; color: white; padding: 0.5rem 1rem; text-decoration: none; border-radius: 3px; border: none; cursor: pointer; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🧪 اختبار CPALead API</h1>";

// نموذج تغيير معرف API
echo "<form method='GET'>";
echo "<div class='form-group'>";
echo "<label>معرف API:</label> ";
echo "<input type='text' name='id' value='" . htmlspecialchars($api_id) . "' placeholder='1941213'>";
echo " <button type='submit' class='btn'>اختبار</button>";
echo "</div>";
echo "</form>";

echo "<div class='info'><strong>رابط API:</strong> " . htmlspecialchars($apiUrl) . "</div>";

// إعداد السياق
$context = stream_context_create([
    'http' => [
        'timeout' => 15,
        'user_agent' => 'Mozilla/5.0 (compatible; CPA-Site-Test/1.0)',
        'method' => 'GET',
        'header' => [
            'Accept: application/json, text/plain, */*',
            'Content-Type: application/json'
        ]
    ]
]);

echo "<h3>📡 محاولة الاتصال بـ API...</h3>";

// محاولة الاتصال
$response = @file_get_contents($apiUrl, false, $context);

if ($response === false) {
    echo "<div class='error'>❌ فشل في الاتصال بـ API!</div>";
    
    // عرض معلومات إضافية عن الخطأ
    $error = error_get_last();
    if ($error) {
        echo "<div class='error'><strong>تفاصيل الخطأ:</strong><br>" . htmlspecialchars($error['message']) . "</div>";
    }
    
    // اختبار الاتصال بـ cURL كبديل
    if (function_exists('curl_init')) {
        echo "<h3>🔄 محاولة الاتصال باستخدام cURL...</h3>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; CPA-Site-Test/1.0)');
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $curlResponse = curl_exec($ch);
        $curlError = curl_error($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($curlResponse !== false && empty($curlError)) {
            echo "<div class='success'>✅ نجح الاتصال باستخدام cURL! (HTTP Code: $httpCode)</div>";
            $response = $curlResponse;
        } else {
            echo "<div class='error'>❌ فشل cURL أيضاً: " . htmlspecialchars($curlError) . " (HTTP Code: $httpCode)</div>";
        }
    }
} else {
    echo "<div class='success'>✅ نجح الاتصال بـ API!</div>";
}

if ($response !== false) {
    $rawData = trim($response);
    echo "<div class='info'><strong>حجم البيانات المُستقبلة:</strong> " . strlen($rawData) . " بايت</div>";
    
    // عرض أول 500 حرف من البيانات الخام
    echo "<h3>📄 البيانات الخام (أول 500 حرف):</h3>";
    echo "<pre>" . htmlspecialchars(substr($rawData, 0, 500)) . "</pre>";
    
    // محاولة تحليل JSON
    $data = json_decode($rawData, true);
    $jsonError = json_last_error();
    
    if ($jsonError === JSON_ERROR_NONE) {
        if (isset($data['status'])) {
            echo "<div class='success'>✅ تم تحليل JSON بنجاح!</div>";
            echo "<div class='info'><strong>حالة API:</strong> " . htmlspecialchars($data['status']) . "</div>";

            if ($data['status'] === 'success' && isset($data['offers'])) {
                $offers = $data['offers'];
                echo "<div class='success'>✅ تم العثور على " . count($offers) . " عرض</div>";

                // عرض معلومات الاستجابة
                echo "<h3>📋 معلومات الاستجابة:</h3>";
                echo "<div class='info'>";
                echo "<strong>عدد العروض:</strong> " . ($data['number_offers'] ?? count($offers)) . "<br>";
                echo "<strong>الدولة:</strong> " . ($data['country'] ?? 'غير محدد') . "<br>";
                echo "<strong>الأجهزة:</strong> " . ($data['devices'] ?? 'جميع الأجهزة') . "<br>";
                echo "</div>";

                if (!empty($offers)) {
                    // عرض هيكل أول عرض
                    $firstOffer = reset($offers);
                    echo "<h3>🔍 هيكل العرض:</h3>";
                    echo "<div class='info'><strong>الحقول المتاحة:</strong> " . implode(', ', array_keys($firstOffer)) . "</div>";
                    echo "<pre>" . htmlspecialchars(json_encode($firstOffer, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";

                    // تحليل العروض
                    echo "<h3>📊 تحليل العروض:</h3>";
                    $validOffers = 0;
                    $invalidOffers = 0;

                    foreach (array_slice($offers, 0, 10) as $index => $offer) {
                        $hasTitle = isset($offer['title']) && !empty($offer['title']);
                        $hasPreviewLink = isset($offer['preview_link']) && !empty($offer['preview_link']);
                        $hasTrackingLink = isset($offer['link']) && !empty($offer['link']);

                        if ($hasTitle && ($hasPreviewLink || $hasTrackingLink)) {
                            $validOffers++;
                        } else {
                            $invalidOffers++;
                            echo "<div class='error'>❌ العرض " . ($index + 1) . " - بيانات ناقصة (العنوان: " . ($hasTitle ? 'موجود' : 'مفقود') . ", الرابط: " . (($hasPreviewLink || $hasTrackingLink) ? 'موجود' : 'مفقود') . ")</div>";
                        }
                    }

                    echo "<div class='info'>";
                    echo "<strong>العروض الصحيحة:</strong> $validOffers<br>";
                    echo "<strong>العروض الناقصة:</strong> $invalidOffers<br>";
                    echo "<strong>إجمالي العروض المفحوصة:</strong> " . min(10, count($offers));
                    echo "</div>";

                    // عرض عينة من العروض الصحيحة
                    if ($validOffers > 0) {
                        echo "<h3>✅ عينة من العروض الصحيحة:</h3>";
                        $sampleCount = 0;
                        foreach (array_slice($offers, 0, 5) as $offer) {
                            $title = $offer['title'] ?? 'غير محدد';
                            $preview_link = $offer['preview_link'] ?? '';
                            $tracking_link = $offer['link'] ?? '';
                            $amount = $offer['amount'] ?? 0;
                            $device = $offer['device'] ?? 'غير محدد';
                            $payout_type = $offer['payout_type'] ?? 'غير محدد';

                            if ($title !== 'غير محدد' && (!empty($preview_link) || !empty($tracking_link))) {
                                $sampleCount++;
                                echo "<div class='success'>";
                                echo "<strong>العرض $sampleCount:</strong><br>";
                                echo "<strong>العنوان:</strong> " . htmlspecialchars($title) . "<br>";
                                echo "<strong>المبلغ:</strong> $" . $amount . " " . $payout_type . "<br>";
                                echo "<strong>الجهاز:</strong> " . htmlspecialchars($device) . "<br>";
                                echo "<strong>رابط المعاينة:</strong> " . htmlspecialchars($preview_link) . "<br>";
                                echo "<strong>رابط التتبع:</strong> " . htmlspecialchars($tracking_link);
                                echo "</div>";
                            }
                        }
                    }
                }
            } else {
                echo "<div class='error'>❌ فشل API أو لا توجد عروض متاحة</div>";
                if (isset($data['message'])) {
                    echo "<div class='error'><strong>رسالة API:</strong> " . htmlspecialchars($data['message']) . "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ تنسيق الاستجابة غير متوقع! نوع البيانات: " . gettype($data) . "</div>";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ خطأ في تحليل JSON: " . json_last_error_msg() . "</div>";
        
        // محاولة تحليل البيانات كـ XML أو HTML
        if (strpos($rawData, '<') !== false) {
            echo "<div class='info'>ℹ️ يبدو أن البيانات في تنسيق HTML/XML وليس JSON</div>";
        }
    }
}

echo "<div style='margin-top: 2rem; text-align: center;'>";
echo "<a href='api_manager.php' class='btn'>🔙 العودة لإدارة API</a> ";
echo "<a href='admin.php' class='btn'>🏠 لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
