<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة حذف اسم المستخدم
if (isset($_POST['delete_username']) && isset($_POST['username_id'])) {
    $username_id = (int)$_POST['username_id'];
    
    // حذف اسم المستخدم من saved_ips
    $deleteStmt = $conn->prepare("DELETE FROM saved_ips WHERE id = ?");
    $deleteStmt->bind_param("i", $username_id);
    
    if ($deleteStmt->execute()) {
        $message = '<div class="alert alert-success">✅ تم حذف اسم المستخدم بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في حذف اسم المستخدم: ' . $conn->error . '</div>';
    }
    $deleteStmt->close();
}

// معالجة تحديث اسم المستخدم
if (isset($_POST['update_username']) && isset($_POST['username_id']) && isset($_POST['new_username'])) {
    $username_id = (int)$_POST['username_id'];
    $new_username = trim($_POST['new_username']);
    
    if (!empty($new_username)) {
        // التحقق من عدم تكرار اسم المستخدم
        $checkStmt = $conn->prepare("SELECT id FROM saved_ips WHERE username = ? AND id != ?");
        $checkStmt->bind_param("si", $new_username, $username_id);
        $checkStmt->execute();
        
        if ($checkStmt->get_result()->num_rows == 0) {
            // تحديث اسم المستخدم
            $updateStmt = $conn->prepare("UPDATE saved_ips SET username = ? WHERE id = ?");
            $updateStmt->bind_param("si", $new_username, $username_id);
            
            if ($updateStmt->execute()) {
                // تحديث اسم المستخدم في جدول clicks
                $updateClicksStmt = $conn->prepare("UPDATE clicks SET username = ? WHERE ip_address = (SELECT ip_address FROM saved_ips WHERE id = ?)");
                $updateClicksStmt->bind_param("si", $new_username, $username_id);
                $updateClicksStmt->execute();
                $updateClicksStmt->close();
                
                // تحديث اسم المستخدم في جدول conversions
                $updateConversionsStmt = $conn->prepare("UPDATE conversions SET username = ? WHERE ip_address = (SELECT ip_address FROM saved_ips WHERE id = ?)");
                $updateConversionsStmt->bind_param("si", $new_username, $username_id);
                $updateConversionsStmt->execute();
                $updateConversionsStmt->close();
                
                $message = '<div class="alert alert-success">✅ تم تحديث اسم المستخدم بنجاح!</div>';
            } else {
                $message = '<div class="alert alert-error">❌ فشل في تحديث اسم المستخدم: ' . $conn->error . '</div>';
            }
            $updateStmt->close();
        } else {
            $message = '<div class="alert alert-error">❌ اسم المستخدم موجود مسبقاً!</div>';
        }
        $checkStmt->close();
    } else {
        $message = '<div class="alert alert-error">❌ اسم المستخدم لا يمكن أن يكون فارغاً!</div>';
    }
}

// الحصول على قائمة أسماء المستخدمين مع الإحصائيات
$usersQuery = "
    SELECT 
        s.id,
        s.ip_address,
        s.username,
        s.country,
        s.saved_at,
        COUNT(DISTINCT c.id) as clicks_count,
        COUNT(DISTINCT conv.id) as conversions_count,
        MAX(c.clicked_at) as last_click,
        MAX(conv.converted_at) as last_conversion
    FROM saved_ips s
    LEFT JOIN clicks c ON s.ip_address = c.ip_address
    LEFT JOIN conversions conv ON s.ip_address = conv.ip_address
    GROUP BY s.id, s.ip_address, s.username, s.country, s.saved_at
    ORDER BY s.saved_at DESC
";

$users = $conn->query($usersQuery);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة أسماء المستخدمين - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .users-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .users-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .username-cell {
            font-weight: bold;
            color: #667eea;
        }
        
        .stats-cell {
            font-size: 0.9rem;
        }
        
        .clicks-count {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .conversions-count {
            background: #ffc107;
            color: #333;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .actions-cell {
            white-space: nowrap;
        }
        
        .btn {
            padding: 0.3rem 0.8rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
            margin: 0 0.2rem;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-edit {
            background: #007bff;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn-save {
            background: #28a745;
            color: white;
        }
        
        .btn-cancel {
            background: #6c757d;
            color: white;
        }
        
        .edit-form {
            display: none;
        }
        
        .edit-input {
            padding: 0.3rem;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 150px;
        }
        
        .country-flag {
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .table-container {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 إدارة أسماء المستخدمين</h1>
            <p>إدارة وتحرير أسماء المستخدمين المُنشأة تلقائياً</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="username_guide.php">📚 دليل الاستخدام</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="users-table">
            <h3>📋 قائمة أسماء المستخدمين (<?php echo $users->num_rows; ?> مستخدم)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>عنوان IP</th>
                            <th>البلد</th>
                            <th>الضغطات</th>
                            <th>التحويلات</th>
                            <th>آخر نشاط</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($user = $users->fetch_assoc()): ?>
                            <tr id="row-<?php echo $user['id']; ?>">
                                <td class="username-cell">
                                    <span class="username-display"><?php echo htmlspecialchars($user['username']); ?></span>
                                    <form class="edit-form" style="display: none;" method="POST">
                                        <input type="hidden" name="username_id" value="<?php echo $user['id']; ?>">
                                        <input type="text" name="new_username" value="<?php echo htmlspecialchars($user['username']); ?>" class="edit-input" required>
                                        <button type="submit" name="update_username" class="btn btn-save">💾</button>
                                        <button type="button" class="btn btn-cancel" onclick="cancelEdit(<?php echo $user['id']; ?>)">❌</button>
                                    </form>
                                </td>
                                <td><?php echo htmlspecialchars($user['ip_address']); ?></td>
                                <td>
                                    <?php if ($user['country']): ?>
                                        <span class="country-flag"><?php echo getCountryName($user['country']); ?></span>
                                    <?php else: ?>
                                        غير محدد
                                    <?php endif; ?>
                                </td>
                                <td class="stats-cell">
                                    <?php if ($user['clicks_count'] > 0): ?>
                                        <span class="clicks-count"><?php echo $user['clicks_count']; ?> ضغطة</span>
                                    <?php else: ?>
                                        لا توجد
                                    <?php endif; ?>
                                </td>
                                <td class="stats-cell">
                                    <?php if ($user['conversions_count'] > 0): ?>
                                        <span class="conversions-count"><?php echo $user['conversions_count']; ?> تحويل</span>
                                    <?php else: ?>
                                        لا توجد
                                    <?php endif; ?>
                                </td>
                                <td class="stats-cell">
                                    <?php 
                                    $lastActivity = '';
                                    if ($user['last_conversion']) {
                                        $lastActivity = 'تحويل: ' . date('Y-m-d', strtotime($user['last_conversion']));
                                    } elseif ($user['last_click']) {
                                        $lastActivity = 'ضغطة: ' . date('Y-m-d', strtotime($user['last_click']));
                                    } else {
                                        $lastActivity = 'لا يوجد نشاط';
                                    }
                                    echo $lastActivity;
                                    ?>
                                </td>
                                <td><?php echo date('Y-m-d H:i', strtotime($user['saved_at'])); ?></td>
                                <td class="actions-cell">
                                    <button class="btn btn-edit" onclick="editUsername(<?php echo $user['id']; ?>)">✏️ تعديل</button>
                                    <form style="display: inline;" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                        <input type="hidden" name="username_id" value="<?php echo $user['id']; ?>">
                                        <button type="submit" name="delete_username" class="btn btn-delete">🗑️ حذف</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        function editUsername(userId) {
            const row = document.getElementById('row-' + userId);
            const displaySpan = row.querySelector('.username-display');
            const editForm = row.querySelector('.edit-form');
            
            displaySpan.style.display = 'none';
            editForm.style.display = 'inline';
            editForm.querySelector('input[name="new_username"]').focus();
        }
        
        function cancelEdit(userId) {
            const row = document.getElementById('row-' + userId);
            const displaySpan = row.querySelector('.username-display');
            const editForm = row.querySelector('.edit-form');
            
            displaySpan.style.display = 'inline';
            editForm.style.display = 'none';
        }
    </script>
</body>
</html>

<?php $conn->close(); ?>
