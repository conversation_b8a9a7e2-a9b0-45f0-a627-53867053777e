<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$test_results = [];

// اختبار نظام إدارة العروض
if (isset($_POST['test_offers_system'])) {
    $tests = [];
    
    try {
        $conn = getDBConnection();
        
        // اختبار 1: التحقق من جدول العروض
        $result = $conn->query("SHOW TABLES LIKE 'offers'");
        if ($result->num_rows > 0) {
            $tests[] = ['name' => 'جدول العروض', 'status' => 'success', 'message' => 'موجود'];
            
            // التحقق من الحقول المطلوبة
            $columns = $conn->query("SHOW COLUMNS FROM offers");
            $column_names = [];
            while ($col = $columns->fetch_assoc()) {
                $column_names[] = $col['Field'];
            }
            
            $required_columns = ['id', 'title', 'offer_url', 'image_url', 'is_active', 'external_id'];
            $missing_columns = array_diff($required_columns, $column_names);
            
            if (empty($missing_columns)) {
                $tests[] = ['name' => 'حقول جدول العروض', 'status' => 'success', 'message' => 'جميع الحقول موجودة'];
            } else {
                $tests[] = ['name' => 'حقول جدول العروض', 'status' => 'warning', 'message' => 'حقول مفقودة: ' . implode(', ', $missing_columns)];
            }
        } else {
            $tests[] = ['name' => 'جدول العروض', 'status' => 'error', 'message' => 'غير موجود'];
        }
        
        // اختبار 2: التحقق من جدول الإعدادات
        $result = $conn->query("SHOW TABLES LIKE 'settings'");
        if ($result->num_rows > 0) {
            $tests[] = ['name' => 'جدول الإعدادات', 'status' => 'success', 'message' => 'موجود'];
            
            // التحقق من إعداد وضع العروض
            $mode_result = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'offers_mode'");
            if ($mode_result && $mode_result->num_rows > 0) {
                $mode = $mode_result->fetch_assoc()['setting_value'];
                $tests[] = ['name' => 'وضع العروض', 'status' => 'success', 'message' => "الوضع الحالي: $mode"];
            } else {
                $tests[] = ['name' => 'وضع العروض', 'status' => 'warning', 'message' => 'لم يتم تعيين الوضع (سيستخدم التلقائي)'];
            }
        } else {
            $tests[] = ['name' => 'جدول الإعدادات', 'status' => 'warning', 'message' => 'غير موجود - سيتم إنشاؤه تلقائياً'];
        }
        
        // اختبار 3: عدد العروض
        $offers_count = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
        $active_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
        $manual_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id LIKE 'manual_%'")->fetch_assoc()['count'];
        
        $tests[] = ['name' => 'إجمالي العروض', 'status' => 'info', 'message' => "$offers_count عرض"];
        $tests[] = ['name' => 'العروض النشطة', 'status' => 'info', 'message' => "$active_offers عرض نشط"];
        $tests[] = ['name' => 'العروض اليدوية', 'status' => 'info', 'message' => "$manual_offers عرض يدوي"];
        
        // اختبار 4: الدوال المطلوبة
        // تضمين الدوال أولاً
        if (file_exists('safe_description_functions.php')) {
            require_once 'safe_description_functions.php';
        }

        $required_functions = ['safeInsertOffer', 'hasDescriptionField', 'ensureDescriptionField'];
        foreach ($required_functions as $func) {
            if (function_exists($func)) {
                $tests[] = ['name' => "دالة $func", 'status' => 'success', 'message' => 'متاحة'];
            } else {
                $tests[] = ['name' => "دالة $func", 'status' => 'error', 'message' => 'غير متاحة'];
            }
        }
        
        // اختبار 5: الملفات المطلوبة
        $required_files = ['offers_management.php', 'safe_description_functions.php'];
        foreach ($required_files as $file) {
            if (file_exists($file)) {
                $tests[] = ['name' => "ملف $file", 'status' => 'success', 'message' => 'موجود'];
            } else {
                $tests[] = ['name' => "ملف $file", 'status' => 'error', 'message' => 'غير موجود'];
            }
        }
        
        $conn->close();
        $test_results = $tests;
        $message = "✅ تم إجراء " . count($tests) . " اختبار!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في الاختبار: " . $e->getMessage();
    }
}

// إنشاء عرض تجريبي
if (isset($_POST['create_test_offer'])) {
    try {
        // تضمين الدوال المطلوبة
        if (file_exists('safe_description_functions.php')) {
            require_once 'safe_description_functions.php';
        }

        // التأكد من وجود الدالة
        if (!function_exists('safeInsertOffer')) {
            // إنشاء الدالة محلياً إذا لم تكن موجودة
            function safeInsertOffer($title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active = 1, $external_id = null) {
                $conn = getDBConnection();

                // التحقق من وجود حقل external_id
                $has_external_id = false;
                $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'external_id'");
                if ($result->num_rows > 0) {
                    $has_external_id = true;
                }

                // التحقق من وجود حقل description
                $has_description = false;
                $result = $conn->query("SHOW COLUMNS FROM offers LIKE 'description'");
                if ($result->num_rows > 0) {
                    $has_description = true;
                }

                if ($has_description && $has_external_id) {
                    $stmt = $conn->prepare("
                        INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param("ssssssdsis", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $external_id);
                } elseif ($has_description) {
                    $stmt = $conn->prepare("
                        INSERT INTO offers (title, description, image_url, offer_url, countries, device, amount, payout_type, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param("ssssssdsi", $title, $description, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
                } elseif ($has_external_id) {
                    $stmt = $conn->prepare("
                        INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active, external_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param("sssssdsiss", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active, $external_id);
                } else {
                    $stmt = $conn->prepare("
                        INSERT INTO offers (title, image_url, offer_url, countries, device, amount, payout_type, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->bind_param("sssssdsi", $title, $image_url, $offer_url, $countries, $device, $amount, $payout_type, $is_active);
                }

                $result = $stmt->execute();
                $insert_id = $conn->insert_id;
                $stmt->close();
                $conn->close();

                return $result ? $insert_id : false;
            }
        }

        $test_title = "عرض تجريبي - " . date('Y-m-d H:i:s');
        $test_description = "هذا عرض تجريبي لاختبار نظام إدارة العروض. يمكنك حذفه بعد التأكد من عمل النظام.";
        $test_image = "https://via.placeholder.com/400x250/28a745/ffffff?text=عرض+تجريبي";
        $test_url = "https://example.com/test-offer";

        $insert_id = safeInsertOffer(
            $test_title,
            $test_description,
            $test_image,
            $test_url,
            'all',
            'all',
            5.00,
            'CPI',
            1,
            'manual_test_' . time()
        );

        if ($insert_id) {
            $message = "✅ تم إنشاء عرض تجريبي بنجاح! معرف العرض: $insert_id";
        } else {
            $message = "❌ فشل في إنشاء العرض التجريبي!";
        }

    } catch (Exception $e) {
        $message = "❌ خطأ في إنشاء العرض التجريبي: " . $e->getMessage();
    }
}

// حذف العروض التجريبية
if (isset($_POST['delete_test_offers'])) {
    try {
        $conn = getDBConnection();
        $result = $conn->query("DELETE FROM offers WHERE external_id LIKE 'manual_test_%'");
        $deleted_count = $conn->affected_rows;
        $conn->close();
        
        $message = "✅ تم حذف $deleted_count عرض تجريبي!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في حذف العروض التجريبية: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .test-card {
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
        }
        
        .test-card.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        
        .test-card.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        
        .test-card.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        
        .test-card.info {
            background: #e3f2fd;
            border-left-color: #2196f3;
        }
        
        .test-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .test-status {
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }
        
        .test-message {
            color: #666;
            font-size: 0.9rem;
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام إدارة العروض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 حول نظام إدارة العروض:</h3>
            <ul style="margin-right: 1.5rem;">
                <li><strong>الوضع اليدوي:</strong> إضافة وإدارة العروض يدوياً</li>
                <li><strong>الوضع التلقائي:</strong> العروض تأتي من المزامنة مع CPALead</li>
                <li><strong>التبديل:</strong> يمكن التبديل بين الوضعين في أي وقت</li>
                <li><strong>الفلترة:</strong> الصفحة الرئيسية تعرض العروض حسب الوضع المحدد</li>
            </ul>
        </div>
        
        <div class="action-section">
            <h3>🛠️ أدوات الاختبار</h3>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="test_offers_system" class="btn btn-success">
                    🧪 اختبار النظام
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_test_offer" class="btn">
                    ➕ إنشاء عرض تجريبي
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="delete_test_offers" class="btn btn-danger"
                        onclick="return confirm('هل أنت متأكد من حذف جميع العروض التجريبية؟')">
                    🗑️ حذف العروض التجريبية
                </button>
            </form>
        </div>
        
        <?php if (!empty($test_results)): ?>
        <div>
            <h3 style="color: #333; margin-bottom: 1rem;">📊 نتائج الاختبار</h3>
            
            <div class="tests-grid">
                <?php foreach ($test_results as $test): ?>
                    <div class="test-card <?php echo $test['status']; ?>">
                        <div class="test-name">
                            <?php echo htmlspecialchars($test['name']); ?>
                        </div>
                        <div class="test-status">
                            <?php
                            switch ($test['status']) {
                                case 'success':
                                    echo '✅ نجح';
                                    break;
                                case 'error':
                                    echo '❌ فشل';
                                    break;
                                case 'warning':
                                    echo '⚠️ تحذير';
                                    break;
                                case 'info':
                                    echo 'ℹ️ معلومات';
                                    break;
                                default:
                                    echo '❓ غير معروف';
                            }
                            ?>
                        </div>
                        <div class="test-message">
                            <?php echo htmlspecialchars($test['message']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="offers_management.php">📋 إدارة العروض</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="api_manager.php">📡 إدارة API</a>
        </div>
    </div>
</body>
</html>
