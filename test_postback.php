<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة اختبار Postback
if (isset($_POST['test_postback'])) {
    $offer_id = (int)$_POST['offer_id'];
    $test_ip = trim($_POST['test_ip']);
    $source = trim($_POST['source']) ?: 'test';
    
    if ($offer_id && $test_ip) {
        // محاكاة Postback
        $postback_url = "postback.php?offer_id=$offer_id&ip=$test_ip&source=$source";
        
        // تنفيذ Postback محلياً
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/$postback_url");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200) {
            $message = '<div class="alert alert-success">✅ تم اختبار Postback بنجاح!<br><strong>الاستجابة:</strong> ' . htmlspecialchars($response) . '</div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل اختبار Postback. كود الخطأ: ' . $http_code . '</div>';
        }
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال جميع البيانات المطلوبة!</div>';
    }
}

// الحصول على قائمة العروض للاختبار
$offers = $conn->query("SELECT id, title FROM offers ORDER BY id DESC LIMIT 10");

// الحصول على آخر التحويلات
$recent_conversions = $conn->query("
    SELECT c.*, o.title as offer_title 
    FROM conversions c 
    LEFT JOIN offers o ON c.offer_id = o.id 
    ORDER BY c.converted_at DESC 
    LIMIT 5
");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Postback - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .conversions-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .conversions-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        code {
            background: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار Postback</h1>
            <p>اختبار عمل Postback مع CPALead والتأكد من ربط أسماء المستخدمين</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="cpalead_integration.php">🔗 ربط CPALead</a>
                <a href="conversions.php">📊 التحويلات</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- نموذج اختبار Postback -->
        <div class="form-section">
            <h3>🧪 اختبار Postback</h3>
            
            <div class="info-box">
                <h4>📋 كيفية الاختبار:</h4>
                <ul>
                    <li>اختر عرض من القائمة</li>
                    <li>أدخل عنوان IP للاختبار</li>
                    <li>اضغط "اختبار Postback"</li>
                    <li>تحقق من النتيجة والتحويلات</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="offer_id">🎯 اختر العرض:</label>
                    <select id="offer_id" name="offer_id" required>
                        <option value="">-- اختر عرض --</option>
                        <?php while ($offer = $offers->fetch_assoc()): ?>
                            <option value="<?php echo $offer['id']; ?>">
                                العرض #<?php echo $offer['id']; ?> - <?php echo htmlspecialchars(substr($offer['title'], 0, 50)); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="test_ip">🌐 عنوان IP للاختبار:</label>
                    <input type="text" id="test_ip" name="test_ip" value="*************" placeholder="*************" required>
                </div>
                
                <div class="form-group">
                    <label for="source">📡 مصدر التحويل:</label>
                    <input type="text" id="source" name="source" value="cpalead_test" placeholder="cpalead_test">
                </div>
                
                <button type="submit" name="test_postback" class="btn">🧪 اختبار Postback</button>
            </form>
        </div>
        
        <!-- معلومات Postback -->
        <div class="form-section">
            <h3>📡 معلومات Postback</h3>
            
            <div class="alert alert-info">
                <h4>🔗 رابط Postback لـ CPALead:</h4>
                <code>https://yoursite.com/postback.php?offer_id={subid}&ip={user_ip}&source=cpalead</code>
            </div>
            
            <div class="info-box">
                <h4>📋 معاملات Postback المدعومة:</h4>
                <ul>
                    <li><code>offer_id</code> أو <code>subid</code> - معرف العرض</li>
                    <li><code>ip</code> أو <code>user_ip</code> - عنوان IP المستخدم</li>
                    <li><code>source</code> - مصدر التحويل (اختياري)</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>🎯 كيف يعمل ربط اسم المستخدم:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li>البحث عن اسم المستخدم من الضغطات السابقة</li>
                    <li>إذا لم يوجد، البحث في الـ IPs المحفوظة</li>
                    <li>إذا لم يوجد، إنشاء اسم مستخدم جديد</li>
                    <li>ربط التحويل باسم المستخدم</li>
                </ol>
            </div>
        </div>
        
        <!-- آخر التحويلات -->
        <?php if ($recent_conversions->num_rows > 0): ?>
        <div class="conversions-table">
            <h3>📊 آخر التحويلات</h3>
            <table>
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>اسم المستخدم</th>
                        <th>عنوان IP</th>
                        <th>العرض</th>
                        <th>المصدر</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($conversion = $recent_conversions->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $conversion['id']; ?></td>
                            <td style="font-weight: bold; color: #667eea;">
                                <?php echo $conversion['username'] ? htmlspecialchars($conversion['username']) : 'غير محدد'; ?>
                            </td>
                            <td><?php echo htmlspecialchars($conversion['ip_address']); ?></td>
                            <td><?php echo htmlspecialchars(substr($conversion['offer_title'], 0, 30)); ?>...</td>
                            <td>
                                <span style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                    <?php echo $conversion['source'] ? htmlspecialchars($conversion['source']) : 'مباشر'; ?>
                                </span>
                            </td>
                            <td><?php echo date('Y-m-d H:i', strtotime($conversion['converted_at'])); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

<?php $conn->close(); ?>
