<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$fixed_count = 0;

if (isset($_POST['fix_times'])) {
    try {
        $conn = getDBConnection();
        
        // الحصول على جميع الإشعارات
        $result = $conn->query("SELECT id, created_at FROM notifications ORDER BY created_at DESC");
        
        while ($notification = $result->fetch_assoc()) {
            $id = $notification['id'];
            $created_at = $notification['created_at'];
            
            // تحديث الوقت ليكون بالتوقيت المصري
            $cairo_time = new DateTime($created_at);
            $cairo_time->setTimezone(new DateTimeZone('Africa/Cairo'));
            
            // تحديث السجل في قاعدة البيانات
            $update_stmt = $conn->prepare("UPDATE notifications SET created_at = ? WHERE id = ?");
            $formatted_time = $cairo_time->format('Y-m-d H:i:s');
            $update_stmt->bind_param("si", $formatted_time, $id);
            
            if ($update_stmt->execute()) {
                $fixed_count++;
            }
            
            $update_stmt->close();
        }
        
        $conn->close();
        
        $message = "✅ تم إصلاح أوقات {$fixed_count} إشعار بنجاح!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إصلاح الأوقات: " . $e->getMessage();
    }
}

// إنشاء إشعار تجريبي للاختبار
if (isset($_POST['create_test'])) {
    try {
        // إنشاء إشعار تجريبي بالوقت الحالي
        addNotification('test', 'اختبار الوقت', 'إشعار تجريبي لاختبار تنسيق الوقت', [
            'test' => true,
            'created_time' => date('Y-m-d H:i:s')
        ]);
        
        $message = "✅ تم إنشاء إشعار تجريبي للاختبار!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إنشاء الإشعار التجريبي: " . $e->getMessage();
    }
}

// حذف الإشعارات التجريبية
if (isset($_POST['delete_test'])) {
    try {
        $conn = getDBConnection();
        $stmt = $conn->prepare("DELETE FROM notifications WHERE type = 'test'");
        $stmt->execute();
        $deleted = $stmt->affected_rows;
        $stmt->close();
        $conn->close();
        
        $message = "✅ تم حذف {$deleted} إشعار تجريبي!";
        
    } catch (Exception $e) {
        $message = "❌ خطأ في حذف الإشعارات التجريبية: " . $e->getMessage();
    }
}

// الحصول على عينة من الإشعارات لعرض الأوقات
$notifications = [];
try {
    $conn = getDBConnection();
    $result = $conn->query("SELECT id, type, title, message, created_at FROM notifications ORDER BY created_at DESC LIMIT 5");
    
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
    
    $conn->close();
} catch (Exception $e) {
    $message = "❌ خطأ في جلب الإشعارات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أوقات الإشعارات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .action-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .action-section h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            margin: 0.5rem;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .notifications-preview {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .notifications-preview h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .notification-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .notification-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .notification-time {
            color: #666;
            font-size: 0.9rem;
        }
        
        .notification-raw-time {
            color: #999;
            font-size: 0.8rem;
            margin-top: 0.3rem;
        }
        
        .current-time {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 إصلاح أوقات الإشعارات</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="current-time">
            <strong>⏰ الوقت الحالي (توقيت القاهرة):</strong><br>
            <?php 
            $now = new DateTime('now', new DateTimeZone('Africa/Cairo'));
            echo $now->format('Y-m-d g:i:s A'); 
            ?>
        </div>
        
        <div class="action-section">
            <h3>🔧 إجراءات الإصلاح</h3>
            <p style="margin-bottom: 1rem; color: #666;">
                استخدم هذه الأدوات لإصلاح مشاكل أوقات الإشعارات
            </p>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="fix_times" class="btn btn-success">
                    🔧 إصلاح جميع الأوقات
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="create_test" class="btn btn-warning">
                    🧪 إنشاء إشعار تجريبي
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="delete_test" class="btn btn-danger">
                    🗑️ حذف الإشعارات التجريبية
                </button>
            </form>
        </div>
        
        <?php if (!empty($notifications)): ?>
        <div class="notifications-preview">
            <h3>📋 معاينة الإشعارات الحديثة</h3>
            
            <?php foreach ($notifications as $notification): ?>
                <div class="notification-item">
                    <div class="notification-title">
                        <?php echo htmlspecialchars($notification['title']); ?>
                    </div>
                    <div class="notification-time">
                        <strong>الوقت المنسق:</strong> <?php echo formatCairoTime($notification['created_at']); ?>
                    </div>
                    <div class="notification-raw-time">
                        <strong>الوقت الخام:</strong> <?php echo $notification['created_at']; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="notifications_panel.php">📢 لوحة الإشعارات</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="api_manager.php">🔧 إدارة API</a>
        </div>
    </div>
</body>
</html>
