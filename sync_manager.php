<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة إضافة معرف API
if (isset($_POST['add_api_id'])) {
    $api_id = trim($_POST['api_id']);
    
    if (!empty($api_id)) {
        $apiFile = 'api_ids.txt';
        $existingIds = [];
        
        if (file_exists($apiFile)) {
            $existingIds = array_filter(array_map('trim', file($apiFile)));
        }
        
        if (!in_array($api_id, $existingIds)) {
            file_put_contents($apiFile, $api_id . "\n", FILE_APPEND | LOCK_EX);
            $message = '<div class="alert alert-success">✅ تم إضافة معرف API بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-warning">⚠️ معرف API موجود مسبقاً!</div>';
        }
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال معرف API!</div>';
    }
}

// معالجة حذف معرف API
if (isset($_POST['remove_api_id'])) {
    $api_id = trim($_POST['api_id_to_remove']);
    
    if (!empty($api_id)) {
        $apiFile = 'api_ids.txt';
        
        if (file_exists($apiFile)) {
            $existingIds = array_filter(array_map('trim', file($apiFile)));
            $newIds = array_filter($existingIds, function($id) use ($api_id) {
                return $id !== $api_id;
            });
            
            file_put_contents($apiFile, implode("\n", $newIds) . "\n");
            $message = '<div class="alert alert-success">✅ تم حذف معرف API بنجاح!</div>';
        }
    }
}

// معالجة تشغيل المزامنة اليدوية
if (isset($_POST['manual_sync'])) {
    $output = [];
    $return_var = 0;
    
    // تشغيل المزامنة
    exec("php auto_sync_offers.php 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        $message = '<div class="alert alert-success">✅ تم تشغيل المزامنة بنجاح!</div>';
        $message .= '<div class="alert alert-info"><strong>النتائج:</strong><br>' . implode('<br>', $output) . '</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في تشغيل المزامنة!</div>';
    }
}

// الحصول على قائمة معرفات API
$apiIds = [];
$apiFile = 'api_ids.txt';
if (file_exists($apiFile)) {
    $apiIds = array_filter(array_map('trim', file($apiFile)));
}

// الحصول على إحصائيات العروض
$stats = $conn->query("
    SELECT 
        COUNT(*) as total_offers,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_offers,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_offers,
        MAX(created_at) as last_added,
        MAX(updated_at) as last_updated
    FROM offers
")->fetch_assoc();

// الحصول على آخر العروض المضافة
$recent_offers = $conn->query("
    SELECT title, created_at, is_active 
    FROM offers 
    ORDER BY created_at DESC 
    LIMIT 10
");

// قراءة آخر سجلات المزامنة
$syncLog = '';
$logFile = 'auto_sync.log';
if (file_exists($logFile)) {
    $lines = file($logFile);
    $syncLog = implode('', array_slice($lines, -20)); // آخر 20 سطر
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المزامنة التلقائية - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin: 0.25rem;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .api-list {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        
        .api-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin: 0.5rem 0;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #667eea;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-active {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .status-inactive {
            background: #dc3545;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 إدارة المزامنة التلقائية</h1>
            <p>إضافة العروض الجديدة تلقائياً وإخفاء العروض المتوقفة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="offers_manager.php">📋 إدارة العروض</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total_offers']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['active_offers']; ?></div>
                <div class="stats-label">✅ عروض نشطة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['inactive_offers']; ?></div>
                <div class="stats-label">❌ عروض متوقفة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo count($apiIds); ?></div>
                <div class="stats-label">🔌 معرفات API</div>
            </div>
        </div>
        
        <!-- إدارة معرفات API -->
        <div class="form-section">
            <h3>🔌 إدارة معرفات API</h3>
            
            <form method="POST" style="margin-bottom: 1rem;">
                <div class="form-group">
                    <label for="api_id">إضافة معرف API جديد:</label>
                    <input type="text" id="api_id" name="api_id" placeholder="مثال: 1941213" required>
                </div>
                <button type="submit" name="add_api_id" class="btn">➕ إضافة معرف API</button>
            </form>
            
            <?php if (!empty($apiIds)): ?>
            <div class="api-list">
                <h4>📋 معرفات API المحفوظة:</h4>
                <?php foreach ($apiIds as $id): ?>
                <div class="api-item">
                    <span><strong>API ID:</strong> <?php echo htmlspecialchars($id); ?></span>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="api_id_to_remove" value="<?php echo htmlspecialchars($id); ?>">
                        <button type="submit" name="remove_api_id" class="btn btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المعرف؟')">🗑️ حذف</button>
                    </form>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- تشغيل المزامنة -->
        <div class="form-section">
            <h3>🔄 تشغيل المزامنة</h3>
            
            <div class="alert alert-info">
                <h4>📋 ما تفعله المزامنة:</h4>
                <ul style="margin-right: 1.5rem;">
                    <li>🆕 إضافة العروض الجديدة من CPALead API</li>
                    <li>✅ تفعيل العروض المتوقفة إذا عادت للعمل</li>
                    <li>❌ إيقاف العروض المتوقفة أو المنتهية</li>
                    <li>📊 تحديث معلومات العروض الموجودة</li>
                </ul>
            </div>
            
            <form method="POST">
                <button type="submit" name="manual_sync" class="btn btn-success">🔄 تشغيل المزامنة الآن</button>
            </form>
            
            <div style="margin-top: 1rem;">
                <h4>⏰ إعداد المزامنة التلقائية (Cron Job):</h4>
                <div class="log-container">
# إضافة هذا السطر إلى Cron Jobs لتشغيل المزامنة كل 6 ساعات:
0 */6 * * * /usr/bin/php <?php echo realpath('auto_sync_offers.php'); ?>

# أو كل يوم في الساعة 2:00 صباحاً:
0 2 * * * /usr/bin/php <?php echo realpath('auto_sync_offers.php'); ?>
                </div>
            </div>
        </div>
        
        <!-- سجل المزامنة -->
        <?php if (!empty($syncLog)): ?>
        <div class="form-section">
            <h3>📋 سجل المزامنة</h3>
            <div class="log-container"><?php echo htmlspecialchars($syncLog); ?></div>
        </div>
        <?php endif; ?>
        
        <!-- آخر العروض المضافة -->
        <?php if ($recent_offers->num_rows > 0): ?>
        <div class="offers-table">
            <h3>📊 آخر العروض المضافة</h3>
            <table>
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($offer = $recent_offers->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo htmlspecialchars(substr($offer['title'], 0, 60)); ?>...</td>
                            <td>
                                <?php if ($offer['is_active']): ?>
                                    <span class="status-active">نشط</span>
                                <?php else: ?>
                                    <span class="status-inactive">متوقف</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>

<?php $conn->close(); ?>
