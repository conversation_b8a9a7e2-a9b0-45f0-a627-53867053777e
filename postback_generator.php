<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();

// الحصول على جميع العروض
$offers = $conn->query("SELECT id, title FROM offers ORDER BY id DESC");

// الحصول على الدومين الحالي
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$domain = $protocol . '://' . $_SERVER['HTTP_HOST'];
$current_path = dirname($_SERVER['REQUEST_URI']);
$base_url = $domain . $current_path;

$generated_links = [];
$message = '';

// معالجة إنشاء روابط Postback
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_postback'])) {
    $selected_offers = $_POST['offers'] ?? [];
    $custom_params = $_POST['custom_params'] ?? '';
    $test_ip = $_POST['test_ip'] ?? '***********';
    
    if (!empty($selected_offers)) {
        foreach ($selected_offers as $offer_id) {
            $stmt = $conn->prepare("SELECT id, title FROM offers WHERE id = ?");
            $stmt->bind_param("i", $offer_id);
            $stmt->execute();
            $offer = $stmt->get_result()->fetch_assoc();
            $stmt->close();
            
            if ($offer) {
                // إنشاء روابط مختلفة
                $links = [
                    'basic' => $base_url . "/postback.php?offer_id={$offer_id}&ip={IP}",
                    'subid' => $base_url . "/postback.php?subid={$offer_id}&user_ip={IP}",
                    'with_params' => $base_url . "/postback.php?offer_id={$offer_id}&ip={IP}" . ($custom_params ? "&{$custom_params}" : ""),
                    'test_link' => $base_url . "/postback.php?offer_id={$offer_id}&ip={$test_ip}"
                ];
                
                $generated_links[] = [
                    'offer' => $offer,
                    'links' => $links
                ];
            }
        }
        
        $message = '<div class="alert alert-success">✅ تم إنشاء روابط Postback بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-error">❌ يرجى اختيار عرض واحد على الأقل!</div>';
    }
}

// اختبار رابط Postback
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_postback'])) {
    $test_url = $_POST['test_url'] ?? '';
    
    if ($test_url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'CPA-Site-Postback-Tester/1.0'
            ]
        ]);
        
        $response = @file_get_contents($test_url, false, $context);
        
        if ($response !== false) {
            $message = '<div class="alert alert-success">✅ تم اختبار Postback بنجاح!<br><strong>الاستجابة:</strong><br><pre>' . htmlspecialchars($response) . '</pre></div>';
        } else {
            $message = '<div class="alert alert-error">❌ فشل في اختبار Postback! تحقق من الرابط.</div>';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد روابط Postback - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #555;
            font-weight: bold;
        }
        
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group textarea:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-left: 0.5rem;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .generated-links {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .generated-links h2 {
            padding: 1.5rem;
            background: #f8f9fa;
            color: #333;
            margin: 0;
        }
        
        .offer-links {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .offer-links:last-child {
            border-bottom: none;
        }
        
        .offer-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .link-item {
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .link-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 0.5rem;
        }
        
        .link-url {
            font-family: monospace;
            background: white;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 3px;
            word-break: break-all;
            font-size: 0.9rem;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            cursor: pointer;
            margin-top: 0.5rem;
        }
        
        .copy-btn:hover {
            background: #138496;
        }
        
        .info-box {
            background: #d1ecf1;
            color: #0c5460;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .info-box h4 {
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 مولد روابط Postback</h1>
            <p>إنشاء واختبار روابط Postback بسهولة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="conversions.php">📊 التحويلات</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="api_manager.php">🔌 إدارة API</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <div class="info-box">
            <h4>📋 معلومات مهمة:</h4>
            <ul>
                <li><strong>{IP}</strong> - سيتم استبداله بعنوان IP الفعلي من شبكة CPA</li>
                <li><strong>offer_id/subid</strong> - معرف العرض</li>
                <li><strong>ip/user_ip</strong> - عنوان IP المستخدم</li>
                <li>يمكن إضافة معاملات مخصصة حسب متطلبات الشبكة</li>
            </ul>
        </div>
        
        <div class="form-section">
            <h2>🎯 إنشاء روابط Postback</h2>
            <form method="POST">
                <div class="form-group">
                    <label>اختر العروض:</label>
                    <div class="checkbox-group">
                        <?php if ($offers->num_rows > 0): ?>
                            <?php while ($offer = $offers->fetch_assoc()): ?>
                                <div class="checkbox-item">
                                    <input type="checkbox" name="offers[]" value="<?php echo $offer['id']; ?>" id="offer_<?php echo $offer['id']; ?>">
                                    <label for="offer_<?php echo $offer['id']; ?>"><?php echo htmlspecialchars($offer['title']); ?></label>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <p>لا توجد عروض متاحة</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="custom_params">معاملات مخصصة (اختياري):</label>
                    <input type="text" id="custom_params" name="custom_params" placeholder="مثال: source=facebook&campaign=test">
                </div>
                
                <div class="form-group">
                    <label for="test_ip">IP للاختبار:</label>
                    <input type="text" id="test_ip" name="test_ip" value="***********">
                </div>
                
                <button type="submit" name="generate_postback" class="btn">🔗 إنشاء روابط Postback</button>
            </form>
        </div>
        
        <?php if (!empty($generated_links)): ?>
            <div class="generated-links">
                <h2>📋 روابط Postback المُنشأة</h2>
                <?php foreach ($generated_links as $item): ?>
                    <div class="offer-links">
                        <div class="offer-title">🎯 <?php echo htmlspecialchars($item['offer']['title']); ?></div>
                        
                        <div class="link-item">
                            <div class="link-label">🔗 رابط أساسي (offer_id + ip):</div>
                            <div class="link-url"><?php echo htmlspecialchars($item['links']['basic']); ?></div>
                            <button class="copy-btn" onclick="copyToClipboard('<?php echo addslashes($item['links']['basic']); ?>')">📋 نسخ</button>
                        </div>
                        
                        <div class="link-item">
                            <div class="link-label">🔗 رابط بديل (subid + user_ip):</div>
                            <div class="link-url"><?php echo htmlspecialchars($item['links']['subid']); ?></div>
                            <button class="copy-btn" onclick="copyToClipboard('<?php echo addslashes($item['links']['subid']); ?>')">📋 نسخ</button>
                        </div>
                        
                        <?php if ($item['links']['with_params'] !== $item['links']['basic']): ?>
                            <div class="link-item">
                                <div class="link-label">🔗 رابط مع معاملات مخصصة:</div>
                                <div class="link-url"><?php echo htmlspecialchars($item['links']['with_params']); ?></div>
                                <button class="copy-btn" onclick="copyToClipboard('<?php echo addslashes($item['links']['with_params']); ?>')">📋 نسخ</button>
                            </div>
                        <?php endif; ?>
                        
                        <div class="link-item">
                            <div class="link-label">🧪 رابط اختبار:</div>
                            <div class="link-url"><?php echo htmlspecialchars($item['links']['test_link']); ?></div>
                            <button class="copy-btn" onclick="copyToClipboard('<?php echo addslashes($item['links']['test_link']); ?>')">📋 نسخ</button>
                            <a href="<?php echo htmlspecialchars($item['links']['test_link']); ?>" target="_blank" style="margin-right: 0.5rem; color: #28a745; text-decoration: none;">🚀 اختبار</a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div class="form-section">
            <h2>🧪 اختبار رابط Postback</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="test_url">رابط Postback للاختبار:</label>
                    <input type="url" id="test_url" name="test_url" placeholder="https://yoursite.com/postback.php?offer_id=1&ip=***********" required>
                </div>
                
                <button type="submit" name="test_postback" class="btn btn-success">🧪 اختبار Postback</button>
            </form>
        </div>
    </div>
    
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ الرابط بنجاح! 📋');
            }, function(err) {
                console.error('فشل في نسخ الرابط: ', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('تم نسخ الرابط بنجاح! 📋');
                } catch (err) {
                    alert('فشل في نسخ الرابط. يرجى النسخ يدوياً.');
                }
                document.body.removeChild(textArea);
            });
        }
        
        // تحديد/إلغاء تحديد جميع العروض
        function toggleAllOffers() {
            const checkboxes = document.querySelectorAll('input[name="offers[]"]');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }
        
        // إضافة زر تحديد الكل
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxGroup = document.querySelector('.checkbox-group');
            if (checkboxGroup) {
                const toggleBtn = document.createElement('button');
                toggleBtn.type = 'button';
                toggleBtn.className = 'btn';
                toggleBtn.style.marginBottom = '1rem';
                toggleBtn.textContent = '🔄 تحديد/إلغاء تحديد الكل';
                toggleBtn.onclick = toggleAllOffers;
                
                checkboxGroup.parentNode.insertBefore(toggleBtn, checkboxGroup);
            }
        });
    </script>
</body>
</html>

<?php
$conn->close();
?>
