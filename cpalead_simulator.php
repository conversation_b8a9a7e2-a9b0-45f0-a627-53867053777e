<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$simulation_result = '';

// معالجة محاكاة CPALead
if (isset($_POST['simulate_conversion'])) {
    $postback_url = trim($_POST['postback_url']);
    $offer_id = (int)$_POST['offer_id'];
    $user_ip = trim($_POST['user_ip']);
    $payout = (float)$_POST['payout'];
    $campaign_id = trim($_POST['campaign_id']);
    $lead_id = trim($_POST['lead_id']);
    $country_iso = trim($_POST['country_iso']);
    
    if (!empty($postback_url)) {
        // استبدال المعاملات في رابط Postback
        $final_url = str_replace([
            '{subid}',
            '{campaign_id}',
            '{ip_address}',
            '{payout}',
            '{lead_id}',
            '{country_iso}',
            '{campaign_name}',
            '{gateway_id}',
            '{virtual_currency}'
        ], [
            $offer_id,
            $campaign_id,
            $user_ip,
            $payout,
            $lead_id,
            $country_iso,
            'Test Campaign',
            'gate123',
            '100'
        ], $postback_url);
        
        // تنفيذ طلب HTTP
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $final_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'CPALead-Postback/1.0');
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $simulation_result = '<div class="alert alert-error">❌ خطأ في الاتصال: ' . htmlspecialchars($error) . '</div>';
        } else {
            $status_class = $http_code == 200 ? 'alert-success' : 'alert-error';
            $status_icon = $http_code == 200 ? '✅' : '❌';
            
            $simulation_result = '<div class="alert ' . $status_class . '">';
            $simulation_result .= $status_icon . ' محاكاة CPALead مكتملة!<br>';
            $simulation_result .= '<strong>كود الاستجابة:</strong> ' . $http_code . '<br>';
            $simulation_result .= '<strong>الرابط المستخدم:</strong><br>';
            $simulation_result .= '<code style="word-break: break-all;">' . htmlspecialchars($final_url) . '</code><br><br>';
            $simulation_result .= '<strong>استجابة الخادم:</strong><br>';
            $simulation_result .= '<pre style="background: #f8f9fa; padding: 1rem; border-radius: 5px; max-height: 300px; overflow-y: auto;">' . htmlspecialchars($response) . '</pre>';
            $simulation_result .= '</div>';
        }
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال رابط Postback!</div>';
    }
}

// الحصول على قائمة العروض للاختبار
$conn = getDBConnection();
$offers = $conn->query("SELECT id, title FROM offers WHERE is_active = 1 ORDER BY id DESC LIMIT 10");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محاكي CPALead - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        .preset-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .preset-btn {
            background: #17a2b8;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .preset-btn:hover {
            background: #138496;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .preset-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 محاكي CPALead</h1>
            <p>محاكاة إرسال Postback من CPALead لاختبار النظام</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="postback_wizard.php">🧙‍♂️ معالج Postback</a>
                <a href="postback_debug.php">🔍 تشخيص Postback</a>
                <a href="conversions.php">📊 التحويلات</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        <?php echo $simulation_result; ?>
        
        <!-- نموذج محاكاة CPALead -->
        <div class="form-section">
            <h3>🎭 محاكاة إرسال Postback من CPALead</h3>
            
            <div class="info-box">
                <h4>📋 كيف يعمل المحاكي:</h4>
                <ul>
                    <li>يحاكي إرسال Postback من خوادم CPALead</li>
                    <li>يستبدل المعاملات الديناميكية بقيم حقيقية</li>
                    <li>يرسل طلب HTTP إلى رابط Postback الخاص بك</li>
                    <li>يعرض الاستجابة والنتائج</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="postback_url">🔗 رابط Postback:</label>
                    <textarea id="postback_url" name="postback_url" rows="3" placeholder="https://yoursite.com/postback_cpalead.php?subid={subid}&ip_address={ip_address}&payout={payout}" required></textarea>
                    
                    <div class="preset-buttons">
                        <button type="button" class="preset-btn" onclick="setPreset('basic')">📋 Postback أساسي</button>
                        <button type="button" class="preset-btn" onclick="setPreset('advanced')">🚀 Postback متقدم</button>
                        <button type="button" class="preset-btn" onclick="setPreset('minimal')">⚡ Postback مبسط</button>
                    </div>
                </div>
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="offer_id">🎯 معرف العرض (subid):</label>
                        <select id="offer_id" name="offer_id" required>
                            <option value="">-- اختر عرض --</option>
                            <?php while ($offer = $offers->fetch_assoc()): ?>
                                <option value="<?php echo $offer['id']; ?>">
                                    العرض #<?php echo $offer['id']; ?> - <?php echo htmlspecialchars(substr($offer['title'], 0, 30)); ?>...
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="user_ip">🌐 عنوان IP المستخدم:</label>
                        <input type="text" id="user_ip" name="user_ip" value="*************" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="payout">💰 مبلغ العمولة:</label>
                        <input type="number" id="payout" name="payout" step="0.01" value="2.50" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="campaign_id">📊 معرف الحملة:</label>
                        <input type="text" id="campaign_id" name="campaign_id" value="camp_123">
                    </div>
                    
                    <div class="form-group">
                        <label for="lead_id">👤 معرف العميل:</label>
                        <input type="text" id="lead_id" name="lead_id" value="lead_abc123">
                    </div>
                    
                    <div class="form-group">
                        <label for="country_iso">🌍 رمز البلد:</label>
                        <input type="text" id="country_iso" name="country_iso" value="US" maxlength="2">
                    </div>
                </div>
                
                <button type="submit" name="simulate_conversion" class="btn btn-success">🎭 محاكاة إرسال Postback</button>
            </form>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="form-section">
            <h3>📚 معلومات المحاكاة</h3>
            
            <div class="info-box">
                <h4>🎯 ما يحدث عند المحاكاة:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li>استبدال جميع المعاملات الديناميكية بالقيم المدخلة</li>
                    <li>إرسال طلب HTTP GET إلى رابط Postback</li>
                    <li>تسجيل التحويل في قاعدة البيانات</li>
                    <li>ربط التحويل باسم المستخدم تلقائياً</li>
                    <li>عرض النتيجة والاستجابة</li>
                </ol>
            </div>
            
            <div class="info-box">
                <h4>🔍 علامات النجاح:</h4>
                <ul>
                    <li><strong>كود 200:</strong> تم استقبال Postback بنجاح</li>
                    <li><strong>استجابة JSON:</strong> تحتوي على status: "success"</li>
                    <li><strong>conversion_id:</strong> معرف التحويل الجديد</li>
                    <li><strong>username:</strong> اسم المستخدم المرتبط</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>❌ علامات الخطأ:</h4>
                <ul>
                    <li><strong>كود 400:</strong> معاملات مفقودة</li>
                    <li><strong>كود 404:</strong> العرض غير موجود</li>
                    <li><strong>كود 500:</strong> خطأ في الخادم</li>
                    <li><strong>status: "error":</strong> رسالة خطأ في الاستجابة</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function setPreset(type) {
            const baseUrl = window.location.origin + window.location.pathname.replace('cpalead_simulator.php', 'postback_cpalead.php');
            let url = '';
            
            switch(type) {
                case 'basic':
                    url = baseUrl + '?subid={subid}&ip_address={ip_address}&payout={payout}';
                    break;
                case 'advanced':
                    url = baseUrl + '?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&idfa={idfa}&gaid={gaid}&payout={payout}&ip_address={ip_address}&gateway_id={gateway_id}&lead_id={lead_id}&country_iso={country_iso}&virtual_currency={virtual_currency}';
                    break;
                case 'minimal':
                    url = baseUrl + '?subid={subid}&ip_address={ip_address}';
                    break;
            }
            
            document.getElementById('postback_url').value = url;
        }
        
        // تعيين رابط افتراضي عند تحميل الصفحة
        window.onload = function() {
            if (!document.getElementById('postback_url').value) {
                setPreset('basic');
            }
        };
    </script>
</body>
</html>

<?php $conn->close(); ?>
