<?php
require_once 'config.php';
require_once 'countries.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات البلدان - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stats-card h3 {
            color: #333;
            margin-bottom: 1rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .country-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            margin: 0.3rem 0;
            background: #f8f9fa;
            border-radius: 5px;
            border-right: 4px solid #667eea;
        }
        
        .country-name {
            font-weight: bold;
        }
        
        .country-count {
            background: #667eea;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.9rem;
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .device-badge {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .amount-badge {
            background: #ffc107;
            color: #333;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .countries-list {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 إحصائيات البلدان والعروض</h1>
            <p>تحليل شامل للعروض والبلدان المدعومة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="api_manager.php">🔌 إدارة API</a>
                <a href="offers_manager.php">📋 إدارة العروض</a>
            </div>
        </div>
        
        <div class="stats-grid">
            <!-- إحصائيات عامة -->
            <div class="stats-card">
                <h3>📈 إحصائيات عامة</h3>
                <?php
                // إجمالي العروض
                $totalOffers = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
                
                // العروض مع بلدان محددة
                $offersWithCountries = $conn->query("SELECT COUNT(*) as count FROM offers WHERE countries IS NOT NULL AND countries != ''")->fetch_assoc()['count'];
                
                // العروض العامة (جميع البلدان)
                $globalOffers = $totalOffers - $offersWithCountries;
                
                // متوسط المبلغ
                $avgAmount = $conn->query("SELECT AVG(amount) as avg FROM offers WHERE amount > 0")->fetch_assoc()['avg'];
                ?>
                
                <div class="country-item">
                    <span class="country-name">📊 إجمالي العروض</span>
                    <span class="country-count"><?php echo $totalOffers; ?></span>
                </div>
                
                <div class="country-item">
                    <span class="country-name">🌍 عروض عامة</span>
                    <span class="country-count"><?php echo $globalOffers; ?></span>
                </div>
                
                <div class="country-item">
                    <span class="country-name">🎯 عروض مخصصة</span>
                    <span class="country-count"><?php echo $offersWithCountries; ?></span>
                </div>
                
                <div class="country-item">
                    <span class="country-name">💰 متوسط المبلغ</span>
                    <span class="country-count">$<?php echo number_format($avgAmount, 2); ?></span>
                </div>
            </div>
            
            <!-- أكثر البلدان في العروض -->
            <div class="stats-card">
                <h3>🌍 أكثر البلدان في العروض</h3>
                <?php
                $result = $conn->query("
                    SELECT countries, COUNT(*) as count 
                    FROM offers 
                    WHERE countries IS NOT NULL AND countries != '' 
                    GROUP BY countries 
                    ORDER BY count DESC 
                    LIMIT 10
                ");
                
                $countryStats = [];
                while ($row = $result->fetch_assoc()) {
                    $countries = explode(',', $row['countries']);
                    foreach ($countries as $country) {
                        $country = trim($country);
                        if (!empty($country)) {
                            $countryStats[$country] = ($countryStats[$country] ?? 0) + $row['count'];
                        }
                    }
                }
                
                arsort($countryStats);
                $topCountries = array_slice($countryStats, 0, 10, true);
                
                foreach ($topCountries as $country => $count) {
                    $countryName = getCountryName($country);
                    echo "<div class='country-item'>";
                    echo "<span class='country-name'>$countryName</span>";
                    echo "<span class='country-count'>$count</span>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <!-- إحصائيات الأجهزة -->
            <div class="stats-card">
                <h3>📱 إحصائيات الأجهزة</h3>
                <?php
                $deviceStats = $conn->query("
                    SELECT device, COUNT(*) as count 
                    FROM offers 
                    WHERE device IS NOT NULL AND device != '' 
                    GROUP BY device 
                    ORDER BY count DESC
                ");
                
                while ($row = $deviceStats->fetch_assoc()) {
                    $deviceName = ucfirst($row['device']);
                    $deviceIcon = [
                        'android' => '🤖',
                        'ios' => '🍎',
                        'desktop' => '💻',
                        'mobile' => '📱'
                    ][$row['device']] ?? '📱';
                    
                    echo "<div class='country-item'>";
                    echo "<span class='country-name'>$deviceIcon $deviceName</span>";
                    echo "<span class='country-count'>{$row['count']}</span>";
                    echo "</div>";
                }
                ?>
            </div>
            
            <!-- إحصائيات أنواع الدفع -->
            <div class="stats-card">
                <h3>💳 أنواع الدفع</h3>
                <?php
                $payoutStats = $conn->query("
                    SELECT payout_type, COUNT(*) as count, AVG(amount) as avg_amount
                    FROM offers 
                    WHERE payout_type IS NOT NULL AND payout_type != '' 
                    GROUP BY payout_type 
                    ORDER BY count DESC
                ");
                
                while ($row = $payoutStats->fetch_assoc()) {
                    $payoutIcon = [
                        'CPA' => '💰',
                        'CPE' => '🎯',
                        'CPI' => '📲',
                        'CPL' => '📝'
                    ][$row['payout_type']] ?? '💵';
                    
                    echo "<div class='country-item'>";
                    echo "<span class='country-name'>$payoutIcon {$row['payout_type']}</span>";
                    echo "<span class='country-count'>{$row['count']} ($" . number_format($row['avg_amount'], 2) . ")</span>";
                    echo "</div>";
                }
                ?>
            </div>
        </div>
        
        <!-- جدول العروض التفصيلي -->
        <div class="offers-table">
            <h3>📋 العروض التفصيلية</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>العنوان</th>
                            <th>البلدان</th>
                            <th>الجهاز</th>
                            <th>المبلغ</th>
                            <th>نوع الدفع</th>
                            <th>تاريخ الإضافة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $offers = $conn->query("
                            SELECT title, countries, device, amount, payout_type, created_at 
                            FROM offers 
                            ORDER BY created_at DESC 
                            LIMIT 20
                        ");
                        
                        while ($offer = $offers->fetch_assoc()) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars(substr($offer['title'], 0, 50)) . "...</td>";
                            echo "<td class='countries-list'>" . formatCountriesDisplay($offer['countries'], 2) . "</td>";
                            echo "<td>";
                            if ($offer['device']) {
                                echo "<span class='device-badge'>" . ucfirst($offer['device']) . "</span>";
                            } else {
                                echo "-";
                            }
                            echo "</td>";
                            echo "<td>";
                            if ($offer['amount'] > 0) {
                                echo "<span class='amount-badge'>$" . $offer['amount'] . "</span>";
                            } else {
                                echo "-";
                            }
                            echo "</td>";
                            echo "<td>" . ($offer['payout_type'] ?: '-') . "</td>";
                            echo "<td>" . date('Y-m-d', strtotime($offer['created_at'])) . "</td>";
                            echo "</tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>

<?php $conn->close(); ?>
