<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$user_ip = $_SERVER['REMOTE_ADDR'];

// الحصول على بلد المستخدم
$ipInfo = checkIPQuality($user_ip);
$detectedCountry = $ipInfo['country'];

// التحقق من الدولة المختارة
$selectedCountry = isset($_GET['country']) && !empty($_GET['country']) ? $_GET['country'] : $detectedCountry;

// التحقق من وضع العروض
$offers_mode = 'automatic';
try {
    $conn = getDBConnection();
    $result = $conn->query("SELECT setting_value FROM settings WHERE setting_key = 'offers_mode'");
    if ($result && $result->num_rows > 0) {
        $offers_mode = $result->fetch_assoc()['setting_value'];
    }
    $conn->close();
} catch (Exception $e) {
    // استخدام الوضع الافتراضي
}

$debug_info = [];

try {
    $conn = getDBConnection();
    
    // 1. فحص إجمالي العروض
    $total_offers = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
    $debug_info[] = "إجمالي العروض في قاعدة البيانات: $total_offers";
    
    // 2. فحص العروض النشطة
    $active_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
    $debug_info[] = "العروض النشطة: $active_offers";
    
    // 3. فحص العروض حسب النوع
    $manual_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id LIKE 'manual_%'")->fetch_assoc()['count'];
    $auto_offers = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id NOT LIKE 'manual_%' OR external_id IS NULL OR external_id = ''")->fetch_assoc()['count'];
    $debug_info[] = "العروض اليدوية: $manual_offers";
    $debug_info[] = "العروض التلقائية: $auto_offers";
    
    // 4. فحص النقرات للمستخدم الحالي
    $clicked_offers = $conn->query("SELECT COUNT(*) as count FROM clicks WHERE ip_address = '$user_ip' AND clicked_at > DATE_SUB(NOW(), INTERVAL 15 DAY)")->fetch_assoc()['count'];
    $debug_info[] = "العروض التي نقر عليها المستخدم خلال 15 يوم: $clicked_offers";
    
    // 5. بناء الاستعلام كما في الصفحة الرئيسية
    $countryFilter = "";
    $modeFilter = "";
    $params = [$user_ip];
    $types = "s";
    
    // فلترة حسب وضع العروض
    if ($offers_mode === 'manual') {
        $modeFilter = " AND (o.external_id LIKE 'manual_%')";
    } else {
        $modeFilter = " AND (o.external_id NOT LIKE 'manual_%' OR o.external_id IS NULL OR o.external_id = '')";
    }
    
    if ($selectedCountry && $selectedCountry !== 'غير معروف' && $selectedCountry !== 'محلي' && $selectedCountry !== 'all' && !empty($selectedCountry)) {
        $countryFilter = " AND (o.countries IS NULL OR o.countries = '' OR o.countries LIKE ? OR o.countries LIKE ? OR o.countries LIKE ?)";
        $params[] = "%{$selectedCountry}%";
        $params[] = "%ALL%";
        $params[] = "%Global%";
        $types .= "sss";
    }
    
    $sql = "
        SELECT o.id, o.title, o.image_url, o.countries, o.device, o.amount, o.payout_type, o.external_id
        FROM offers o
        WHERE o.is_active = 1
        AND o.id NOT IN (
            SELECT DISTINCT c.offer_id
            FROM clicks c
            WHERE c.ip_address = ?
            AND c.clicked_at > DATE_SUB(NOW(), INTERVAL 15 DAY)
        ) {$countryFilter} {$modeFilter}
        ORDER BY o.id DESC
    ";
    
    $debug_info[] = "الاستعلام المستخدم: " . str_replace(["\n", "    "], [" ", " "], $sql);
    $debug_info[] = "معاملات الاستعلام: " . json_encode($params);
    $debug_info[] = "أنواع المعاملات: $types";
    
    // 6. تنفيذ الاستعلام
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $offers_result = $stmt->get_result();
    $offers_count = $offers_result->num_rows;
    
    $debug_info[] = "عدد العروض المعروضة للمستخدم: $offers_count";
    
    // 7. عرض العروض الموجودة
    $sample_offers = [];
    while ($offer = $offers_result->fetch_assoc()) {
        $sample_offers[] = [
            'id' => $offer['id'],
            'title' => $offer['title'],
            'external_id' => $offer['external_id'],
            'countries' => $offer['countries'],
            'device' => $offer['device']
        ];
    }
    
    // 8. اختبار بدون فلاتر
    $no_filter_sql = "SELECT COUNT(*) as count FROM offers WHERE is_active = 1";
    $no_filter_count = $conn->query($no_filter_sql)->fetch_assoc()['count'];
    $debug_info[] = "العروض النشطة بدون أي فلاتر: $no_filter_count";
    
    // 9. اختبار فلتر الوضع فقط
    $mode_only_sql = "SELECT COUNT(*) as count FROM offers WHERE is_active = 1 $modeFilter";
    $mode_only_count = $conn->query($mode_only_sql)->fetch_assoc()['count'];
    $debug_info[] = "العروض بعد فلتر الوضع فقط: $mode_only_count";
    
    $conn->close();
    
} catch (Exception $e) {
    $debug_info[] = "خطأ: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            color: #666;
        }
        
        .debug-section {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .debug-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid #2196f3;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .offers-section {
            background: #f0fff4;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .offer-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid #28a745;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص العروض</h1>
        
        <div class="info-grid">
            <div class="info-card">
                <div class="info-title">عنوان IP الحالي</div>
                <div class="info-value"><?php echo htmlspecialchars($user_ip); ?></div>
            </div>
            
            <div class="info-card">
                <div class="info-title">الدولة المكتشفة</div>
                <div class="info-value"><?php echo htmlspecialchars($detectedCountry); ?></div>
            </div>
            
            <div class="info-card">
                <div class="info-title">الدولة المختارة</div>
                <div class="info-value"><?php echo htmlspecialchars($selectedCountry); ?></div>
            </div>
            
            <div class="info-card">
                <div class="info-title">وضع العروض</div>
                <div class="info-value"><?php echo $offers_mode === 'manual' ? 'يدوي' : 'تلقائي'; ?></div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3 style="color: #1976d2; margin-bottom: 1rem;">📊 معلومات التشخيص</h3>
            
            <?php foreach ($debug_info as $info): ?>
                <div class="debug-item">
                    <?php echo htmlspecialchars($info); ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (!empty($sample_offers)): ?>
        <div class="offers-section">
            <h3 style="color: #2e7d32; margin-bottom: 1rem;">🎁 العروض المتاحة للمستخدم</h3>
            
            <?php foreach ($sample_offers as $offer): ?>
                <div class="offer-item">
                    <strong>ID: <?php echo $offer['id']; ?></strong> - 
                    <?php echo htmlspecialchars($offer['title']); ?>
                    <br>
                    <small style="color: #666;">
                        النوع: <?php echo $offer['external_id'] ? (strpos($offer['external_id'], 'manual_') === 0 ? 'يدوي' : 'تلقائي') : 'تلقائي'; ?> | 
                        البلدان: <?php echo $offer['countries'] ?: 'جميع البلدان'; ?> | 
                        الجهاز: <?php echo $offer['device'] ?: 'جميع الأجهزة'; ?>
                    </small>
                </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div style="background: #fff3cd; padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem; text-align: center;">
            <h3 style="color: #856404;">⚠️ لا توجد عروض متاحة للمستخدم</h3>
            <p style="color: #856404; margin-top: 0.5rem;">راجع معلومات التشخيص أعلاه لمعرفة السبب</p>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="index.php">🏠 الصفحة الرئيسية</a>
            <a href="offers_management.php">📋 إدارة العروض</a>
            <a href="add_sample_offers.php">🎁 إضافة عروض تجريبية</a>
            <a href="fix_database_structure.php">🔧 إصلاح قاعدة البيانات</a>
        </div>
    </div>
</body>
</html>
