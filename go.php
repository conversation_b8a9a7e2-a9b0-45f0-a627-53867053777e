<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

// التحقق من وجود معرف العرض
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$offer_id = (int)$_GET['id'];
$user_ip = getUserIP();

// التحقق من وجود العرض
$conn = getDBConnection();
$stmt = $conn->prepare("SELECT id, title, offer_url FROM offers WHERE id = ?");
$stmt->bind_param("i", $offer_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $stmt->close();
    $conn->close();
    header('Location: index.php');
    exit;
}

$offer = $result->fetch_assoc();
$stmt->close();

// التحقق من عدم وجود ضغطة سابقة خلال 15 يوم
if (hasRecentClick($offer_id, $user_ip, 15)) {
    $conn->close();
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تم الضغط مسبقاً</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
            }
            
            .message-container {
                background: white;
                padding: 3rem;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 500px;
                width: 100%;
            }
            
            .message-container h2 {
                color: #e74c3c;
                margin-bottom: 1rem;
                font-size: 2rem;
            }
            
            .message-container p {
                color: #666;
                margin-bottom: 2rem;
                font-size: 1.1rem;
                line-height: 1.6;
            }
            
            .btn {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 0.75rem 2rem;
                text-decoration: none;
                border-radius: 5px;
                font-size: 1rem;
                transition: transform 0.2s;
                display: inline-block;
            }
            
            .btn:hover {
                transform: translateY(-2px);
            }
        </style>
    </head>
    <body>
        <div class="message-container">
            <h2>⚠️ تم الضغط مسبقاً</h2>
            <p>لقد قمت بالضغط على هذا العرض خلال الـ 15 يوم الماضية. يرجى المحاولة مرة أخرى لاحقاً أو اختيار عرض آخر.</p>
            <a href="index.php" class="btn">🏠 العودة للصفحة الرئيسية</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// الحصول على اسم المستخدم للـ IP
$username = '';
$usernameStmt = $conn->prepare("SELECT username FROM saved_ips WHERE ip_address = ?");
$usernameStmt->bind_param("s", $user_ip);
$usernameStmt->execute();
$usernameResult = $usernameStmt->get_result();
if ($usernameResult->num_rows > 0) {
    $username = $usernameResult->fetch_assoc()['username'];
} else {
    // إنشاء اسم مستخدم جديد إذا لم يكن موجود
    $username = generateRandomUsername();
    // التأكد من عدم تكرار اسم المستخدم
    $checkStmt = $conn->prepare("SELECT id FROM saved_ips WHERE username = ?");
    $attempts = 0;
    while ($attempts < 10) {
        $checkStmt->bind_param("s", $username);
        $checkStmt->execute();
        if ($checkStmt->get_result()->num_rows == 0) {
            break;
        }
        $username = generateRandomUsername();
        $attempts++;
    }
    $checkStmt->close();
}
$usernameStmt->close();

// الحصول على معلومات إضافية للتتبع
$source = $_GET['source'] ?? 'direct';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

// تسجيل الضغطة مع اسم المستخدم
$stmt = $conn->prepare("INSERT INTO clicks (offer_id, ip_address, username, source, user_agent) VALUES (?, ?, ?, ?, ?)");
$stmt->bind_param("issss", $offer_id, $user_ip, $username, $source, $user_agent);
$stmt->execute();
$stmt->close();
$conn->close();

// إنشاء رابط العرض مع معاملات التتبع
$final_url = $offer['offer_url'];

// إضافة معاملات CPALead إذا كان الرابط يحتوي على CPALead
if (strpos($final_url, 'cpalead.com') !== false) {
    // استبدال المعاملات الديناميكية
    $final_url = str_replace('{user_ip}', $user_ip, $final_url);
    $final_url = str_replace('{subid}', $offer_id, $final_url);

    // إضافة معاملات إضافية
    $separator = (strpos($final_url, '?') !== false) ? '&' : '?';
    $final_url .= $separator . "ip=" . urlencode($user_ip);
    $final_url .= "&subid=" . urlencode($offer_id);
    $final_url .= "&username=" . urlencode($username);
}

// عرض صفحة تأكيد قبل إعادة التوجيه
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم تسجيل الضغطة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .message-container {
            background: white;
            padding: 3rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .message-container h2 {
            color: #4CAF50;
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .message-container p {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .countdown {
            font-size: 1.5rem;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 2rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.75rem 2rem;
            text-decoration: none;
            border-radius: 5px;
            font-size: 1rem;
            transition: transform 0.2s;
            display: inline-block;
            margin: 0 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
    </style>
</head>
<body>
    <div class="message-container">
        <h2>✅ تم تسجيل الضغطة بنجاح!</h2>
        <p>تم تسجيل ضغطتك على العرض "<strong><?php echo htmlspecialchars($offer['title']); ?></strong>"</p>
        <p><strong>👤 اسم المستخدم:</strong> <?php echo htmlspecialchars($username); ?></p>
        <p>⚠️ <strong>ملاحظة مهمة:</strong> لن يظهر لك هذا العرض مرة أخرى لمدة 15 يوم من الآن.</p>

        <div class="countdown" id="countdown">سيتم التوجيه خلال <span id="timer">2</span> ثواني...</div>

        <a href="<?php echo htmlspecialchars($final_url); ?>" class="btn btn-success">
            🚀 الانتقال للعرض الآن
        </a>
        <a href="index.php" class="btn">
            🏠 العودة للصفحة الرئيسية
        </a>
    </div>

    <script>
        let timeLeft = 2;
        const timer = document.getElementById('timer');
        const countdown = document.getElementById('countdown');

        const countdownInterval = setInterval(() => {
            timeLeft--;
            timer.textContent = timeLeft;

            if (timeLeft <= 0) {
                clearInterval(countdownInterval);
                countdown.textContent = 'جاري التوجيه...';
                window.location.href = '<?php echo addslashes($final_url); ?>';
            }
        }, 1000);
    </script>
</body>
</html>
<?php
exit;
?>
