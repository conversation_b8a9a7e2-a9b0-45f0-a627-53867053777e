<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'sql303.infinityfree.com');
define('DB_USER', 'if0_39395085');
define('DB_PASS', 'Qweeee12');
define('DB_NAME', 'if0_39395085_q12');

// كلمة مرور الموقع
define('SITE_PASSWORD', 's');

// تحسينات للاستضافة المجانية
ini_set('mysql.connect_timeout', 60);
ini_set('default_socket_timeout', 60);

// الاتصال بقاعدة البيانات مع إعادة المحاولة
function getDBConnection($retry = 3) {
    for ($i = 0; $i < $retry; $i++) {
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

        if (!$conn->connect_error) {
            $conn->set_charset("utf8mb4");
            return $conn;
        }

        // انتظار ثانية واحدة قبل المحاولة مرة أخرى
        if ($i < $retry - 1) {
            sleep(1);
        }
    }

    die("فشل الاتصال بقاعدة البيانات بعد $retry محاولات: " . $conn->connect_error);
}

// التحقق من كلمة المرور
function checkPassword() {
    session_start();
    
    if (!isset($_SESSION['authenticated'])) {
        if (isset($_POST['password']) && $_POST['password'] === SITE_PASSWORD) {
            $_SESSION['authenticated'] = true;
            return true;
        }
        
        showLoginForm();
        exit;
    }
    
    return true;
}

// عرض نموذج تسجيل الدخول
function showLoginForm() {
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تسجيل الدخول - موقع CPA</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .login-container {
                background: white;
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
                width: 100%;
                max-width: 400px;
            }
            
            .login-container h2 {
                text-align: center;
                margin-bottom: 1.5rem;
                color: #333;
            }
            
            .form-group {
                margin-bottom: 1rem;
            }
            
            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                color: #555;
                font-weight: bold;
            }
            
            .form-group input {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 1rem;
                transition: border-color 0.3s;
            }
            
            .form-group input:focus {
                outline: none;
                border-color: #667eea;
            }
            
            .btn {
                width: 100%;
                padding: 0.75rem;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 1rem;
                cursor: pointer;
                transition: transform 0.2s;
            }
            
            .btn:hover {
                transform: translateY(-2px);
            }
            
            @media (max-width: 480px) {
                .login-container {
                    margin: 1rem;
                    padding: 1.5rem;
                }
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h2>🔒 تسجيل الدخول</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn">دخول</button>
            </form>
        </div>
    </body>
    </html>
    <?php
}

// الحصول على IP المستخدم
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// التحقق من وجود ضغطة سابقة خلال 15 يوم
function hasRecentClick($offer_id, $ip, $days = 15) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT id FROM clicks WHERE offer_id = ? AND ip_address = ? AND clicked_at > DATE_SUB(NOW(), INTERVAL ? DAY)");
    $stmt->bind_param("isi", $offer_id, $ip, $days);
    $stmt->execute();
    $result = $stmt->get_result();
    $hasClick = $result->num_rows > 0;
    $stmt->close();
    $conn->close();
    return $hasClick;
}

// التحقق من وجود تحويل سابق خلال 24 ساعة
function hasRecentConversion($offer_id, $ip, $hours = 24) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT id FROM conversions WHERE offer_id = ? AND ip_address = ? AND converted_at > DATE_SUB(NOW(), INTERVAL ? HOUR)");
    $stmt->bind_param("isi", $offer_id, $ip, $hours);
    $stmt->execute();
    $result = $stmt->get_result();
    $hasConversion = $result->num_rows > 0;
    $stmt->close();
    $conn->close();
    return $hasConversion;
}

// التحقق من وجود IP محفوظ خلال 15 يوم
function hasRecentSavedIP($ip, $days = 15) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT id FROM saved_ips WHERE ip_address = ? AND saved_at > DATE_SUB(NOW(), INTERVAL ? DAY)");
    $stmt->bind_param("si", $ip, $days);
    $stmt->execute();
    $result = $stmt->get_result();
    $hasSaved = $result->num_rows > 0;
    $stmt->close();
    $conn->close();
    return $hasSaved;
}

// إنشاء اسم مستخدم عشوائي أجنبي
function generateRandomUsername() {
    // قوائم الصفات الأجنبية
    $adjectives = [
        'Fast', 'Smart', 'Strong', 'Cool', 'Epic', 'Super', 'Active', 'Pro',
        'Elite', 'Master', 'Expert', 'Skilled', 'Talented', 'Creative', 'Effective', 'Star',
        'Hero', 'Legend', 'Genius', 'Supreme', 'Brilliant', 'Shiny', 'Bright', 'Golden',
        'Wild', 'Fierce', 'Bold', 'Brave', 'Swift', 'Sharp', 'Mighty', 'Power',
        'Ultra', 'Mega', 'Alpha', 'Prime', 'Royal', 'Noble', 'Divine', 'Cosmic'
    ];

    // قوائم الأسماء الأجنبية
    $nouns = [
        'Tiger', 'Lion', 'Eagle', 'Hawk', 'Wolf', 'Panther', 'Falcon', 'Horse',
        'Fighter', 'Warrior', 'Knight', 'Champion', 'King', 'Prince', 'Leader', 'Chief',
        'Star', 'Moon', 'Sun', 'Lightning', 'Thunder', 'Storm', 'Hurricane', 'Blaze',
        'Phoenix', 'Dragon', 'Viper', 'Cobra', 'Shark', 'Raven', 'Scorpion', 'Titan',
        'Hunter', 'Ranger', 'Sniper', 'Ninja', 'Samurai', 'Gladiator', 'Spartan', 'Viking',
        'Shadow', 'Ghost', 'Phantom', 'Demon', 'Angel', 'Beast', 'Monster', 'Destroyer'
    ];

    // أسماء أولى أجنبية شائعة
    $firstNames = [
        'Alex', 'Mike', 'John', 'David', 'Chris', 'Ryan', 'Kevin', 'Brian',
        'Mark', 'Steve', 'Paul', 'Tom', 'Jack', 'Nick', 'Sam', 'Max',
        'Luke', 'Jake', 'Matt', 'Dan', 'Ben', 'Josh', 'Kyle', 'Sean',
        'Adam', 'Noah', 'Ethan', 'Mason', 'Logan', 'Lucas', 'Jacob', 'Liam'
    ];

    // اختيار نمط عشوائي لإنشاء اسم المستخدم (بدون أرقام)
    $patterns = [
        'adjective_noun',      // FastTiger
        'firstname_noun',      // AlexWolf
        'adjective_firstname', // CoolMike
        'noun_adjective',      // TigerFast
        'firstname_adjective', // AlexCool
        'double_adjective',    // FastCool
        'double_noun',         // TigerWolf
        'noun_only',          // Dragon
        'firstname_only'      // Kevin
    ];

    $pattern = $patterns[array_rand($patterns)];

    switch ($pattern) {
        case 'adjective_noun':
            $adjective = $adjectives[array_rand($adjectives)];
            $noun = $nouns[array_rand($nouns)];
            return $adjective . $noun;

        case 'firstname_noun':
            $firstName = $firstNames[array_rand($firstNames)];
            $noun = $nouns[array_rand($nouns)];
            return $firstName . $noun;

        case 'adjective_firstname':
            $adjective = $adjectives[array_rand($adjectives)];
            $firstName = $firstNames[array_rand($firstNames)];
            return $adjective . $firstName;

        case 'noun_adjective':
            $noun = $nouns[array_rand($nouns)];
            $adjective = $adjectives[array_rand($adjectives)];
            return $noun . $adjective;

        case 'firstname_adjective':
            $firstName = $firstNames[array_rand($firstNames)];
            $adjective = $adjectives[array_rand($adjectives)];
            return $firstName . $adjective;

        case 'double_adjective':
            $adjective1 = $adjectives[array_rand($adjectives)];
            $adjective2 = $adjectives[array_rand($adjectives)];
            // تجنب تكرار نفس الصفة
            while ($adjective1 === $adjective2) {
                $adjective2 = $adjectives[array_rand($adjectives)];
            }
            return $adjective1 . $adjective2;

        case 'double_noun':
            $noun1 = $nouns[array_rand($nouns)];
            $noun2 = $nouns[array_rand($nouns)];
            // تجنب تكرار نفس الاسم
            while ($noun1 === $noun2) {
                $noun2 = $nouns[array_rand($nouns)];
            }
            return $noun1 . $noun2;

        case 'noun_only':
            $noun = $nouns[array_rand($nouns)];
            return $noun;

        case 'firstname_only':
            $firstName = $firstNames[array_rand($firstNames)];
            return $firstName;

        default:
            $adjective = $adjectives[array_rand($adjectives)];
            $noun = $nouns[array_rand($nouns)];
            return $adjective . $noun;
    }
}

// الحصول على اسم المستخدم لـ IP معين
function getUsernameForIP($ip) {
    $conn = getDBConnection();
    $stmt = $conn->prepare("SELECT username FROM saved_ips WHERE ip_address = ? ORDER BY saved_at DESC LIMIT 1");
    $stmt->bind_param("s", $ip);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $username = $row['username'];
    } else {
        // إنشاء اسم مستخدم جديد إذا لم يكن موجوداً
        $username = generateRandomUsername();
    }

    $stmt->close();
    $conn->close();
    return $username;
}

// حفظ IP جديد مع اسم المستخدم
function saveIP($ip) {
    $conn = getDBConnection();

    // التحقق من عدم وجود IP محفوظ خلال 15 يوم
    if (hasRecentSavedIP($ip, 15)) {
        $conn->close();
        return false;
    }

    // فحص جودة IP
    $ipInfo = checkIPQuality($ip);

    // إنشاء اسم مستخدم عشوائي
    $username = generateRandomUsername();

    // التأكد من عدم تكرار اسم المستخدم
    $attempts = 0;
    while ($attempts < 10) {
        $checkStmt = $conn->prepare("SELECT id FROM saved_ips WHERE username = ?");
        $checkStmt->bind_param("s", $username);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $checkStmt->close();

        if ($checkResult->num_rows == 0) {
            break; // اسم المستخدم فريد
        }

        $username = generateRandomUsername();
        $attempts++;
    }

    $stmt = $conn->prepare("INSERT INTO saved_ips (ip_address, username, country, city, isp, is_proxy, is_vpn, quality_score) VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE saved_at = CURRENT_TIMESTAMP, username = VALUES(username)");
    $stmt->bind_param("sssssibi", $ip, $username, $ipInfo['country'], $ipInfo['city'], $ipInfo['isp'], $ipInfo['is_proxy'], $ipInfo['is_vpn'], $ipInfo['quality_score']);
    $result = $stmt->execute();
    $stmt->close();
    $conn->close();

    return $result ? $username : false;
}

// فحص جودة IP
function checkIPQuality($ip) {
    $result = [
        'country' => 'غير معروف',
        'city' => 'غير معروف',
        'isp' => 'غير معروف',
        'is_proxy' => false,
        'is_vpn' => false,
        'quality_score' => 50
    ];

    // تجنب فحص IPs المحلية
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        $result['country'] = 'محلي';
        $result['city'] = 'محلي';
        $result['quality_score'] = 0;
        return $result;
    }

    try {
        // استخدام خدمة مجانية لفحص IP
        $apiUrl = "http://ip-api.com/json/{$ip}?fields=status,country,city,isp,proxy,query";
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'Mozilla/5.0 (compatible; CPA-Site/1.0)'
            ]
        ]);

        $response = @file_get_contents($apiUrl, false, $context);

        if ($response !== false) {
            $data = json_decode($response, true);

            if ($data && $data['status'] === 'success') {
                $result['country'] = $data['country'] ?? 'غير معروف';
                $result['city'] = $data['city'] ?? 'غير معروف';
                $result['isp'] = $data['isp'] ?? 'غير معروف';
                $result['is_proxy'] = isset($data['proxy']) ? $data['proxy'] : false;

                // حساب نقاط الجودة
                $score = 100;

                // خصم نقاط للبروكسي
                if ($result['is_proxy']) {
                    $score -= 50;
                }

                // خصم نقاط لبعض ISPs المشبوهة
                $suspiciousISPs = ['hosting', 'datacenter', 'cloud', 'server'];
                foreach ($suspiciousISPs as $suspicious) {
                    if (stripos($result['isp'], $suspicious) !== false) {
                        $score -= 30;
                        break;
                    }
                }

                $result['quality_score'] = max(0, min(100, $score));
            }
        }
    } catch (Exception $e) {
        // في حالة فشل الفحص، استخدم القيم الافتراضية
    }

    return $result;
}
?>
