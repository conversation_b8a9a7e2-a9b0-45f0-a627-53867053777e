<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';

// معالجة اختبار Postback
if (isset($_POST['test_postback'])) {
    $test_url = trim($_POST['test_url']);
    
    if (!empty($test_url)) {
        // تنفيذ طلب HTTP للـ URL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $test_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $message = '<div class="alert alert-error">❌ خطأ في الاتصال: ' . htmlspecialchars($error) . '</div>';
        } else {
            $message = '<div class="alert alert-success">✅ تم الاختبار بنجاح!</div>';
            $message .= '<div class="alert alert-info"><strong>كود الاستجابة:</strong> ' . $http_code . '<br>';
            $message .= '<strong>الاستجابة:</strong><br><pre>' . htmlspecialchars($response) . '</pre></div>';
        }
    } else {
        $message = '<div class="alert alert-error">❌ يرجى إدخال رابط الاختبار!</div>';
    }
}

// قراءة سجلات الأخطاء
$error_logs = '';
$error_file = 'cpalead_errors.log';
if (file_exists($error_file)) {
    $lines = file($error_file);
    $error_logs = implode('', array_slice($lines, -50)); // آخر 50 سطر
}

// قراءة سجلات التحويلات
$conversion_logs = '';
$conversion_file = 'cpalead_conversions.log';
if (file_exists($conversion_file)) {
    $lines = file($conversion_file);
    $conversion_logs = implode('', array_slice($lines, -20)); // آخر 20 سطر
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص Postback - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 1rem 0;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .diagnostic-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .diagnostic-item.error {
            border-left-color: #dc3545;
        }
        
        .diagnostic-item.warning {
            border-left-color: #ffc107;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .diagnostic-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص Postback</h1>
            <p>أداة تشخيص وحل مشاكل Postback مع CPALead</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="cpalead_postback_generator.php">🔗 مولد Postback</a>
                <a href="test_postback.php">🧪 اختبار Postback</a>
                <a href="conversions.php">📊 التحويلات</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- تشخيص سريع -->
        <div class="form-section">
            <h3>🔍 التشخيص السريع</h3>
            
            <div class="diagnostic-grid">
                <div class="diagnostic-item">
                    <h4>✅ ملف Postback</h4>
                    <p>postback_cpalead.php موجود ويعمل</p>
                </div>
                
                <div class="diagnostic-item <?php echo file_exists('cpalead_errors.log') ? 'warning' : ''; ?>">
                    <h4><?php echo file_exists('cpalead_errors.log') ? '⚠️' : '✅'; ?> سجل الأخطاء</h4>
                    <p><?php echo file_exists('cpalead_errors.log') ? 'يوجد أخطاء مسجلة' : 'لا توجد أخطاء'; ?></p>
                </div>
                
                <div class="diagnostic-item">
                    <h4>📡 IP المصدر</h4>
                    <p>IP الحالي: <?php echo $_SERVER['REMOTE_ADDR'] ?? 'غير محدد'; ?></p>
                </div>
                
                <div class="diagnostic-item">
                    <h4>🔗 قاعدة البيانات</h4>
                    <p>
                        <?php
                        try {
                            $conn = getDBConnection();
                            echo "✅ متصلة بنجاح";
                            $conn->close();
                        } catch (Exception $e) {
                            echo "❌ خطأ في الاتصال";
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- اختبار Postback -->
        <div class="form-section">
            <h3>🧪 اختبار Postback</h3>
            
            <div class="info-box">
                <h4>📋 أمثلة روابط الاختبار:</h4>
                <ul>
                    <li><strong>اختبار أساسي:</strong> postback_cpalead.php?subid=123&ip_address=*************</li>
                    <li><strong>اختبار كامل:</strong> postback_cpalead.php?campaign_id=456&subid=123&ip_address=*************&payout=2.50</li>
                    <li><strong>اختبار بدون offer_id:</strong> postback_cpalead.php?ip_address=************* (سيظهر خطأ)</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="test_url">🔗 رابط الاختبار:</label>
                    <input type="text" id="test_url" name="test_url" 
                           placeholder="postback_cpalead.php?subid=123&ip_address=*************&payout=2.50" 
                           value="postback_cpalead.php?subid=123&ip_address=*************&payout=2.50">
                </div>
                
                <button type="submit" name="test_postback" class="btn">🧪 اختبار الآن</button>
            </form>
        </div>
        
        <!-- سجل الأخطاء -->
        <?php if (!empty($error_logs)): ?>
        <div class="form-section">
            <h3>❌ سجل الأخطاء</h3>
            <div class="log-container"><?php echo htmlspecialchars($error_logs); ?></div>
            <button onclick="clearLogs('errors')" class="btn btn-danger">🗑️ مسح سجل الأخطاء</button>
        </div>
        <?php endif; ?>
        
        <!-- سجل التحويلات -->
        <?php if (!empty($conversion_logs)): ?>
        <div class="form-section">
            <h3>✅ سجل التحويلات</h3>
            <div class="log-container"><?php echo htmlspecialchars($conversion_logs); ?></div>
            <button onclick="clearLogs('conversions')" class="btn btn-danger">🗑️ مسح سجل التحويلات</button>
        </div>
        <?php endif; ?>
        
        <!-- حلول المشاكل الشائعة -->
        <div class="form-section">
            <h3>🔧 حلول المشاكل الشائعة</h3>
            
            <div class="info-box">
                <h4>❌ خطأ: Missing required parameters</h4>
                <ul>
                    <li>تأكد من وجود معامل subid في رابط Postback</li>
                    <li>تحقق من إعدادات CPALead Postback</li>
                    <li>تأكد من استخدام {subid} في رابط التتبع</li>
                    <li>جرب استخدام campaign_id بدلاً من subid</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>🔒 خطأ: Unauthorized IP address</h4>
                <ul>
                    <li>تأكد من أن IP المصدر هو ************ (CPALead)</li>
                    <li>أو قم بتعطيل فحص IP للاختبار</li>
                    <li>تحقق من إعدادات الخادم والـ Firewall</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>📊 خطأ: Offer not found</h4>
                <ul>
                    <li>تأكد من وجود العرض في قاعدة البيانات</li>
                    <li>تحقق من أن العرض نشط (is_active = 1)</li>
                    <li>تأكد من صحة معرف العرض</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>🔗 إعداد CPALead الصحيح:</h4>
                <ul>
                    <li><strong>Postback URL:</strong> https://yoursite.com/postback_cpalead.php?subid={subid}&ip_address={ip_address}&payout={payout}</li>
                    <li><strong>Tracking URL:</strong> https://yoursite.com/go.php?id={offer_id}&subid={subid}</li>
                    <li><strong>IP Whitelist:</strong> ************</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function clearLogs(type) {
            if (confirm('هل أنت متأكد من مسح السجل؟')) {
                fetch('clear_logs.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'type=' + type
                })
                .then(response => response.text())
                .then(data => {
                    alert('تم مسح السجل بنجاح');
                    location.reload();
                })
                .catch(error => {
                    alert('خطأ في مسح السجل');
                });
            }
        }
    </script>
</body>
</html>
