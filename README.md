# موقع CPA بالعربية 🎯

موقع CPA متكامل باللغة العربية مع جميع المميزات المطلوبة للعمل مع شبكات CPA.

## 🚀 المميزات

### 🔒 الحماية والأمان
- حماية الموقع بالكامل بكلمة مرور موحدة (`site123`)
- استخدام Prepared Statements لحماية قاعدة البيانات
- منع التكرار للضغطات والتحويلات حسب IP والوقت

### 🗄️ قاعدة البيانات
- جدول `offers`: العروض المتاحة
- جدول `clicks`: تسجيل الضغطات
- جدول `conversions`: تسجيل التحويلات الناجحة

### 🖥️ الواجهات
- **الصفحة الرئيسية**: عرض العروض المتاحة للمستخدم مع معلومات IP وزر الحفظ
- **لوحة التحكم**: إدارة العروض
- **صفحة التحويلات**: عرض الإحصائيات والتحويلات
- **صفحة الـ IPs المحفوظة**: عرض وإدارة عناوين IP المحفوظة مع فحص الجودة
- **صفحة Postback**: استقبال التحويلات من شبكات CPA

### 📱 التصميم
- متجاوب مع جميع الأجهزة
- تصميم عربي أنيق وبسيط
- بدون مكتبات خارجية

## 📁 ملفات المشروع

```
├── config.php          # إعدادات قاعدة البيانات والحماية وفحص IP
├── database.sql        # هيكل قاعدة البيانات مع جدول الـ IPs المحفوظة
├── index.php          # الصفحة الرئيسية مع معلومات IP وزر الحفظ
├── go.php             # معالجة الضغطات وإعادة التوجيه مع صفحة تأكيد
├── admin.php          # لوحة التحكم
├── conversions.php    # عرض التحويلات
├── saved_ips.php      # عرض وإدارة الـ IPs المحفوظة
├── usernames.php      # عرض وإدارة أسماء المستخدمين
├── username_guide.php # دليل استخدام أسماء المستخدمين
├── postback.php       # استقبال Postback من شبكات CPA
├── postback_generator.php # مولد روابط Postback
├── api_manager.php    # إدارة واستيراد العروض من CPALead API
├── auto_import.php    # الاستيراد التلقائي للعروض
├── test_api.php       # اختبار CPALead API المتقدم
├── countries.php      # إدارة أسماء البلدان والترجمة
├── countries_stats.php # إحصائيات البلدان والعروض
├── username_manager.php # إدارة وحذف أسماء المستخدمين
├── cpalead_integration.php # ربط جميع العروض بـ CPALead
├── test_postback.php  # اختبار Postback مع CPALead
├── auto_sync_offers.php # المزامنة التلقائية للعروض
├── sync_manager.php   # إدارة المزامنة التلقائية
├── offers_manager.php # إدارة العروض النشطة والمتوقفة
├── postback_cpalead.php # معالج Postback متوافق مع CPALead
├── cpalead_postback_generator.php # مولد Postback لـ CPALead
├── postback_wizard.php # معالج إعداد Postback تفاعلي
├── cpalead_simulator.php # محاكي CPALead لاختبار Postback
├── add_test_offers.php # إضافة عروض تجريبية للاختبار
├── notifications.php  # نظام الإشعارات
├── notifications_panel.php # لوحة عرض الإشعارات
├── postback_debug.php # تشخيص وحل مشاكل Postback
├── clear_logs.php     # مسح ملفات السجل
├── fix_database.php   # إصلاح مشاكل الترميز
├── test_db.php        # اختبار قاعدة البيانات
└── README.md          # هذا الملف
```

## ⚙️ التثبيت والإعداد

### 1. إعداد قاعدة البيانات
```sql
-- قم بتشغيل محتوى ملف database.sql في phpMyAdmin أو MySQL
```

### 2. تعديل إعدادات قاعدة البيانات
قم بتعديل الإعدادات في ملف `config.php`:
```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_USER', 'root');          // اسم المستخدم
define('DB_PASS', '');              // كلمة المرور
define('DB_NAME', 'cpa_site');      // اسم قاعدة البيانات
```

### 3. رفع الملفات
قم برفع جميع الملفات إلى خادم الويب الخاص بك.

### 4. الوصول للموقع
- **الصفحة الرئيسية**: `http://yoursite.com/`
- **لوحة التحكم**: `http://yoursite.com/admin.php`
- **التحويلات**: `http://yoursite.com/conversions.php`
- **الـ IPs المحفوظة**: `http://yoursite.com/saved_ips.php`
- **أسماء المستخدمين**: `http://yoursite.com/usernames.php`
- **إدارة أسماء المستخدمين**: `http://yoursite.com/username_manager.php`
- **دليل أسماء المستخدمين**: `http://yoursite.com/username_guide.php`
- **مولد Postback**: `http://yoursite.com/postback_generator.php`
- **إدارة API**: `http://yoursite.com/api_manager.php`
- **اختبار API المتقدم**: `http://yoursite.com/test_api.php`
- **ربط CPALead**: `http://yoursite.com/cpalead_integration.php`
- **اختبار Postback**: `http://yoursite.com/test_postback.php`
- **المزامنة التلقائية**: `http://yoursite.com/sync_manager.php`
- **إدارة العروض**: `http://yoursite.com/offers_manager.php`
- **مولد Postback CPALead**: `http://yoursite.com/cpalead_postback_generator.php`
- **معالج Postback التفاعلي**: `http://yoursite.com/postback_wizard.php`
- **محاكي CPALead**: `http://yoursite.com/cpalead_simulator.php`
- **عروض تجريبية**: `http://yoursite.com/add_test_offers.php`
- **لوحة الإشعارات**: `http://yoursite.com/notifications_panel.php`
- **تشخيص Postback**: `http://yoursite.com/postback_debug.php`
- **إحصائيات البلدان**: `http://yoursite.com/countries_stats.php`
- **إصلاح قاعدة البيانات**: `http://yoursite.com/fix_database.php`
- **الاستيراد التلقائي**: `http://yoursite.com/auto_import.php`
- **اختبار قاعدة البيانات**: `http://yoursite.com/test_db.php`
- **Postback**: `http://yoursite.com/postback.php`

## 🔑 كلمة المرور الافتراضية
```
s
```

## 🆕 المميزات الجديدة

### 💾 حفظ عناوين IP
- **زر حفظ IP**: يمكن للمستخدمين حفظ عنوان IP الخاص بهم
- **منع التكرار**: لا يمكن حفظ نفس IP خلال 15 يوم
- **إخفاء العروض**: عند حفظ IP، تختفي جميع العروض لمدة 15 يوم
- **إدارة متقدمة**: صفحة خاصة لعرض وإدارة الـ IPs المحفوظة

### 🔍 فحص جودة IP
- **معلومات شاملة**: عرض الدولة، المدينة، ومزود الخدمة
- **كشف البروكسي**: التحقق من استخدام البروكسي أو VPN
- **نقاط الجودة**: تقييم جودة IP من 0-100
- **إحصائيات متقدمة**: عرض إحصائيات شاملة للـ IPs

### 🎯 تحسين تجربة المستخدم
- **إخفاء العروض**: العروض تختفي بعد الضغط عليها لمدة 15 يوم
- **صفحة تأكيد**: عرض صفحة تأكيد قبل التوجيه للعرض
- **عداد تنازلي**: توجيه تلقائي بعد ثانيتين (محدث)
- **رسائل واضحة**: إشعارات مفصلة للمستخدم

### � أسماء المستخدمين العشوائية
- **إنشاء تلقائي**: إنشاء اسم مستخدم فريد لكل IP
- **أسماء أجنبية**: استخدام أسماء وكلمات إنجليزية حقيقية
- **أنماط متنوعة**: عدة أنماط مختلفة لإنشاء الأسماء
- **عدم التكرار**: ضمان عدم تكرار أسماء المستخدمين
- **عرض شامل**: صفحة خاصة لعرض جميع أسماء المستخدمين
- **نسخ سريع**: نسخ أي اسم مستخدم بضغطة واحدة

#### أنماط أسماء المستخدمين (بدون أرقام):
- **صفة + اسم**: `FastTiger`, `CoolDragon`, `SmartWolf`
- **اسم أول + اسم**: `AlexWolf`, `MikeEagle`, `JohnLion`
- **اسم + صفة**: `TigerFast`, `DragonCool`, `WolfSmart`
- **اسم أول + صفة**: `AlexCool`, `MikeFast`, `JohnSmart`
- **صفتان**: `FastCool`, `SmartBrave`, `EpicWild`
- **اسمان**: `TigerWolf`, `DragonEagle`, `LionPhoenix`
- **اسم واحد**: `Dragon`, `Phoenix`, `Tiger`
- **اسم أول واحد**: `Alex`, `Mike`, `John`

#### طريقة استخدام أسماء المستخدمين:
1. **تلقائياً**: يتم إنشاء اسم مستخدم فريد لكل IP عند الزيارة الأولى
2. **العرض**: يظهر اسم المستخدم في معلومات IP على الصفحة الرئيسية
3. **التتبع**: يمكن استخدام الأسماء لتتبع نشاط المستخدمين
4. **التحليل**: تجميع الإحصائيات حسب أسماء المستخدمين
5. **التخصيص**: إنشاء تجربة شخصية للمستخدمين

#### دليل الاستخدام المفصل:
- **صفحة الدليل**: `/username_guide.php` - شرح شامل لجميع الأنماط وطرق الاستخدام

#### إدارة أسماء المستخدمين:
- **إخفاء العروض**: إخفاء العرض المحدد فقط بعد الضغط عليه (وليس كل العروض)
- **ربط Postback**: ربط اسم المستخدم بالـ Postback للتتبع الدقيق
- **إدارة شاملة**: تعديل وحذف أسماء المستخدمين
- **إحصائيات مفصلة**: عرض الضغطات والتحويلات لكل مستخدم

### � استيراد العروض من CPALead API
- **اتصال مباشر**: استيراد العروض مباشرة من CPALead API
- **اختبار API**: اختبار الاتصال قبل الاستيراد
- **فلترة البلدان**: عرض العروض حسب بلد المستخدم تلقائياً
- **معلومات شاملة**: حفظ البلدان، الجهاز، المبلغ، نوع الدفع
- **منع التكرار**: عدم استيراد العروض الموجودة مسبقاً
- **استيراد تلقائي**: إمكانية جدولة الاستيراد التلقائي عبر Cron
- **إصلاح الترميز**: حل مشاكل UTF8 وترميز قاعدة البيانات
- **سجل العمليات**: تسجيل جميع عمليات الاستيراد والأخطاء

### ��🔗 مولد روابط Postback
- **إنشاء تلقائي**: إنشاء روابط Postback لجميع العروض
- **أنواع مختلفة**: روابط أساسية وبديلة ومخصصة
- **اختبار مدمج**: اختبار الروابط مباشرة من الواجهة
- **نسخ سريع**: نسخ الروابط بضغطة واحدة
- **معاملات مخصصة**: إضافة معاملات إضافية حسب الحاجة

## � استخدام CPALead API

### الحصول على معرف API:
1. سجل الدخول إلى حسابك في CPALead
2. اذهب إلى قسم API أو Developer Tools
3. انسخ معرف API الخاص بك

### استيراد العروض يدوياً:
1. اذهب إلى `/api_manager.php`
2. اختبر API أولاً بإدخال معرف API
3. إذا نجح الاختبار، استخدم نفس المعرف للاستيراد
4. اضغط "استيراد العروض"

### الاستيراد التلقائي:
1. عدّل ملف `auto_import.php` وأضف معرفات API
2. أضف Cron Job لتشغيل الملف دورياً:
```bash
# تشغيل كل ساعة
0 * * * * /usr/bin/php /path/to/your/site/auto_import.php

# تشغيل كل 6 ساعات
0 */6 * * * /usr/bin/php /path/to/your/site/auto_import.php
```

### مثال على رابط API:
```
https://www.cpalead.com/api/offers?id=1941213
```

## 🔧 حل مشاكل الترميز

### مشكلة "Illegal mix of collations":
إذا واجهت خطأ `Illegal mix of collations (latin1_swedish_ci,IMPLICIT) and (utf8mb4_general_ci,COERCIBLE)`:

1. **اذهب إلى**: `/fix_database.php`
2. **تشغيل الإصلاح**: سيتم إصلاح جميع الجداول تلقائياً
3. **التحقق**: فحص حالة الجداول بعد الإصلاح

### ما يتم إصلاحه:
- **تحويل قاعدة البيانات**: إلى UTF8MB4 Unicode
- **إصلاح الجداول**: تحويل جميع الجداول للترميز الصحيح
- **إضافة حقول جديدة**: countries, device, amount, payout_type
- **فحص الحالة**: التأكد من نجاح الإصلاح

### العروض حسب البلد:
- **تلقائياً**: يتم عرض العروض المناسبة لبلد المستخدم
- **فلترة ذكية**: العروض التي تدعم البلد أو بدون تحديد بلد
- **معلومات شاملة**: عرض البلدان المدعومة، الجهاز، المبلغ

## 🌍 إدارة البلدان والترجمة

### ميزات عرض البلدان:
- **ترجمة كاملة**: تحويل أكواد البلدان إلى أسماء عربية مع الأعلام
- **دعم شامل**: أكثر من 150 بلد مدعوم
- **عرض ذكي**: عرض البلدان بشكل مختصر ومنظم
- **إحصائيات مفصلة**: تحليل العروض حسب البلدان

### أمثلة على العرض:
```
🌍 الولايات المتحدة 🇺🇸 • كندا 🇨🇦 • المملكة المتحدة 🇬🇧
🌍 ألمانيا 🇩🇪 • فرنسا 🇫🇷 و 3 بلد آخر
🌍 جميع البلدان (للعروض العامة)
```

### إحصائيات البلدان:
- **أكثر البلدان**: ترتيب البلدان حسب عدد العروض
- **إحصائيات الأجهزة**: توزيع العروض حسب نوع الجهاز
- **أنواع الدفع**: تحليل أنواع الدفع ومتوسط المبالغ
- **جدول تفصيلي**: عرض جميع العروض مع معلوماتها

## � نظام إدارة أسماء المستخدمين المحسن

### إخفاء العروض الذكي:
- **إخفاء محدد**: إخفاء العرض المحدد فقط بعد الضغط عليه
- **مدة الإخفاء**: 15 يوم لكل عرض على حدة
- **عروض أخرى متاحة**: باقي العروض تبقى ظاهرة للمستخدم

### ربط Postback بأسماء المستخدمين:
- **تتبع دقيق**: ربط كل تحويل باسم المستخدم
- **تحليل الأداء**: معرفة أداء كل مستخدم
- **إحصائيات شاملة**: ضغطات وتحويلات لكل مستخدم

### إدارة أسماء المستخدمين:
- **تعديل الأسماء**: تغيير أسماء المستخدمين
- **حذف المستخدمين**: إزالة المستخدمين غير المرغوب فيهم
- **إحصائيات مفصلة**: عرض نشاط كل مستخدم
- **تحديث تلقائي**: تحديث الأسماء في جميع الجداول

### مثال على التتبع:
```
المستخدم: FastTiger
- ضغط على العرض #1: تم إخفاؤه لمدة 15 يوم
- ضغط على العرض #2: تم إخفاؤه لمدة 15 يوم
- العرض #3: لا يزال ظاهراً (لم يضغط عليه)
- تحويل ناجح: العرض #1 - $2.50
```

## � ربط CPALead الشامل

### ربط جميع العروض مرة واحدة:
- **ربط تلقائي**: ربط جميع العروض بحساب CPALead واحد
- **معاملات التتبع**: إضافة معاملات IP واسم المستخدم تلقائياً
- **Postback موحد**: رابط Postback واحد لجميع العروض
- **اختبار شامل**: اختبار Postback مع جميع المعاملات

### خطوات الربط:
1. **اذهب إلى**: `/cpalead_integration.php`
2. **أدخل معرف CPALead**: Publisher ID الخاص بك
3. **ربط العروض**: جميع العروض تصبح مرتبطة بحسابك
4. **إعداد Postback**: إنشاء رابط Postback موحد
5. **إضافة في CPALead**: نسخ الرابط إلى لوحة تحكم CPALead

### مثال على الربط:
```
قبل الربط:
العرض #1: https://example.com/offer1
العرض #2: https://example.com/offer2

بعد الربط:
العرض #1: https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=1&ip={user_ip}&username=FastTiger
العرض #2: https://www.cpalead.com/view.php?id=1941213&pub=1941213&subid=2&ip={user_ip}&username=CoolDragon
```

### رابط Postback الموحد:
```
https://yoursite.com/postback.php?offer_id={subid}&ip={user_ip}&source=cpalead
```

## � المزامنة التلقائية للعروض

### إضافة العروض الجديدة تلقائياً:
- **مراقبة API**: فحص CPALead API بانتظام للعروض الجديدة
- **إضافة تلقائية**: إضافة العروض الجديدة تلقائياً دون تدخل يدوي
- **تحديث العروض**: تحديث معلومات العروض الموجودة
- **تفعيل العروض**: إعادة تفعيل العروض المتوقفة إذا عادت للعمل

### إخفاء العروض المتوقفة:
- **فحص الحالة**: فحص حالة كل عرض عبر HTTP requests
- **كشف التوقف**: اكتشاف العروض المنتهية أو المتوقفة
- **إخفاء تلقائي**: إخفاء العروض المتوقفة من الموقع
- **حفظ البيانات**: الاحتفاظ بالعروض في قاعدة البيانات مع تعليمها كمتوقفة

### إعداد المزامنة:
1. **إضافة معرفات API**: في `/sync_manager.php`
2. **تشغيل يدوي**: اختبار المزامنة يدوياً
3. **إعداد Cron Job**: للتشغيل التلقائي
4. **مراقبة السجلات**: متابعة عمليات المزامنة

### مثال Cron Job:
```bash
# كل 6 ساعات
0 */6 * * * /usr/bin/php /path/to/auto_sync_offers.php

# يومياً في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /path/to/auto_sync_offers.php
```

### ما تفعله المزامنة:
```
1. 🔍 فحص معرفات API المحفوظة
2. 📥 جلب العروض من CPALead API
3. 🆕 إضافة العروض الجديدة
4. ✅ تفعيل العروض المتوقفة إذا عادت
5. 🔍 فحص حالة العروض الموجودة
6. ❌ إيقاف العروض المتوقفة أو المنتهية
7. 📊 تحديث الإحصائيات
8. 📝 كتابة السجلات
```

## � Postback المحسن لـ CPALead

### دعم كامل لجميع معاملات CPALead:
- **معاملات أساسية**: campaign_id, campaign_name, subid, subid2, subid3
- **معاملات التطبيقات**: idfa (Apple), gaid (Google)
- **معاملات المالية**: payout, virtual_currency
- **معاملات التتبع**: ip_address, gateway_id, lead_id, country_iso
- **معاملات الأمان**: password للتحقق من صحة Postback

### الأمان والحماية:
- **فحص IP المصدر**: التحقق من IP CPALead الرسمي (************)
- **كلمة مرور اختيارية**: حماية إضافية للـ Postback
- **تسجيل شامل**: حفظ جميع البيانات والأخطاء في ملفات السجل
- **استجابة JSON**: استجابات منظمة وواضحة

### ربط تلقائي بأسماء المستخدمين:
```
1. 🔍 البحث في الضغطات السابقة للعرض والـ IP
2. 🔍 البحث في الـ IPs المحفوظة
3. 🆕 إنشاء اسم مستخدم جديد إذا لم يوجد
4. 🔗 ربط التحويل باسم المستخدم
```

### مثال رابط Postback كامل:
```
https://yoursite.com/postback_cpalead.php?campaign_id={campaign_id}&campaign_name={campaign_name}&subid={subid}&subid2={subid2}&subid3={subid3}&idfa={idfa}&gaid={gaid}&payout={payout}&ip_address={ip_address}&gateway_id={gateway_id}&lead_id={lead_id}&country_iso={country_iso}&password={password}&virtual_currency={virtual_currency}
```

### مثال رابط Postback أساسي:
```
https://yoursite.com/postback_cpalead.php?subid={subid}&ip_address={ip_address}&payout={payout}
```

### استجابة نجح:
```json
{
    "status": "success",
    "message": "Conversion recorded successfully",
    "data": {
        "conversion_id": 123,
        "offer_id": 45,
        "offer_title": "Mobile App Install",
        "username": "FastTiger",
        "ip_address": "***********00",
        "payout": 2.50,
        "lead_id": "lead_abc123",
        "campaign_id": "camp_456",
        "country_iso": "US",
        "timestamp": "2024-01-15 14:30:00"
    }
}
```

## ����📊 استخدام Postback

### معاملات Postback المدعومة:
- `offer_id` أو `subid`: معرف العرض
- `ip` أو `user_ip`: عنوان IP المستخدم

### أمثلة على روابط Postback:
```
http://yoursite.com/postback.php?offer_id=1&ip=***********
http://yoursite.com/postback.php?subid=2&user_ip=********
```

### استجابة Postback:
```json
{
    "status": "success",
    "message": "Conversion recorded successfully",
    "data": {
        "conversion_id": 1,
        "offer_id": 1,
        "offer_title": "عنوان العرض",
        "ip_address": "***********",
        "timestamp": "2024-01-01 12:00:00"
    }
}
```

## � استخدام مولد Postback

### الوصول لمولد Postback:
اذهب إلى `/postback_generator.php` من لوحة التحكم

### خطوات الاستخدام:
1. **اختيار العروض**: حدد العروض التي تريد إنشاء روابط لها
2. **معاملات مخصصة**: أضف معاملات إضافية (اختياري)
3. **IP الاختبار**: حدد IP للاختبار
4. **إنشاء الروابط**: اضغط "إنشاء روابط Postback"

### أنواع الروابط المُنشأة:
- **رابط أساسي**: `postback.php?offer_id=1&ip={IP}`
- **رابط بديل**: `postback.php?subid=1&user_ip={IP}`
- **رابط مخصص**: مع المعاملات الإضافية
- **رابط اختبار**: للاختبار المباشر

### مميزات المولد:
- **نسخ سريع**: نسخ أي رابط بضغطة واحدد
- **اختبار مباشر**: اختبار الروابط من الواجهة
- **تحديد متعدد**: اختيار عدة عروض مرة واحدة
- **معاينة فورية**: عرض الروابط فور الإنشاء

## �🛡️ قواعد الحماية

### الضغطات:
- لا يمكن للمستخدم الضغط على نفس العرض خلال 15 يوم
- يتم إخفاء العروض التي تم الضغط عليها من الصفحة الرئيسية

### التحويلات:
- لا يمكن تسجيل تحويل مكرر من نفس IP للعرض خلال 24 ساعة
- التحقق من صحة البيانات قبل التسجيل

## � نظام الإشعارات والتوقيت المصري

### إشعارات فورية لجميع الأنشطة:
- **إشعار الضغطات**: عند ضغط المستخدم على أي عرض
- **إشعار التحويلات**: عند نجاح أي تحويل من CPALead
- **إشعار حفظ IP**: عند حفظ IP جديد
- **إشعار العروض الجديدة**: عند إضافة عروض جديدة

### التوقيت المصري:
- **تعيين تلقائي**: جميع الأوقات بتوقيت القاهرة
- **عرض مباشر**: الوقت الحالي في لوحة الإشعارات
- **تحديث مستمر**: تحديث الوقت كل ثانية
- **تنسيق ذكي**: عرض الوقت النسبي (منذ دقيقة، منذ ساعة)

### مميزات لوحة الإشعارات:
```
🔔 عرض جميع الإشعارات مع التفاصيل
📊 إحصائيات شاملة للأنشطة
🕐 التوقيت المصري المباشر
✅ تعليم الإشعارات كمقروءة
🗑️ تنظيف الإشعارات القديمة
🔄 تحديث تلقائي كل 5 دقائق
```

### مثال على الإشعارات:
```
💰 تحويل جديد!
تحويل ناجح للمستخدم FastTiger على العرض: تطبيق موبايل بقيمة $2.50
🎯 العرض: #1 👤 المستخدم: FastTiger 🌐 IP: ***********00 💰 العمولة: $2.50
🕐 2024-01-15 14:30:25

🎯 ضغطة جديدة على عرض
المستخدم CoolDragon ضغط على العرض: استطلاع رأي
🎯 العرض: #2 👤 المستخدم: CoolDragon 🌐 IP: ***********01
🕐 2024-01-15 14:28:15
```

### الوصول للإشعارات:
- **من الصفحة الرئيسية**: زر الإشعارات في الأسفل مع عداد
- **لوحة الإشعارات**: `/notifications_panel.php`
- **تحديث تلقائي**: كل 5 دقائق
- **مؤشر غير مقروءة**: عداد أحمر متحرك

## �📧 مواقع الإيميلات المؤقتة المدعومة
- Temp Mail (temp-mail.org)
- Tempail (tempail.com)
- Temp Mail IO (temp-mail.io)
- Run2Mail (run2mail.com)

## 🔧 التخصيص

### تغيير كلمة المرور:
قم بتعديل السطر التالي في `config.php`:
```php
define('SITE_PASSWORD', 'كلمة_المرور_الجديدة');
```

### إضافة مواقع إيميلات مؤقتة جديدة:
قم بتعديل قسم "مواقع الإيميلات المؤقتة" في `index.php`.

### تخصيص فترات الحماية:
- تغيير فترة منع الضغط المكرر (افتراضي: 15 يوم)
- تغيير فترة منع التحويل المكرر (افتراضي: 24 ساعة)

## 📈 الإحصائيات المتاحة
- إجمالي التحويلات
- عدد عناوين IP الفريدة
- العروض النشطة
- تحويلات اليوم الحالي

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ في الاتصال بقاعدة البيانات**: تحقق من إعدادات `config.php`
2. **عدم ظهور العروض**: تأكد من وجود بيانات في جدول `offers`
3. **مشاكل في Postback**: تحقق من صحة المعاملات المرسلة

### ملفات السجل:
- `postback.log`: سجل طلبات Postback

## 📞 الدعم الفني
لأي استفسارات أو مشاكل، يرجى مراجعة الكود أو التواصل مع المطور.

---

**ملاحظة**: هذا المشروع مخصص للاستخدام التعليمي والتجاري. يرجى التأكد من الامتثال لقوانين بلدك قبل الاستخدام.
