<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// معالجة الإجراءات
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    $offer_id = intval($_POST['offer_id']);
    
    try {
        $conn = getDBConnection();
        
        switch ($action) {
            case 'activate':
                $stmt = $conn->prepare("UPDATE offers SET is_active = 1 WHERE id = ?");
                $stmt->bind_param("i", $offer_id);
                if ($stmt->execute()) {
                    $message = "✅ تم تفعيل العرض بنجاح!";
                    $success = true;
                }
                $stmt->close();
                break;
                
            case 'deactivate':
                $stmt = $conn->prepare("UPDATE offers SET is_active = 0 WHERE id = ?");
                $stmt->bind_param("i", $offer_id);
                if ($stmt->execute()) {
                    $message = "✅ تم إلغاء تفعيل العرض بنجاح!";
                    $success = true;
                }
                $stmt->close();
                break;
                
            case 'delete':
                $stmt = $conn->prepare("DELETE FROM offers WHERE id = ?");
                $stmt->bind_param("i", $offer_id);
                if ($stmt->execute()) {
                    $message = "✅ تم حذف العرض بنجاح!";
                    $success = true;
                }
                $stmt->close();
                break;
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تنفيذ العملية: " . $e->getMessage();
    }
}

// جلب العروض مع الفلترة
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];
$types = "";

if ($filter === 'active') {
    $where_conditions[] = "is_active = 1";
} elseif ($filter === 'inactive') {
    $where_conditions[] = "is_active = 0";
} elseif ($filter === 'manual') {
    $where_conditions[] = "(external_id IS NULL OR external_id = '')";
} elseif ($filter === 'api') {
    $where_conditions[] = "(external_id IS NOT NULL AND external_id != '')";
}

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR offer_url LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ss";
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $conn = getDBConnection();
    
    // عدد العروض الإجمالي
    $count_query = "SELECT COUNT(*) as total FROM offers $where_clause";
    if (!empty($params)) {
        $count_stmt = $conn->prepare($count_query);
        if (!empty($types)) {
            $count_stmt->bind_param($types, ...$params);
        }
        $count_stmt->execute();
        $total_offers = $count_stmt->get_result()->fetch_assoc()['total'];
        $count_stmt->close();
    } else {
        $total_offers = $conn->query($count_query)->fetch_assoc()['total'];
    }
    
    $total_pages = ceil($total_offers / $per_page);
    
    // جلب العروض
    $query = "SELECT id, title, image_url, offer_url, amount, countries, device, is_active, created_at, external_id 
              FROM offers $where_clause 
              ORDER BY created_at DESC 
              LIMIT $per_page OFFSET $offset";
    
    if (!empty($params)) {
        $stmt = $conn->prepare($query);
        if (!empty($types)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $offers = $stmt->get_result();
        $stmt->close();
    } else {
        $offers = $conn->query($query);
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $message = "❌ خطأ في جلب العروض: " . $e->getMessage();
    $offers = null;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العروض - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
        }
        
        .filter-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #333;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-sm {
            padding: 0.3rem 0.8rem;
            font-size: 0.8rem;
        }
        
        .offers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 2rem;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .offers-table th,
        .offers-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .offers-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .offers-table tr:hover {
            background: #f8f9fa;
        }
        
        .offer-image {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .offer-title {
            font-weight: bold;
            color: #333;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .offer-url {
            color: #667eea;
            text-decoration: none;
            font-size: 0.8rem;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
        }
        
        .offer-url:hover {
            text-decoration: underline;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .type-badge {
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .type-manual {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .type-api {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .actions {
            display: flex;
            gap: 0.3rem;
            flex-wrap: wrap;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .offers-table {
                font-size: 0.8rem;
            }
            
            .offers-table th,
            .offers-table td {
                padding: 0.5rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📋 إدارة العروض</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="controls">
            <div class="filter-group">
                <label>الفلتر:</label>
                <select onchange="updateFilter()" id="filter">
                    <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>جميع العروض</option>
                    <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>النشطة</option>
                    <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>غير النشطة</option>
                    <option value="manual" <?php echo $filter === 'manual' ? 'selected' : ''; ?>>اليدوية</option>
                    <option value="api" <?php echo $filter === 'api' ? 'selected' : ''; ?>>من API</option>
                </select>
            </div>
            
            <div class="filter-group">
                <label>البحث:</label>
                <input type="text" id="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="ابحث في العنوان أو الرابط..." onkeypress="handleSearch(event)">
                <button onclick="performSearch()" class="btn btn-sm">🔍</button>
            </div>
            
            <div style="margin-right: auto;">
                <a href="add_manual_offer.php" class="btn btn-success">➕ إضافة عرض جديد</a>
                <a href="bulk_manage_offers.php" class="btn" style="background: #6f42c1;">☑️ الإدارة المتعددة</a>
            </div>
        </div>
        
        <?php if ($offers && $offers->num_rows > 0): ?>
        <table class="offers-table">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>العنوان</th>
                    <th>الرابط</th>
                    <th>المبلغ</th>
                    <th>البلدان</th>
                    <th>الجهاز</th>
                    <th>النوع</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($offer = $offers->fetch_assoc()): ?>
                <tr>
                    <td>
                        <img src="<?php echo htmlspecialchars($offer['image_url']); ?>" 
                             alt="صورة العرض" class="offer-image" 
                             onerror="this.src='https://via.placeholder.com/60x40/667eea/ffffff?text=صورة'">
                    </td>
                    <td>
                        <div class="offer-title" title="<?php echo htmlspecialchars($offer['title']); ?>">
                            <?php echo htmlspecialchars($offer['title']); ?>
                        </div>
                    </td>
                    <td>
                        <a href="<?php echo htmlspecialchars($offer['offer_url']); ?>" 
                           target="_blank" class="offer-url" 
                           title="<?php echo htmlspecialchars($offer['offer_url']); ?>">
                            <?php echo htmlspecialchars($offer['offer_url']); ?>
                        </a>
                    </td>
                    <td>
                        <?php if ($offer['amount'] > 0): ?>
                            <strong>$<?php echo number_format($offer['amount'], 2); ?></strong>
                        <?php else: ?>
                            <span style="color: #999;">غير محدد</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php echo !empty($offer['countries']) ? htmlspecialchars($offer['countries']) : 'جميع البلدان'; ?>
                    </td>
                    <td>
                        <?php echo htmlspecialchars($offer['device']); ?>
                    </td>
                    <td>
                        <span class="type-badge <?php echo empty($offer['external_id']) ? 'type-manual' : 'type-api'; ?>">
                            <?php echo empty($offer['external_id']) ? 'يدوي' : 'API'; ?>
                        </span>
                    </td>
                    <td>
                        <span class="status-badge <?php echo $offer['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                            <?php echo $offer['is_active'] ? 'نشط' : 'غير نشط'; ?>
                        </span>
                    </td>
                    <td>
                        <?php echo date('Y-m-d H:i', strtotime($offer['created_at'])); ?>
                    </td>
                    <td>
                        <div class="actions">
                            <a href="edit_offer.php?id=<?php echo $offer['id']; ?>" class="btn btn-sm" title="تحرير العرض">✏️</a>

                            <?php if ($offer['is_active']): ?>
                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من إلغاء تفعيل هذا العرض؟')">
                                    <input type="hidden" name="action" value="deactivate">
                                    <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                    <button type="submit" class="btn btn-warning btn-sm" title="إلغاء التفعيل">⏸️</button>
                                </form>
                            <?php else: ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="activate">
                                    <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                    <button type="submit" class="btn btn-success btn-sm" title="تفعيل">▶️</button>
                                </form>
                            <?php endif; ?>

                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا العرض؟ هذا الإجراء لا يمكن التراجع عنه!')">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="offer_id" value="<?php echo $offer['id']; ?>">
                                <button type="submit" class="btn btn-danger btn-sm" title="حذف">🗑️</button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endwhile; ?>
            </tbody>
        </table>
        
        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">السابق</a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <?php if ($i == $page): ?>
                    <span class="current"><?php echo $i; ?></span>
                <?php else: ?>
                    <a href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                <?php endif; ?>
            <?php endfor; ?>
            
            <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">التالي</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <div style="text-align: center; padding: 3rem; color: #666;">
            <h3>لا توجد عروض</h3>
            <p>لم يتم العثور على عروض تطابق المعايير المحددة.</p>
            <a href="add_manual_offer.php" class="btn btn-success" style="margin-top: 1rem;">➕ إضافة أول عرض</a>
        </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="add_manual_offer.php">➕ إضافة عرض جديد</a>
            <a href="featured_offers.php">⭐ العروض المميزة</a>
            <a href="comprehensive_sync.php">🌟 المزامنة الشاملة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
    
    <script>
        function updateFilter() {
            const filter = document.getElementById('filter').value;
            const search = document.getElementById('search').value;
            window.location.href = `?filter=${filter}&search=${encodeURIComponent(search)}`;
        }
        
        function performSearch() {
            const filter = document.getElementById('filter').value;
            const search = document.getElementById('search').value;
            window.location.href = `?filter=${filter}&search=${encodeURIComponent(search)}`;
        }
        
        function handleSearch(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }
    </script>
</body>
</html>
