<?php
require_once 'config.php';
require_once 'notifications.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();

// الحصول على التحويلات مع تفاصيل العروض وجميع معاملات CPALead
$stmt = $conn->prepare("
    SELECT c.*, o.title as offer_title
    FROM conversions c
    JOIN offers o ON c.offer_id = o.id
    ORDER BY c.converted_at DESC
    LIMIT 100
");
$stmt->execute();
$conversions = $stmt->get_result();

// إحصائيات سريعة
$stats_stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_conversions,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT offer_id) as active_offers
    FROM conversions
");
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// التحويلات اليوم
$today_stmt = $conn->prepare("
    SELECT COUNT(*) as today_conversions 
    FROM conversions 
    WHERE DATE(converted_at) = CURDATE()
");
$today_stmt->execute();
$today_stats = $today_stmt->get_result()->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحويلات - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .stat-card .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .conversions-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .conversions-table h2 {
            padding: 1.5rem;
            background: #f8f9fa;
            color: #333;
            margin: 0;
        }
        
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .ip-address {
            font-family: monospace;
            background: #f1f3f4;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.9rem;
        }
        
        .conversion-time {
            color: #666;
            font-size: 0.9rem;
        }
        
        .offer-title {
            font-weight: bold;
            color: #333;
        }
        
        .no-conversions {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .no-conversions h3 {
            margin-bottom: 1rem;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 1rem;
            border: none;
            border-radius: 50%;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: transform 0.2s;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            th, td {
                padding: 0.5rem;
                font-size: 0.9rem;
            }
            
            .stat-card .stat-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 التحويلات</h1>
            <p>عرض جميع التحويلات الناجحة</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="saved_ips.php">💾 الـ IPs المحفوظة</a>
                <a href="usernames.php">👤 أسماء المستخدمين</a>
                <a href="postback_generator.php">🔗 مولد Postback</a>
                <a href="api_manager.php">🔌 إدارة API</a>
            </div>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>📈 إجمالي التحويلات</h3>
                <div class="stat-number"><?php echo number_format($stats['total_conversions']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>👥 عناوين IP فريدة</h3>
                <div class="stat-number"><?php echo number_format($stats['unique_ips']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>🎯 العروض النشطة</h3>
                <div class="stat-number"><?php echo number_format($stats['active_offers']); ?></div>
            </div>
            
            <div class="stat-card">
                <h3>📅 تحويلات اليوم</h3>
                <div class="stat-number"><?php echo number_format($today_stats['today_conversions']); ?></div>
            </div>
        </div>
        
        <div class="conversions-table">
            <h2>📋 سجل التحويلات</h2>
            <div class="table-responsive">
                <?php if ($conversions->num_rows > 0): ?>
                    <table>
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>اسم المستخدم</th>
                                <th>عنوان IP</th>
                                <th>اسم العرض</th>
                                <th>العمولة</th>
                                <th>البلد</th>
                                <th>Lead ID</th>
                                <th>المصدر</th>
                                <th>تاريخ التحويل</th>
                                <th>التفاصيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($conversion = $conversions->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $conversion['id']; ?></td>
                                    <td>
                                        <span style="font-weight: bold; color: #667eea;">
                                            <?php echo $conversion['username'] ? htmlspecialchars($conversion['username']) : 'غير محدد'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="ip-address"><?php echo htmlspecialchars($conversion['ip_address']); ?></span>
                                    </td>
                                    <td>
                                        <span class="offer-title"><?php echo htmlspecialchars(substr($conversion['offer_title'], 0, 30)); ?>...</span>
                                    </td>
                                    <td>
                                        <?php if (isset($conversion['payout']) && $conversion['payout'] > 0): ?>
                                            <span style="background: #ffc107; color: #333; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">
                                                $<?php echo number_format($conversion['payout'], 2); ?>
                                            </span>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($conversion['country_iso']) && $conversion['country_iso']): ?>
                                            <span style="background: #17a2b8; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                                <?php echo htmlspecialchars($conversion['country_iso']); ?>
                                            </span>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (isset($conversion['lead_id']) && $conversion['lead_id']): ?>
                                            <span style="font-family: monospace; background: #6c757d; color: white; padding: 0.2rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                                                <?php echo htmlspecialchars(substr($conversion['lead_id'], 0, 10)); ?>...
                                            </span>
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span style="background: #28a745; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                            <?php echo $conversion['source'] ? htmlspecialchars($conversion['source']) : 'مباشر'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="conversion-time">
                                            <?php echo formatTime12Hour($conversion['converted_at']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button onclick="showDetails(<?php echo $conversion['id']; ?>)" style="background: #667eea; color: white; border: none; padding: 0.3rem 0.6rem; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                                            📋 تفاصيل
                                        </button>
                                    </td>
                                </tr>

                                <!-- صف التفاصيل المخفي -->
                                <tr id="details-<?php echo $conversion['id']; ?>" style="display: none;">
                                    <td colspan="10" style="background: #f8f9fa; padding: 1rem;">
                                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                                            <?php if (isset($conversion['campaign_id']) && $conversion['campaign_id']): ?>
                                                <div><strong>Campaign ID:</strong> <?php echo htmlspecialchars($conversion['campaign_id']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['campaign_name']) && $conversion['campaign_name']): ?>
                                                <div><strong>Campaign Name:</strong> <?php echo htmlspecialchars($conversion['campaign_name']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['subid']) && $conversion['subid']): ?>
                                                <div><strong>SubID:</strong> <?php echo htmlspecialchars($conversion['subid']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['subid2']) && $conversion['subid2']): ?>
                                                <div><strong>SubID2:</strong> <?php echo htmlspecialchars($conversion['subid2']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['subid3']) && $conversion['subid3']): ?>
                                                <div><strong>SubID3:</strong> <?php echo htmlspecialchars($conversion['subid3']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['idfa']) && $conversion['idfa']): ?>
                                                <div><strong>IDFA:</strong> <?php echo htmlspecialchars($conversion['idfa']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['gaid']) && $conversion['gaid']): ?>
                                                <div><strong>GAID:</strong> <?php echo htmlspecialchars($conversion['gaid']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['gateway_id']) && $conversion['gateway_id']): ?>
                                                <div><strong>Gateway ID:</strong> <?php echo htmlspecialchars($conversion['gateway_id']); ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['virtual_currency']) && $conversion['virtual_currency'] > 0): ?>
                                                <div><strong>Virtual Currency:</strong> <?php echo $conversion['virtual_currency']; ?></div>
                                            <?php endif; ?>
                                            <?php if (isset($conversion['user_agent']) && $conversion['user_agent']): ?>
                                                <div><strong>User Agent:</strong> <?php echo htmlspecialchars(substr($conversion['user_agent'], 0, 100)); ?>...</div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="no-conversions">
                        <h3>😔 لا توجد تحويلات حتى الآن</h3>
                        <p>لم يتم تسجيل أي تحويلات ناجحة بعد.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="location.reload()" title="تحديث الصفحة">
        🔄
    </button>

    <script>
        function showDetails(conversionId) {
            const detailsRow = document.getElementById('details-' + conversionId);
            if (detailsRow.style.display === 'none') {
                detailsRow.style.display = 'table-row';
            } else {
                detailsRow.style.display = 'none';
            }
        }
    </script>
</body>
</html>

<?php
$stmt->close();
$stats_stmt->close();
$today_stmt->close();
$conn->close();
?>
