<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$conn = getDBConnection();
$message = '';

// معالجة إضافة عروض تجريبية
if (isset($_POST['add_test_offers'])) {
    $test_offers = [
        [
            'title' => 'عرض تجريبي #1 - تطبيق موبايل',
            'description' => 'حمل تطبيق الألعاب الجديد واحصل على مكافآت يومية! تطبيق مجاني مع إعلانات، يحتوي على ألعاب متنوعة ومسابقات شيقة. متوافق مع جميع أجهزة الأندرويد والآيفون.',
            'image_url' => 'https://via.placeholder.com/300x200/4CAF50/white?text=Mobile+App',
            'offer_url' => 'https://example.com/mobile-app-offer',
            'countries' => 'US,CA,UK,AU',
            'device' => 'mobile',
            'amount' => 2.50,
            'payout_type' => 'CPI'
        ],
        [
            'title' => 'عرض تجريبي #2 - استطلاع رأي',
            'description' => 'شارك برأيك في استطلاع سريع حول المنتجات الاستهلاكية واربح نقاط يمكن تحويلها لأموال حقيقية. الاستطلاع يستغرق 5-10 دقائق فقط ومتاح للجميع.',
            'image_url' => 'https://via.placeholder.com/300x200/2196F3/white?text=Survey',
            'offer_url' => 'https://example.com/survey-offer',
            'countries' => 'US,UK,DE,FR',
            'device' => 'desktop',
            'amount' => 1.75,
            'payout_type' => 'CPL'
        ],
        [
            'title' => 'عرض تجريبي #3 - لعبة مجانية',
            'description' => 'استمتع بلعبة الأكشن الجديدة مجاناً! لعبة ثلاثية الأبعاد مع جرافيك عالي الجودة ومؤثرات صوتية رائعة. تحديات يومية ومكافآت حصرية للاعبين الجدد.',
            'image_url' => 'https://via.placeholder.com/300x200/FF9800/white?text=Free+Game',
            'offer_url' => 'https://example.com/game-offer',
            'countries' => 'US,CA,AU,NZ',
            'device' => 'mobile',
            'amount' => 3.00,
            'payout_type' => 'CPI'
        ],
        [
            'title' => 'عرض تجريبي #4 - تسجيل مجاني',
            'description' => 'سجل مجاناً في منصة التداول الجديدة واحصل على بونص ترحيبي 50 دولار! منصة آمنة ومرخصة مع أدوات تداول متقدمة وتحليلات السوق المباشرة.',
            'image_url' => 'https://via.placeholder.com/300x200/9C27B0/white?text=Free+Registration',
            'offer_url' => 'https://example.com/registration-offer',
            'countries' => 'US,UK,CA,AU,DE',
            'device' => 'all',
            'amount' => 1.25,
            'payout_type' => 'CPL'
        ],
        [
            'title' => 'عرض تجريبي #5 - تطبيق تسوق',
            'description' => 'تطبيق التسوق الذكي مع خصومات حصرية تصل إلى 70%! آلاف المنتجات من أشهر الماركات العالمية، توصيل مجاني، وضمان استرداد الأموال خلال 30 يوم.',
            'image_url' => 'https://via.placeholder.com/300x200/E91E63/white?text=Shopping+App',
            'offer_url' => 'https://example.com/shopping-offer',
            'countries' => 'US,UK,FR,DE,IT',
            'device' => 'mobile',
            'amount' => 4.00,
            'payout_type' => 'CPI'
        ]
    ];
    
    $added_count = 0;
    
    foreach ($test_offers as $offer) {
        // التحقق من عدم وجود العرض مسبقاً
        $checkStmt = $conn->prepare("SELECT id FROM offers WHERE title = ?");
        $checkStmt->bind_param("s", $offer['title']);
        $checkStmt->execute();
        $existing = $checkStmt->get_result();
        
        if ($existing->num_rows == 0) {
            // إضافة العرض باستخدام الدالة الآمنة
            $insert_id = safeInsertOffer(
                $offer['title'],
                $offer['description'],
                $offer['image_url'],
                $offer['offer_url'],
                $offer['countries'],
                $offer['device'],
                $offer['amount'],
                $offer['payout_type'],
                1
            );

            if ($insert_id) {
                $added_count++;
            }
        }
        $checkStmt->close();
    }
    
    if ($added_count > 0) {
        $message = '<div class="alert alert-success">✅ تم إضافة ' . $added_count . ' عرض تجريبي بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-info">ℹ️ جميع العروض التجريبية موجودة مسبقاً.</div>';
    }
}

// معالجة حذف العروض التجريبية
if (isset($_POST['remove_test_offers'])) {
    $deleteStmt = $conn->prepare("DELETE FROM offers WHERE title LIKE 'عرض تجريبي%'");
    if ($deleteStmt->execute()) {
        $deleted_count = $deleteStmt->affected_rows;
        $message = '<div class="alert alert-success">✅ تم حذف ' . $deleted_count . ' عرض تجريبي.</div>';
    } else {
        $message = '<div class="alert alert-error">❌ فشل في حذف العروض التجريبية.</div>';
    }
    $deleteStmt->close();
}

// الحصول على إحصائيات العروض
$stats = $conn->query("
    SELECT 
        COUNT(*) as total_offers,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_offers,
        COUNT(CASE WHEN title LIKE 'عرض تجريبي%' THEN 1 END) as test_offers
    FROM offers
")->fetch_assoc();

// الحصول على العروض التجريبية الموجودة
$test_offers_query = $conn->query("
    SELECT id, title, amount, payout_type, device, countries, is_active 
    FROM offers 
    WHERE title LIKE 'عرض تجريبي%' 
    ORDER BY id ASC
");
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عروض تجريبية - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .nav-links {
            margin-top: 1rem;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stats-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 1.5rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: background 0.3s;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .offers-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .offers-table h3 {
            background: #667eea;
            color: white;
            padding: 1rem;
            margin: 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-active {
            background: #28a745;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }
        
        .info-box h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.3rem;
        }
        
        @media (max-width: 768px) {
            .nav-links a {
                display: block;
                margin: 0.25rem 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 إضافة عروض تجريبية</h1>
            <p>إضافة عروض وهمية لاختبار نظام Postback</p>
            <div class="nav-links">
                <a href="index.php">🏠 الصفحة الرئيسية</a>
                <a href="admin.php">⚙️ لوحة التحكم</a>
                <a href="postback_wizard.php">🧙‍♂️ معالج Postback</a>
                <a href="cpalead_simulator.php">🎭 محاكي CPALead</a>
                <a href="offers_manager.php">📋 إدارة العروض</a>
            </div>
        </div>
        
        <?php echo $message; ?>
        
        <!-- إحصائيات العروض -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['total_offers']; ?></div>
                <div class="stats-label">📊 إجمالي العروض</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['active_offers']; ?></div>
                <div class="stats-label">✅ عروض نشطة</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $stats['test_offers']; ?></div>
                <div class="stats-label">🧪 عروض تجريبية</div>
            </div>
        </div>
        
        <!-- إضافة عروض تجريبية -->
        <div class="form-section">
            <h3>🧪 إضافة عروض تجريبية</h3>
            
            <div class="info-box">
                <h4>📋 ما هي العروض التجريبية؟</h4>
                <ul>
                    <li>عروض وهمية لاختبار نظام Postback</li>
                    <li>تحتوي على معرفات صحيحة للاختبار</li>
                    <li>آمنة للاستخدام ولا تؤثر على البيانات الحقيقية</li>
                    <li>يمكن حذفها بسهولة بعد الانتهاء من الاختبار</li>
                </ul>
            </div>
            
            <div class="info-box">
                <h4>🎯 العروض التي سيتم إضافتها:</h4>
                <ul>
                    <li><strong>عرض #1:</strong> تطبيق موبايل - $2.50 CPI</li>
                    <li><strong>عرض #2:</strong> استطلاع رأي - $1.75 CPL</li>
                    <li><strong>عرض #3:</strong> لعبة مجانية - $3.00 CPI</li>
                    <li><strong>عرض #4:</strong> تسجيل مجاني - $1.25 CPL</li>
                    <li><strong>عرض #5:</strong> تطبيق تسوق - $4.00 CPI</li>
                </ul>
            </div>
            
            <form method="POST" style="display: inline;">
                <button type="submit" name="add_test_offers" class="btn btn-success">🧪 إضافة عروض تجريبية</button>
            </form>
            
            <?php if ($stats['test_offers'] > 0): ?>
            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف جميع العروض التجريبية؟')">
                <button type="submit" name="remove_test_offers" class="btn btn-danger">🗑️ حذف العروض التجريبية</button>
            </form>
            <?php endif; ?>
        </div>
        
        <!-- عرض العروض التجريبية -->
        <?php if ($test_offers_query->num_rows > 0): ?>
        <div class="offers-table">
            <h3>🧪 العروض التجريبية الموجودة</h3>
            <table>
                <thead>
                    <tr>
                        <th>المعرف</th>
                        <th>العنوان</th>
                        <th>المبلغ</th>
                        <th>النوع</th>
                        <th>الجهاز</th>
                        <th>البلدان</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($offer = $test_offers_query->fetch_assoc()): ?>
                        <tr>
                            <td><strong><?php echo $offer['id']; ?></strong></td>
                            <td><?php echo htmlspecialchars($offer['title']); ?></td>
                            <td>
                                <span style="background: #ffc107; color: #333; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem; font-weight: bold;">
                                    $<?php echo $offer['amount']; ?> <?php echo $offer['payout_type']; ?>
                                </span>
                            </td>
                            <td><?php echo $offer['payout_type']; ?></td>
                            <td>
                                <span style="background: #17a2b8; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">
                                    <?php echo ucfirst($offer['device']); ?>
                                </span>
                            </td>
                            <td style="font-size: 0.8rem;"><?php echo $offer['countries']; ?></td>
                            <td>
                                <?php if ($offer['is_active']): ?>
                                    <span class="status-active">نشط</span>
                                <?php else: ?>
                                    <span style="background: #dc3545; color: white; padding: 0.2rem 0.5rem; border-radius: 12px; font-size: 0.8rem;">متوقف</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
        
        <!-- خطوات الاختبار -->
        <div class="form-section">
            <h3>🧪 خطوات اختبار Postback</h3>
            
            <div class="info-box">
                <h4>📋 بعد إضافة العروض التجريبية:</h4>
                <ol style="margin-right: 1.5rem;">
                    <li><strong>اذهب إلى محاكي CPALead:</strong> <a href="cpalead_simulator.php">🎭 محاكي CPALead</a></li>
                    <li><strong>اختر عرض تجريبي</strong> من القائمة المنسدلة</li>
                    <li><strong>أدخل رابط Postback</strong> الخاص بك</li>
                    <li><strong>شغّل المحاكاة</strong> واختبر النتائج</li>
                    <li><strong>راجع التحويلات:</strong> <a href="conversions.php">📊 صفحة التحويلات</a></li>
                </ol>
            </div>
            
            <div class="info-box">
                <h4>🎯 مثال رابط Postback للاختبار:</h4>
                <code style="background: #f8f9fa; padding: 0.5rem; border-radius: 3px; display: block; margin-top: 0.5rem;">
                    <?php echo $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>/postback_cpalead.php?subid={subid}&ip_address={ip_address}&payout={payout}
                </code>
            </div>
        </div>
    </div>
</body>
</html>

<?php $conn->close(); ?>
