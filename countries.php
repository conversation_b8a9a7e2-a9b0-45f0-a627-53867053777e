<?php
/**
 * ملف إدارة أسماء البلدان
 * يحتوي على قاموس تحويل أكواد البلدان إلى أسماء عربية مع الأعلام
 */

// قاموس أكواد البلدان مع الأسماء العربية والأعلام
function getCountryMap() {
    return [
        // أمريكا الشمالية
        'US' => 'الولايات المتحدة 🇺🇸',
        'CA' => 'كندا 🇨🇦',
        'MX' => 'المكسيك 🇲🇽',
        
        // أوروبا
        'GB' => 'المملكة المتحدة 🇬🇧',
        'DE' => 'ألمانيا 🇩🇪',
        'FR' => 'فرنسا 🇫🇷',
        'IT' => 'إيطاليا 🇮🇹',
        'ES' => 'إسبانيا 🇪🇸',
        'NL' => 'هولندا 🇳🇱',
        'SE' => 'السويد 🇸🇪',
        'NO' => 'النرويج 🇳🇴',
        'DK' => 'الدنمارك 🇩🇰',
        'FI' => 'فنلندا 🇫🇮',
        'IE' => 'أيرلندا 🇮🇪',
        'AT' => 'النمسا 🇦🇹',
        'CH' => 'سويسرا 🇨🇭',
        'BE' => 'بلجيكا 🇧🇪',
        'PT' => 'البرتغال 🇵🇹',
        'GR' => 'اليونان 🇬🇷',
        'PL' => 'بولندا 🇵🇱',
        'CZ' => 'التشيك 🇨🇿',
        'HU' => 'المجر 🇭🇺',
        'RO' => 'رومانيا 🇷🇴',
        'BG' => 'بلغاريا 🇧🇬',
        'HR' => 'كرواتيا 🇭🇷',
        'SK' => 'سلوفاكيا 🇸🇰',
        'SI' => 'سلوفينيا 🇸🇮',
        'LT' => 'ليتوانيا 🇱🇹',
        'LV' => 'لاتفيا 🇱🇻',
        'EE' => 'إستونيا 🇪🇪',
        'RU' => 'روسيا 🇷🇺',
        'UA' => 'أوكرانيا 🇺🇦',
        'BY' => 'بيلاروسيا 🇧🇾',
        
        // آسيا والمحيط الهادئ
        'AU' => 'أستراليا 🇦🇺',
        'NZ' => 'نيوزيلندا 🇳🇿',
        'JP' => 'اليابان 🇯🇵',
        'KR' => 'كوريا الجنوبية 🇰🇷',
        'CN' => 'الصين 🇨🇳',
        'TW' => 'تايوان 🇹🇼',
        'HK' => 'هونغ كونغ 🇭🇰',
        'SG' => 'سنغافورة 🇸🇬',
        'MY' => 'ماليزيا 🇲🇾',
        'TH' => 'تايلاند 🇹🇭',
        'PH' => 'الفلبين 🇵🇭',
        'ID' => 'إندونيسيا 🇮🇩',
        'VN' => 'فيتنام 🇻🇳',
        'IN' => 'الهند 🇮🇳',
        'PK' => 'باكستان 🇵🇰',
        'BD' => 'بنغلاديش 🇧🇩',
        'LK' => 'سريلانكا 🇱🇰',
        'MM' => 'ميانمار 🇲🇲',
        'KH' => 'كمبوديا 🇰🇭',
        'LA' => 'لاوس 🇱🇦',
        'NP' => 'نيبال 🇳🇵',
        'BT' => 'بوتان 🇧🇹',
        'MN' => 'منغوليا 🇲🇳',
        'KZ' => 'كازاخستان 🇰🇿',
        'UZ' => 'أوزبكستان 🇺🇿',
        'KG' => 'قيرغيزستان 🇰🇬',
        'TJ' => 'طاجيكستان 🇹🇯',
        'TM' => 'تركمانستان 🇹🇲',
        'AF' => 'أفغانستان 🇦🇫',
        'IR' => 'إيران 🇮🇷',
        
        // الشرق الأوسط
        'TR' => 'تركيا 🇹🇷',
        'IL' => 'إسرائيل 🇮🇱',
        'PS' => 'فلسطين 🇵🇸',
        'LB' => 'لبنان 🇱🇧',
        'SY' => 'سوريا 🇸🇾',
        'JO' => 'الأردن 🇯🇴',
        'IQ' => 'العراق 🇮🇶',
        'KW' => 'الكويت 🇰🇼',
        'SA' => 'السعودية 🇸🇦',
        'AE' => 'الإمارات 🇦🇪',
        'QA' => 'قطر 🇶🇦',
        'BH' => 'البحرين 🇧🇭',
        'OM' => 'عُمان 🇴🇲',
        'YE' => 'اليمن 🇾🇪',
        'GE' => 'جورجيا 🇬🇪',
        'AM' => 'أرمينيا 🇦🇲',
        'AZ' => 'أذربيجان 🇦🇿',
        'CY' => 'قبرص 🇨🇾',
        
        // أفريقيا
        'EG' => 'مصر 🇪🇬',
        'ZA' => 'جنوب أفريقيا 🇿🇦',
        'NG' => 'نيجيريا 🇳🇬',
        'KE' => 'كينيا 🇰🇪',
        'ET' => 'إثيوبيا 🇪🇹',
        'GH' => 'غانا 🇬🇭',
        'TZ' => 'تنزانيا 🇹🇿',
        'UG' => 'أوغندا 🇺🇬',
        'RW' => 'رواندا 🇷🇼',
        'MA' => 'المغرب 🇲🇦',
        'DZ' => 'الجزائر 🇩🇿',
        'TN' => 'تونس 🇹🇳',
        'LY' => 'ليبيا 🇱🇾',
        'SD' => 'السودان 🇸🇩',
        'SS' => 'جنوب السودان 🇸🇸',
        'DJ' => 'جيبوتي 🇩🇯',
        'SO' => 'الصومال 🇸🇴',
        'ER' => 'إريتريا 🇪🇷',
        'ZW' => 'زيمبابوي 🇿🇼',
        'BW' => 'بوتسوانا 🇧🇼',
        'NA' => 'ناميبيا 🇳🇦',
        'ZM' => 'زامبيا 🇿🇲',
        'MW' => 'مالاوي 🇲🇼',
        'MZ' => 'موزمبيق 🇲🇿',
        'MG' => 'مدغشقر 🇲🇬',
        'MU' => 'موريشيوس 🇲🇺',
        'SC' => 'سيشل 🇸🇨',
        
        // أمريكا الجنوبية
        'BR' => 'البرازيل 🇧🇷',
        'AR' => 'الأرجنتين 🇦🇷',
        'CL' => 'تشيلي 🇨🇱',
        'CO' => 'كولومبيا 🇨🇴',
        'PE' => 'بيرو 🇵🇪',
        'VE' => 'فنزويلا 🇻🇪',
        'EC' => 'الإكوادور 🇪🇨',
        'BO' => 'بوليفيا 🇧🇴',
        'PY' => 'باراغواي 🇵🇾',
        'UY' => 'أوروغواي 🇺🇾',
        'GY' => 'غيانا 🇬🇾',
        'SR' => 'سورينام 🇸🇷',
        'GF' => 'غيانا الفرنسية 🇬🇫',
        
        // أمريكا الوسطى والكاريبي
        'GT' => 'غواتيمالا 🇬🇹',
        'BZ' => 'بليز 🇧🇿',
        'SV' => 'السلفادور 🇸🇻',
        'HN' => 'هندوراس 🇭🇳',
        'NI' => 'نيكاراغوا 🇳🇮',
        'CR' => 'كوستاريكا 🇨🇷',
        'PA' => 'بنما 🇵🇦',
        'CU' => 'كوبا 🇨🇺',
        'JM' => 'جامايكا 🇯🇲',
        'HT' => 'هايتي 🇭🇹',
        'DO' => 'جمهورية الدومينيكان 🇩🇴',
        'PR' => 'بورتوريكو 🇵🇷',
        'TT' => 'ترينيداد وتوباغو 🇹🇹',
        'BB' => 'بربادوس 🇧🇧',
        'BS' => 'الباهاما 🇧🇸',
        
        // أوقيانوسيا
        'FJ' => 'فيجي 🇫🇯',
        'PG' => 'بابوا غينيا الجديدة 🇵🇬',
        'SB' => 'جزر سليمان 🇸🇧',
        'VU' => 'فانواتو 🇻🇺',
        'NC' => 'كاليدونيا الجديدة 🇳🇨',
        'PF' => 'بولينيزيا الفرنسية 🇵🇫',
        'WS' => 'ساموا 🇼🇸',
        'TO' => 'تونغا 🇹🇴',
        'KI' => 'كيريباتي 🇰🇮',
        'TV' => 'توفالو 🇹🇻',
        'NR' => 'ناورو 🇳🇷',
        'PW' => 'بالاو 🇵🇼',
        'FM' => 'ميكرونيزيا 🇫🇲',
        'MH' => 'جزر مارشال 🇲🇭'
    ];
}

/**
 * تحويل كود البلد إلى اسم عربي مع العلم
 */
function getCountryName($countryCode) {
    $countryMap = getCountryMap();
    return $countryMap[$countryCode] ?? $countryCode . ' 🌍';
}

/**
 * تحويل قائمة أكواد البلدان إلى أسماء عربية
 */
function getCountryNames($countryCodes) {
    if (empty($countryCodes)) {
        return [];
    }
    
    if (is_string($countryCodes)) {
        $countryCodes = explode(',', $countryCodes);
    }
    
    $countryNames = [];
    foreach ($countryCodes as $code) {
        $code = trim($code);
        if (!empty($code)) {
            $countryNames[] = getCountryName($code);
        }
    }
    
    return $countryNames;
}

/**
 * تنسيق قائمة البلدان للعرض
 */
function formatCountriesDisplay($countries, $maxDisplay = 4) {
    if (empty($countries)) {
        return 'جميع البلدان';
    }
    
    $countryNames = getCountryNames($countries);
    
    if (count($countryNames) <= $maxDisplay) {
        return implode(' • ', $countryNames);
    } else {
        $displayed = array_slice($countryNames, 0, $maxDisplay - 1);
        $remaining = count($countryNames) - ($maxDisplay - 1);
        return implode(' • ', $displayed) . ' و ' . $remaining . ' بلد آخر';
    }
}

/**
 * فحص ما إذا كان البلد مدعوماً في قائمة البلدان
 */
function isCountrySupported($userCountry, $offerCountries) {
    if (empty($offerCountries)) {
        return true; // العرض متاح لجميع البلدان
    }
    
    if (is_string($offerCountries)) {
        $offerCountries = explode(',', $offerCountries);
    }
    
    foreach ($offerCountries as $country) {
        if (trim($country) === $userCountry) {
            return true;
        }
    }
    
    return false;
}

/**
 * الحصول على قائمة البلدان الأكثر شيوعاً في CPA
 */
function getPopularCPACountries() {
    return [
        'US' => 'الولايات المتحدة 🇺🇸',
        'CA' => 'كندا 🇨🇦',
        'GB' => 'المملكة المتحدة 🇬🇧',
        'AU' => 'أستراليا 🇦🇺',
        'DE' => 'ألمانيا 🇩🇪',
        'FR' => 'فرنسا 🇫🇷',
        'IT' => 'إيطاليا 🇮🇹',
        'ES' => 'إسبانيا 🇪🇸',
        'NL' => 'هولندا 🇳🇱',
        'SE' => 'السويد 🇸🇪',
        'NO' => 'النرويج 🇳🇴',
        'DK' => 'الدنمارك 🇩🇰',
        'FI' => 'فنلندا 🇫🇮',
        'IE' => 'أيرلندا 🇮🇪',
        'NZ' => 'نيوزيلندا 🇳🇿',
        'JP' => 'اليابان 🇯🇵',
        'KR' => 'كوريا الجنوبية 🇰🇷',
        'SG' => 'سنغافورة 🇸🇬',
        'HK' => 'هونغ كونغ 🇭🇰'
    ];
}
?>
