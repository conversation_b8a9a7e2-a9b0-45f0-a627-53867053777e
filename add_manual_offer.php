<?php
require_once 'config.php';
require_once 'safe_description_functions.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// معالجة إضافة العرض
if (isset($_POST['add_offer'])) {
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $image_url = trim($_POST['image_url']);
    $offer_url = trim($_POST['offer_url']);
    $countries = trim($_POST['countries']);
    $device = trim($_POST['device']);
    $amount = floatval($_POST['amount']);
    $payout_type = trim($_POST['payout_type']);
    
    // التحقق من البيانات المطلوبة
    if (empty($title)) {
        $message = "❌ عنوان العرض مطلوب!";
    } elseif (empty($offer_url)) {
        $message = "❌ رابط العرض مطلوب!";
    } elseif (!filter_var($offer_url, FILTER_VALIDATE_URL)) {
        $message = "❌ رابط العرض غير صحيح!";
    } else {
        // إنشاء صورة افتراضية إذا لم يتم توفير صورة
        if (empty($image_url)) {
            $encodedTitle = urlencode(substr($title, 0, 30));
            $colors = ['667eea', '764ba2', 'f093fb', 'f5576c', '4facfe', '00f2fe', '43e97b', '38f9d7'];
            $bgColor = $colors[array_rand($colors)];
            $image_url = "https://via.placeholder.com/400x250/{$bgColor}/ffffff?text=" . $encodedTitle;
        }
        
        // التحقق من صحة رابط الصورة
        if (!filter_var($image_url, FILTER_VALIDATE_URL)) {
            $encodedTitle = urlencode(substr($title, 0, 30));
            $image_url = "https://via.placeholder.com/400x250/667eea/ffffff?text=" . $encodedTitle;
        }
        
        try {
            // إضافة العرض إلى قاعدة البيانات
            $insert_id = safeInsertOffer(
                $title,
                $description,
                $image_url,
                $offer_url,
                $countries,
                $device,
                $amount,
                $payout_type,
                1 // نشط
            );
            
            if ($insert_id) {
                $success = true;
                $message = "✅ تم إضافة العرض بنجاح! معرف العرض: $insert_id";
                
                // إضافة إشعار
                if (function_exists('addNotification')) {
                    addNotification(
                        'manual_offer',
                        'تم إضافة عرض يدوي',
                        "تم إضافة العرض: $title",
                        ['offer_id' => $insert_id, 'title' => $title]
                    );
                }
                
                // مسح النموذج بعد النجاح
                $_POST = [];
            } else {
                $message = "❌ فشل في إضافة العرض!";
            }
            
        } catch (Exception $e) {
            $message = "❌ خطأ في إضافة العرض: " . $e->getMessage();
        }
    }
}

// جلب إحصائيات العروض
$stats = [];
try {
    $conn = getDBConnection();
    
    $stats['total'] = $conn->query("SELECT COUNT(*) as count FROM offers")->fetch_assoc()['count'];
    $stats['active'] = $conn->query("SELECT COUNT(*) as count FROM offers WHERE is_active = 1")->fetch_assoc()['count'];
    $stats['manual'] = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id IS NULL OR external_id = ''")->fetch_assoc()['count'];
    $stats['api'] = $conn->query("SELECT COUNT(*) as count FROM offers WHERE external_id IS NOT NULL AND external_id != ''")->fetch_assoc()['count'];
    
    $conn->close();
} catch (Exception $e) {
    $stats['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عرض يدوي - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            font-family: inherit;
        }
        
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-group small {
            color: #666;
            font-size: 0.8rem;
            margin-top: 0.3rem;
            display: block;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .preview-section {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .preview-section h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .offer-preview {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .offer-preview img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .offer-preview h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .offer-preview p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .offer-preview .amount {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>➕ إضافة عرض يدوي</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!isset($stats['error'])): ?>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['active']; ?></div>
                <div class="stat-label">العروض النشطة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['manual']; ?></div>
                <div class="stat-label">العروض اليدوية</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['api']; ?></div>
                <div class="stat-label">عروض API</div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="form-section">
            <h3 style="margin-bottom: 1.5rem; color: #333;">📝 بيانات العرض الجديد</h3>
            
            <form method="POST">
                <div class="form-group">
                    <label for="title">عنوان العرض <span class="required">*</span></label>
                    <input type="text" id="title" name="title" required 
                           value="<?php echo isset($_POST['title']) ? htmlspecialchars($_POST['title']) : ''; ?>"
                           placeholder="مثال: احصل على 100$ مجاناً">
                    <small>عنوان جذاب ووصفي للعرض</small>
                </div>
                
                <div class="form-group">
                    <label for="description">وصف العرض</label>
                    <textarea id="description" name="description" 
                              placeholder="وصف مفصل للعرض وما يحصل عليه المستخدم..."><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                    <small>وصف مفصل يشرح العرض ومتطلباته</small>
                </div>
                
                <div class="form-group">
                    <label for="offer_url">رابط العرض <span class="required">*</span></label>
                    <input type="url" id="offer_url" name="offer_url" required 
                           value="<?php echo isset($_POST['offer_url']) ? htmlspecialchars($_POST['offer_url']) : ''; ?>"
                           placeholder="https://example.com/offer">
                    <small>الرابط الذي سيتم توجيه المستخدمين إليه</small>
                </div>
                
                <div class="form-group">
                    <label for="image_url">رابط صورة العرض</label>
                    <input type="url" id="image_url" name="image_url" 
                           value="<?php echo isset($_POST['image_url']) ? htmlspecialchars($_POST['image_url']) : ''; ?>"
                           placeholder="https://example.com/image.jpg">
                    <small>اتركه فارغاً لإنشاء صورة افتراضية تلقائياً</small>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="amount">المبلغ ($)</label>
                        <input type="number" id="amount" name="amount" step="0.01" min="0"
                               value="<?php echo isset($_POST['amount']) ? $_POST['amount'] : ''; ?>"
                               placeholder="0.00">
                        <small>مبلغ العمولة بالدولار</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="payout_type">نوع العمولة</label>
                        <select id="payout_type" name="payout_type">
                            <option value="CPI" <?php echo (isset($_POST['payout_type']) && $_POST['payout_type'] == 'CPI') ? 'selected' : ''; ?>>CPI - تكلفة التثبيت</option>
                            <option value="CPA" <?php echo (isset($_POST['payout_type']) && $_POST['payout_type'] == 'CPA') ? 'selected' : ''; ?>>CPA - تكلفة الإجراء</option>
                            <option value="CPL" <?php echo (isset($_POST['payout_type']) && $_POST['payout_type'] == 'CPL') ? 'selected' : ''; ?>>CPL - تكلفة العميل المحتمل</option>
                            <option value="CPS" <?php echo (isset($_POST['payout_type']) && $_POST['payout_type'] == 'CPS') ? 'selected' : ''; ?>>CPS - تكلفة البيع</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="countries">البلدان المدعومة</label>
                        <input type="text" id="countries" name="countries" 
                               value="<?php echo isset($_POST['countries']) ? htmlspecialchars($_POST['countries']) : ''; ?>"
                               placeholder="US,UK,CA,AU أو اتركه فارغاً لجميع البلدان">
                        <small>رموز البلدان مفصولة بفواصل، أو فارغ لجميع البلدان</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="device">الجهاز المدعوم</label>
                        <select id="device" name="device">
                            <option value="all" <?php echo (isset($_POST['device']) && $_POST['device'] == 'all') ? 'selected' : ''; ?>>جميع الأجهزة</option>
                            <option value="mobile" <?php echo (isset($_POST['device']) && $_POST['device'] == 'mobile') ? 'selected' : ''; ?>>الهاتف المحمول</option>
                            <option value="desktop" <?php echo (isset($_POST['device']) && $_POST['device'] == 'desktop') ? 'selected' : ''; ?>>سطح المكتب</option>
                            <option value="tablet" <?php echo (isset($_POST['device']) && $_POST['device'] == 'tablet') ? 'selected' : ''; ?>>الجهاز اللوحي</option>
                            <option value="android" <?php echo (isset($_POST['device']) && $_POST['device'] == 'android') ? 'selected' : ''; ?>>أندرويد</option>
                            <option value="ios" <?php echo (isset($_POST['device']) && $_POST['device'] == 'ios') ? 'selected' : ''; ?>>iOS</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" name="add_offer" class="btn btn-success">
                    ➕ إضافة العرض
                </button>
            </form>
        </div>
        
        <div class="preview-section">
            <h3>👁️ معاينة العرض</h3>
            <div class="offer-preview">
                <img src="https://via.placeholder.com/400x250/667eea/ffffff?text=معاينة+العرض" alt="معاينة العرض">
                <h4>عنوان العرض سيظهر هنا</h4>
                <p>وصف العرض سيظهر هنا...</p>
                <span class="amount">$0.00</span>
            </div>
            <small style="color: #666; margin-top: 1rem; display: block;">
                هذه معاينة تقريبية لكيفية ظهور العرض في الصفحة الرئيسية
            </small>
        </div>
        
        <div class="nav-links">
            <a href="manage_offers.php">📋 إدارة العروض</a>
            <a href="featured_offers.php">⭐ العروض المميزة</a>
            <a href="comprehensive_sync.php">🌟 المزامنة الشاملة</a>
            <a href="api_manager.php">📡 إدارة API</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
    
    <script>
        // معاينة مباشرة للعرض
        function updatePreview() {
            const title = document.getElementById('title').value || 'عنوان العرض سيظهر هنا';
            const description = document.getElementById('description').value || 'وصف العرض سيظهر هنا...';
            const amount = document.getElementById('amount').value || '0.00';
            const imageUrl = document.getElementById('image_url').value;
            
            document.querySelector('.offer-preview h4').textContent = title;
            document.querySelector('.offer-preview p').textContent = description.substring(0, 100) + (description.length > 100 ? '...' : '');
            document.querySelector('.offer-preview .amount').textContent = '$' + parseFloat(amount).toFixed(2);
            
            if (imageUrl) {
                document.querySelector('.offer-preview img').src = imageUrl;
            } else {
                const encodedTitle = encodeURIComponent(title.substring(0, 30));
                document.querySelector('.offer-preview img').src = `https://via.placeholder.com/400x250/667eea/ffffff?text=${encodedTitle}`;
            }
        }
        
        // ربط الأحداث
        document.getElementById('title').addEventListener('input', updatePreview);
        document.getElementById('description').addEventListener('input', updatePreview);
        document.getElementById('amount').addEventListener('input', updatePreview);
        document.getElementById('image_url').addEventListener('input', updatePreview);
        
        // تحديث المعاينة عند تحميل الصفحة
        updatePreview();
    </script>
</body>
</html>
