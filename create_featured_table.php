<?php
require_once 'config.php';

// التحقق من كلمة المرور
checkPassword();

$message = '';
$success = false;

// إنشاء جدول العروض المميزة
if (isset($_POST['create_table']) || isset($_GET['auto'])) {
    try {
        $conn = getDBConnection();
        
        // التحقق من وجود الجدول
        $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
        
        if ($table_check->num_rows > 0) {
            $message = "⚠️ جدول العروض المميزة موجود بالفعل!";
        } else {
            // إنشاء جدول العروض المميزة
            $create_table = "
                CREATE TABLE featured_offers (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    offer_id INT NOT NULL,
                    position INT NOT NULL DEFAULT 1,
                    is_active TINYINT(1) DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_offer (offer_id),
                    INDEX idx_position_active (position, is_active)
                )
            ";
            
            if ($conn->query($create_table)) {
                $success = true;
                $message = "✅ تم إنشاء جدول العروض المميزة بنجاح!";
            } else {
                $message = "❌ فشل في إنشاء الجدول: " . $conn->error;
            }
        }
        
        $conn->close();
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
    }
}

// فحص حالة الجدول
$table_status = [];
try {
    $conn = getDBConnection();
    
    // التحقق من وجود الجدول
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
    $table_status['exists'] = $table_check->num_rows > 0;
    
    if ($table_status['exists']) {
        // فحص هيكل الجدول
        $columns = $conn->query("SHOW COLUMNS FROM featured_offers");
        $table_status['columns'] = [];
        while ($column = $columns->fetch_assoc()) {
            $table_status['columns'][] = $column;
        }
        
        // عدد العروض المميزة
        $count = $conn->query("SELECT COUNT(*) as count FROM featured_offers WHERE is_active = 1")->fetch_assoc();
        $table_status['featured_count'] = $count['count'];
        
        // فحص المفاتيح الخارجية
        $foreign_keys = $conn->query("
            SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_NAME = 'featured_offers' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $table_status['foreign_keys'] = [];
        while ($fk = $foreign_keys->fetch_assoc()) {
            $table_status['foreign_keys'][] = $fk;
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    $table_status['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول العروض المميزة - موقع CPA</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .message.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .status-section h3 {
            color: #333;
            margin-bottom: 1rem;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            margin: 0.5rem 0;
            background: white;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }
        
        .status-label {
            font-weight: bold;
            color: #333;
        }
        
        .status-value {
            color: #666;
        }
        
        .status-value.success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-value.error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .columns-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: white;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .columns-table th,
        .columns-table td {
            padding: 0.8rem;
            text-align: right;
            border-bottom: 1px solid #eee;
        }
        
        .columns-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: bold;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .action-section {
            text-align: center;
            margin: 2rem 0;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .nav-links a {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            margin: 0.5rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        
        .info-box {
            background: #e3f2fd;
            padding: 1.5rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border-left: 4px solid #2196f3;
        }
        
        .info-box h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            margin-right: 1.5rem;
            color: #1565c0;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗃️ إنشاء جدول العروض المميزة</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo $success ? 'success' : (strpos($message, '⚠️') !== false ? 'warning' : 'error'); ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📋 حول جدول العروض المميزة:</h3>
            <ul>
                <li><strong>الغرض:</strong> تخزين العروض المختارة لتظهر في أعلى الصفحة الرئيسية</li>
                <li><strong>الهيكل:</strong> يحتوي على معرف العرض والترتيب وحالة التفعيل</li>
                <li><strong>العلاقات:</strong> مرتبط بجدول العروض الأساسي</li>
                <li><strong>الفهارس:</strong> محسن للبحث السريع والترتيب</li>
            </ul>
        </div>
        
        <!-- حالة الجدول -->
        <div class="status-section">
            <h3>📊 حالة جدول العروض المميزة</h3>
            
            <?php if (isset($table_status['error'])): ?>
                <div class="status-item">
                    <span class="status-label">❌ خطأ:</span>
                    <span class="status-value error"><?php echo htmlspecialchars($table_status['error']); ?></span>
                </div>
            <?php else: ?>
                <div class="status-item">
                    <span class="status-label">📋 حالة الجدول:</span>
                    <span class="status-value <?php echo $table_status['exists'] ? 'success' : 'error'; ?>">
                        <?php echo $table_status['exists'] ? '✅ موجود' : '❌ غير موجود'; ?>
                    </span>
                </div>
                
                <?php if ($table_status['exists']): ?>
                    <div class="status-item">
                        <span class="status-label">📊 عدد الأعمدة:</span>
                        <span class="status-value"><?php echo count($table_status['columns']); ?> عمود</span>
                    </div>
                    
                    <div class="status-item">
                        <span class="status-label">⭐ العروض المميزة النشطة:</span>
                        <span class="status-value success"><?php echo $table_status['featured_count']; ?> عرض</span>
                    </div>
                    
                    <div class="status-item">
                        <span class="status-label">🔗 المفاتيح الخارجية:</span>
                        <span class="status-value"><?php echo count($table_status['foreign_keys']); ?> مفتاح</span>
                    </div>
                    
                    <!-- تفاصيل الأعمدة -->
                    <h4 style="margin-top: 1.5rem; color: #333;">📋 هيكل الجدول:</h4>
                    <table class="columns-table">
                        <thead>
                            <tr>
                                <th>اسم العمود</th>
                                <th>النوع</th>
                                <th>Null</th>
                                <th>المفتاح</th>
                                <th>القيمة الافتراضية</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($table_status['columns'] as $column): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($column['Field']); ?></td>
                                    <td><?php echo htmlspecialchars($column['Type']); ?></td>
                                    <td><?php echo $column['Null']; ?></td>
                                    <td><?php echo htmlspecialchars($column['Key']); ?></td>
                                    <td><?php echo htmlspecialchars($column['Default'] ?? 'NULL'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- إجراءات -->
        <div class="action-section">
            <?php if (!$table_status['exists']): ?>
                <h3 style="color: #dc3545; margin-bottom: 1rem;">⚠️ الجدول غير موجود</h3>
                <p style="color: #666; margin-bottom: 2rem;">يجب إنشاء جدول العروض المميزة لتتمكن من استخدام هذه الميزة.</p>
                
                <form method="POST">
                    <button type="submit" name="create_table" class="btn btn-success">
                        🗃️ إنشاء جدول العروض المميزة
                    </button>
                </form>
            <?php else: ?>
                <h3 style="color: #28a745; margin-bottom: 1rem;">✅ الجدول جاهز للاستخدام</h3>
                <p style="color: #666; margin-bottom: 2rem;">يمكنك الآن إدارة العروض المميزة بشكل طبيعي.</p>
                
                <a href="featured_offers.php" class="btn btn-success">
                    ⭐ إدارة العروض المميزة
                </a>
            <?php endif; ?>
        </div>
        
        <div class="nav-links">
            <a href="featured_offers.php">⭐ العروض المميزة</a>
            <a href="manage_offers.php">📋 إدارة العروض</a>
            <a href="api_manager.php">📡 لوحة التحكم</a>
            <a href="index.php">🏠 الصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
