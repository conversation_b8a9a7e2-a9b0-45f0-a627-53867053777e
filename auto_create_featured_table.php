<?php
require_once 'config.php';

// إنشاء جدول العروض المميزة تلقائياً
try {
    $conn = getDBConnection();
    
    // التحقق من وجود الجدول
    $table_check = $conn->query("SHOW TABLES LIKE 'featured_offers'");
    
    if ($table_check->num_rows == 0) {
        // إنشاء جدول العروض المميزة
        $create_table = "
            CREATE TABLE featured_offers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                offer_id INT NOT NULL,
                position INT NOT NULL DEFAULT 1,
                is_active TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (offer_id) REFERENCES offers(id) ON DELETE CASCADE,
                UNIQUE KEY unique_offer (offer_id),
                INDEX idx_position_active (position, is_active)
            )
        ";
        
        if ($conn->query($create_table)) {
            echo "✅ تم إنشاء جدول العروض المميزة بنجاح!";
        } else {
            echo "❌ فشل في إنشاء الجدول: " . $conn->error;
        }
    } else {
        echo "⚠️ جدول العروض المميزة موجود بالفعل!";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
}

// إعادة توجيه للصفحة المطلوبة
$redirect = $_GET['redirect'] ?? 'featured_offers.php';
header("Location: $redirect");
exit;
?>
